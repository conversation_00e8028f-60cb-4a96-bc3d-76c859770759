<?php
$syaratkhas_f3_2  = DB::connection('emas')->select("SELECT * FROM 
(   
    SELECT
    a.PROGRAMKOD AS PROGRAMKOD,
    CASE WHEN SUBSTR(PROGRAMKOD, -3, 1) IN ('1', '2', '3', '4') THEN SUBSTR(PROGRAMKOD, -3, 1) ELSE 'X' END AS ALIRAN,
    a.<PERSON> AS GKUMPULAN,
    b.<PERSON> AS KODSUBJEK_1,
    GROUP_CONCAT(d.MATA<PERSON><PERSON>AJARAN ORDER BY b.ORDERID ASC SEPARATOR ' / ') AS KODSUBJEK_2,
    a.MINGRED AS MINGRED,
    a.KUMPULA<PERSON> AS KUMPULAN,
    a.SUB_KUMPULAN AS SUB_KUMPULAN,
    CASE
        WHEN a.JUMLAH_MIN_SUBJEK = '1'
        THEN 'SATU'
        WHEN a.JUMLAH_MIN_SUBJEK = '2'
        THEN 'DUA'
        WHEN a.JUMLAH_MIN_SUBJEK = '3'
        THEN 'TIGA'
        WHEN a.JUMLAH_MIN_SUBJEK = '4'
        THEN 'EMPAT'
        WHEN a.JUMLAH_MIN_SUBJEK = '5'
        THEN 'LIMA'
        WHEN a.JUMLAH_MIN_SUBJEK = '6'
        THEN 'ENAM'
        WHEN a.JUMLAH_MIN_SUBJEK = '7'
        THEN 'TUJUH'
        WHEN a.JUMLAH_MIN_SUBJEK = '8'
        THEN 'LAPAN'
        ELSE 'SEMBILAN'
    END AS KET_JUMLAH_MIN_SUBJEK,
    a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
    a.SESI AS SESI,
    a.ORDERID AS ORDERID,
    b.ORDERID AS ORDERID2
    FROM syarat_khas_stpm a
    LEFT JOIN syarat_xsub_kumpulan_subjek_stpm b ON (a.KODSUBJEK = b.KUMPULAN AND a.SESI = b.SESI)
    LEFT JOIN syarat_kesetaraan_stpm c ON (c.PARENT_KUMPULAN_KOD = b.KODSUBJEK AND a.SESI = b.SESI)
    LEFT JOIN upuplus_all_subjek d ON (c.KOD = d.KOD)
    WHERE a.KUMPULAN = 'F3'
    AND a.SUB_KUMPULAN = 'F3'
    AND a.KODSUBJEK LIKE 'F3%'
    AND b.KODSUBJEK LIKE 'K-%'
    AND a.SESI = '$sessionSesi'
    GROUP BY a.PROGRAMKOD

    UNION ALL

    SELECT
    temp.PROGRAMKOD AS PROGRAMKOD,
    temp.ALIRAN AS ALIRAN,
    temp.GKUMPULAN AS GKUMPULAN,
    temp.KODSUBJEK_1 AS KODSUBJEK_1,
    temp.KODSUBJEK_2 AS KODSUBJEK_2,
    temp.MINGRED AS MINGRED,
    temp.KUMPULAN AS KUMPULAN,
    temp.SUB_KUMPULAN AS SUB_KUMPULAN,
    temp.KET_JUMLAH_MIN_SUBJEK AS KET_JUMLAH_MIN_SUBJEK,
    temp.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
    temp.SESI AS SESI,
    temp.ORDERID AS ORDERID,
    temp.ORDERID2 AS ORDERID2
    FROM
    (SELECT
        a.PROGRAMKOD AS PROGRAMKOD,
        CASE WHEN SUBSTR(PROGRAMKOD, -3, 1) IN ('1', '2', '3', '4') THEN SUBSTR(PROGRAMKOD, -3, 1) ELSE 'X' END AS ALIRAN,
        a.KODSUBJEK AS GKUMPULAN,
        b.KODSUBJEK AS KODSUBJEK_1,
        d.MATAPELAJARAN AS KODSUBJEK_2,
        a.MINGRED AS MINGRED,
        a.KUMPULAN AS KUMPULAN,
        a.SUB_KUMPULAN AS SUB_KUMPULAN,
        CASE
            WHEN a.JUMLAH_MIN_SUBJEK = '1'
            THEN 'SATU'
            WHEN a.JUMLAH_MIN_SUBJEK = '2'
            THEN 'DUA'
            WHEN a.JUMLAH_MIN_SUBJEK = '3'
            THEN 'TIGA'
            WHEN a.JUMLAH_MIN_SUBJEK = '4'
            THEN 'EMPAT'
            WHEN a.JUMLAH_MIN_SUBJEK = '5'
            THEN 'LIMA'
            WHEN a.JUMLAH_MIN_SUBJEK = '6'
            THEN 'ENAM'
            WHEN a.JUMLAH_MIN_SUBJEK = '7'
            THEN 'TUJUH'
            WHEN a.JUMLAH_MIN_SUBJEK = '8'
            THEN 'LAPAN'
            ELSE 'SEMBILAN'
        END AS KET_JUMLAH_MIN_SUBJEK,
        a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
        a.SESI AS SESI,
        a.ORDERID AS ORDERID,
        b.ORDERID AS ORDERID2
    FROM syarat_khas_stpm a
    LEFT JOIN syarat_xsub_kumpulan_subjek_stpm b ON (a.KODSUBJEK = b.KUMPULAN AND a.SESI = b.SESI)
    LEFT JOIN upuplus_all_subjek d ON (b.KODSUBJEK = d.KOD)
    WHERE a.KUMPULAN = 'F3'
        AND a.SUB_KUMPULAN = 'F3'
        AND a.KODSUBJEK LIKE 'F3%'
        AND b.KODSUBJEK NOT LIKE 'K-%'
        AND a.SESI = '$sessionSesi') temp
    ORDER BY ORDERID2
) AS temp 
    WHERE PROGRAMKOD LIKE '$PROGRAM->kod_Program%' 
    AND PROGRAMKOD LIKE '%$PROGRAM->kategori_Pengajian)'
    AND SUBSTR(PROGRAMKOD, -3, 1) = '2'
    GROUP BY SUBSTR(PROGRAMKOD, 1, 9),SUBSTR(PROGRAMKOD, -2, 1), KODSUBJEK_1");