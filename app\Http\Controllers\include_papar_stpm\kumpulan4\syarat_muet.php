<?php
    $syarat_muet_4  = DB::connection('emas')->select("SELECT * FROM
    (
            SELECT
            Programkod AS Programkod,
            (CASE 
                WHEN (SUBSTR(Programkod,-(3),1) = '1') THEN '1' 
                WHEN (SUBSTR(Programkod,-(3),1) = '2') THEN '2' 
                WHEN (SUBSTR(Programkod,-(3),1) = '3') THEN '3' 
                WHEN (SUBSTR(Programkod,-(3),1) = '4') THEN '4' 
                ELSE 'X' 
            END) AS Aliran,
            Kategori AS Kategori,
            Syarat_taraf_perkahwinan AS Syarat_taraf_perkahwinan,
            Taraf_perkahwinan AS Taraf_perkahwinan,
            Syarat_Umur AS Syarat_Umur,
            Operasi_Umur AS Operasi_Umur,
            Umur1 AS Umur1,
            Umur2 AS Umur2,
            Syarat_Kecacatan AS Syarat_Kecacatan,
            Syarat_PNGK_STPM AS Syarat_PNGK_STPM,
            PNGK_STPM AS PNGK_STPM,
            Syarat_PNGK_Diploma AS Syarat_PNGK_Diploma,
            PNGK_Diploma AS PNGK_Diploma,
            Syarat_MUET AS Syarat_MUET,
            MUET1_BAND AS MUET1_BAND,
            MUET1_Tahun AS MUET1_Tahun,
            MUET2_Band AS MUET2_Band,
            MUET2_Tahun AS MUET2_Tahun,
            Syarat_PNGK_Sekolah_Sukan AS Syarat_PNGK_Sekolah_Sukan,
            PNGK_STPM_Sekolah_Sukan AS PNGK_STPM_Sekolah_Sukan,
            Syarat_Jantina AS Syarat_Jantina,
            Jantina AS Jantina,
            Lulus_Ujian_Medsi AS Lulus_Ujian_Medsi,
            Lulus_Ujian_Temuduga AS Lulus_Ujian_Temuduga,
            Lulus_APEL AS Lulus_APEL,
            bulan1 AS bulan1,
            bulan2 AS bulan2,
            Syarat_PNGK_MATRIK AS Syarat_PNGK_MATRIK,
            PNGK_MATRIK AS PNGK_MATRIK,
            Syarat_MUET_OR AS Syarat_MUET_OR,
            syarat_tahap_STAM AS syarat_tahap_STAM,
            tahap_STAM AS tahap_STAM,
            Syarat_1119 AS Syarat_1119,
            gred_1119 AS gred_1119,
            sesi AS sesi,
            umur_bulan1 AS umur_bulan1,
            umur_bulan2 AS umur_bulan2,
            syarat_3M AS syarat_3M,
            updated_at AS updated_at
        FROM syarat_lain
        WHERE Kategori <> 'SPM'
        AND sesi = '$sessionSesi' 
    ) AS temp 
        WHERE SUBSTR(Programkod, 1, 9) = '$PROGRAM->kod_Program'
        AND SUBSTR(Programkod, -2, 1) = '$PROGRAM->kategori_Pengajian'
        AND SUBSTR(Programkod, -3, 1) = '4'
        AND Syarat_MUET = '1'
        GROUP BY SUBSTR(Programkod,1,9), Kategori, MUET1_BAND, MUET2_Band
        ORDER BY Programkod");
