<style>
    select.form-control:focus { box-shadow: none!important; }
    input:focus { box-shadow: none!important; }
	
	.tbody-dark {
		color: #fff;
		background-color: #5a5c69;
		border-color: #6c6e7e;
	}
    
 
    .select2-container .select2-selection--single { border: none; }
    .select2 { width: 100% !important; }
    .select2-container--default .select2-results>.select2-results__options{ max-height: 400px; font-size: .875rem; } 
    .select2-container--default .select2-selection--single .select2-selection__rendered  { color: black; font-weight: normal; }
    .select2-container--default .select2-selection--single .select2-selection__arrow { height: 30px; }
    .select2-container .select2-selection--single .select2-selection__rendered {white-space: break-spaces;}
    .select2-container .select2-selection--single { height: 100%; }
    .select2-container--bootstrap .select2-selection{ border-radius: 0px; }
    
    .select2-container--default .select2-results__option[aria-disabled=true] { display: none;}

</style>
    
    
    <div class="row" style="font-size:.875rem;">
    
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">

            <div id="header_merit" class="mb-2" align="center" style="border:1px solid #e3e6f0; border-radius:5px; background:#bcc9e0;">
                <div class="mt-2 mb-1" style="font-size:16px; font-weight:bold;">Markah Merit Anda :</div>
                <div id="merit" style="font-size:36px; font-weight:bold;"></div>
                <div id="pakej" class="mb-2" style="font-size:16px; font-weight:bold;"></div>
            </div>  

            <table class="table table-bordered table-sm" width="100%" cellspacing="0">           
                <thead class="thead-dark">
                    <tr>
                        <th>Mata Pelajaran Universal</th>
                        <th width="130px" rowspan="2" style="vertical-align: middle;">Gred</th>
                    </tr>
                </thead>
            
                <tbody style="color: #000;">
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="SUBJEK01" id="SUBJEK01" class="form-control form-control-sm" style="border: none; font-weight:bold;">
                                @foreach($subjek_BM as $spm1)
                                    <option value="{{$spm1->kodsubjekspm}}">{{$spm1->ketsubjekspm}}</option>
                                @endforeach
                            </select>  
                          </td>
                        <td>
                            <select name="GRDGRED1" id="GRDGRED1" class="form-control form-control-sm" style="border: none;">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                            {{-- <small class="text-danger" id="grd01" style="margin-left: 0.8rem!important;"></small> --}}
                        </td>
                    </tr>
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="SUBJEK02" id="SUBJEK02" class="form-control form-control-sm" style="border: none; font-weight:bold;">
                                @foreach($subjek_BI as $spm2)
                                    <option value="{{$spm2->kodsubjekspm}}">{{$spm2->ketsubjekspm}}</option>
                                @endforeach
                            </select>   
                         </td>
                        <td>
                            <select name="GRDGRED2" id="GRDGRED2" class="form-control form-control-sm" style="border: none;">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>
                    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="SUBJEK03" id="SUBJEK03" class="form-control form-control-sm" style="border: none; font-weight:bold;">
                                @foreach($subjek_MATE as $spm3)
                                    <option value="{{$spm3->kodsubjekspm}}">{{$spm3->ketsubjekspm}}</option>
                                @endforeach
                            </select>  
                        </td>
                        <td>
                            <select name="GRDGRED3" id="GRDGRED3" class="form-control form-control-sm" style="border: none;">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="SUBJEK04" id="SUBJEK04" class="form-control form-control-sm" style="border: none; font-weight:bold;">
                                @foreach($subjek_SEJ as $spm4)
                                    <option value="{{$spm4->kodsubjekspm}}">{{$spm4->ketsubjekspm}}</option>
                                @endforeach
                            </select> 
                        </td>
                        <td>
                            <select name="GRDGRED4" id="GRDGRED4" class="form-control form-control-sm" style="border: none;">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>

                </tbody>
            
            </table>

    
            <div class="row form-group{{ $errors->has('ALIRAN') ? ' has-error' : '' }} mt-3">                    
                <div class="col-xl-4">
                    {!! Html::decode(Form::label('ALIRAN', 'SILA PILIH JENIS ALIRAN : ',['class'=>'form-label form label-sm', 'style'=>'white-space: nowrap;'])) !!}
                </div>                     
                <div class="col-xl-6">                         
                    <div class="input-group input-group-sm">
                        <select name="ALIRAN" id="ALIRAN" class="form-control form-control-sm mt-n1">
                            {{-- <option value=""selected="selected">-- Sila pilih  --</option> --}}
                            <option value="STEMA" selected="selected">STEM A</option>
                            <option value="STEMB">STEM B</option>
                            <option value="STEMC">STEM C</option>
                            <option value="KSI">KEMANUSIAAN DAN SASTERA IKHTISAS / LAIN-LAIN</option>
                        </select>                         
                    </div>                       
                </div>                 
            </div>
            

            {{-- ************************************************************************************************************************************** --}}          
    
            <table class="table table-bordered table-sm" width="100%" cellspacing="0">
                <thead class="thead-dark">
                    <tr>
                        <th>Mata Pelajaran Pakej Terbaik</th>
                        <th width="130px">Gred</th>
                    </tr>
                </thead>
            
                <tbody style="color: #000;">
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">                      
                            <select name="MPP01" id="MPP01" class="form-control form-control-sm" style="border: none;" onchange="hidespm1()">
                                <option value="" stem-type="info" selected>SILA PILIH SUBJEK</option>  
                                    @foreach($subjek_stem as $mpp01)
                                        <option value="{{$mpp01->kodsubjekspm}}" stem-type="{{$mpp01->pakej}}">{{$mpp01->ketsubjekspm}}</option>
                                    @endforeach
                            </select>
                            <small class="text-danger" id="spm01" style="margin-left: 0.8rem!important;"></small>
                        </td>
    
                        <td>
                            <select name="GRDGRED5" id="GRDGRED5" class="form-control form-control-sm" style="border: none;" onchange="hidespm1()">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>
    
                    <tr id="stem-c">
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="MPP02" id="MPP02" class="form-control form-control-sm" style="border: none;" onchange="hidespm2()">
                                <option value="" stem-type="info" selected>SILA PILIH SUBJEK</option>  
                                    @foreach($subjek_stem as $mpp02)
                                        <option value="{{$mpp02->kodsubjekspm}}" stem-type="{{$mpp02->pakej}}">{{$mpp02->ketsubjekspm}}</option>
                                    @endforeach
                            </select>
                            <small class="text-danger" id="spm02" style="margin-left: 0.8rem!important;"></small>
                        </td>
    
                        <td>
                            <select name="GRDGRED6" id="GRDGRED6" class="form-control form-control-sm" style="border: none;" onchange="hidespm2()">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select>                      
                        </td>
                    </tr>
                </tbody>
            </table>

            
    
            {{-- ************************************************************************************************************************************** --}}

            
            <table class="table table-bordered table-sm" width="100%" cellspacing="0">
               
                <thead class="thead-dark">
                    <tr>
                        <th>Mata Pelajaran Terbaik (Selain Mata Pelajaran Di Atas)</th>
                        <th width="130px">Gred</th>
                    </tr>
                </thead>
            
                <tbody style="color: #000;">
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="MPT01" id="MPT01" class="form-control form-control-sm progControlSelect2" onchange="hidespm3()">
                                <option value="" selected>SILA PILIH SUBJEK</option>  
                                    @foreach($subjek_spm_kssm as $subjekspm)
                                        <option value="{{$subjekspm->kodsubjekspm}}" stem-type="{{$subjekspm->pakej}}">{{$subjekspm->ketsubjekspm}}</option>
                                    @endforeach
                            </select>
                            <small class="text-danger" id="spm03" style="margin-left: 0.5rem!important;"></small>
                        </td>
    
                        <td>
                            <select name="GRDGRED7" id="GRDGRED7" class="form-control form-control-sm" style="border: none;" onchange="hidespm3()">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="MPT02" id="MPT02" class="form-control form-control-sm progControlSelect2" onchange="hidespm4()">
                                <option value="" selected>SILA PILIH SUBJEK</option>  
                                    @foreach($subjek_spm_kssm as $subjekspm)
                                        <option value="{{$subjekspm->kodsubjekspm}}" stem-type="{{$subjekspm->pakej}}">{{$subjekspm->ketsubjekspm}}</option>
                                    @endforeach
                            </select>
                            <small class="text-danger" id="spm04" style="margin-left: 0.5rem!important;"></small>
                        </td>
    
                        <td>
                            <select name="GRDGRED8" id="GRDGRED8" class="form-control form-control-sm" style="border: none;" onchange="hidespm4()">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>
                </tbody>
            
            </table>
    
            {{-- ************************************************************************************************************************************** --}}

            <table class="table table-bordered table-sm" width="100%" cellspacing="0">        
                <tbody  style="color: #000;">
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;" class="tbody-dark">Markah Ko-Kurikulum (10%)</td>
                        <td width="130px">
                            <select name="MRKKOKO" id="MRKKOKO" class="form-control form-control-sm subjekspm">
                                @for ($i = 10.00; $i >= 0; $i -= 0.01)
                                    <option value="{{ number_format($i,2)}}">{{ number_format($i,2) }}</option>
                                @endfor
                            </select>
                        </td>
                    </tr>
                </tbody>
            </table>
 
        </div> 
    </div>
 
    