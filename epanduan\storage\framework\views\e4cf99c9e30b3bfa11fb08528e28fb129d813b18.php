

<?php $__currentLoopData = $j<PERSON><PERSON><PERSON><PERSON><PERSON>; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $jpengajian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

    <?php
        $syaratam_am  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    ?>


    <div class="pl-2 pb-3" style="line-height:1.8rem; float:left; width: 90%; margin-top:-1px; margin-bottom: -35px;">

        <ol style="padding-left: 1.5em;">

            <?php if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN): ?>

                <?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($syaratam->KODSUBJEK=='W'): ?>
                    <li>Warganegara Malaysia yang mempunyai <b>No. Kad Pengenalan / My Kad</b>.</li>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

				<?php if((substr($PROGRAM->kod_Program,0,2) =='FB' || substr($PROGRAM->kod_Program,0,2) =='FC') && $jpengajian->KOD_PENGAJIAN =='A'): ?>
					<li>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</li>

				<?php elseif((substr($PROGRAM->kod_Program,0,2) =='FB' || substr($PROGRAM->kod_Program,0,2) =='FC') && $jpengajian->KOD_PENGAJIAN =='D2'): ?>
					<li>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</li>


				<?php elseif((substr($PROGRAM->kod_Program,0,2) != 'FB' && substr($PROGRAM->kod_Program,0,2) != 'FC' && substr($PROGRAM->kod_Program,0,1) != 'U') && ($jpengajian->KOD_PENGAJIAN =='A')): ?>
					<li>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</li>

				<?php elseif((substr($PROGRAM->kod_Program,0,2) != 'FB' && substr($PROGRAM->kod_Program,0,2) != 'FC' && substr($PROGRAM->kod_Program,0,1) != 'U') && ($jpengajian->KOD_PENGAJIAN =='D2')): ?>
					<li>Calon mestilah <b>memiliki Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya <b>lulus dalam mata pelajaran Bahasa Melayu DAN Sejarah</b>.</li>

				<?php else: ?>
					<li>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</li>

				<?php endif; ?>


            <?php endif; ?>

            <?php if($jpengajian->KOD_PENGAJIAN=='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN): ?>

                <?php if(substr($PROGRAM->kod_Program,0,2) =='FC'): ?>
                    <?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($syaratam->KODSUBJEK=='W'): ?>
                        <li>Warganegara Malaysia yang mempunyai <b>No. Kad Pengenalan / My Kad</b>.</li>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <li>Pemohon mestilah menduduki <b>Sijil Pelajaran Malaysia (SPM)</b> dan boleh membaca, menulis dan mengira. </li>

                <?php endif; ?>

                <?php if(substr($PROGRAM->kod_Program,0,2) !='FC'): ?>
                    <?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($syaratam->KODSUBJEK=='W'): ?>
                        <li>Warganegara Malaysia yang mempunyai <b>No. Kad Pengenalan / My Kad</b>.</li>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <li>Pemohon mestilah menduduki <b>Sijil Pelajaran Malaysia (SPM)</b> dan boleh membaca, menulis dan mengira dalam Bahasa Melayu ATAU Bahasa Inggeris. </li>

                <?php endif; ?>


            <?php endif; ?>


				<?php if((substr($PROGRAM->kod_Program,0,2) =='FB' || substr($PROGRAM->kod_Program,0,2) =='FC') && $jpengajian->KOD_PENGAJIAN =='A'): ?>
					<ol type="i" style="padding-left: 1.5em;">
						<?php if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN): ?>
							<?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
								<?php if($syaratam->KODSUBJEK!='F' &&  $syaratam->KODSUBJEK!='W' &&  $syaratam->KODSUBJEK!='T'): ?>
									<li style="padding-left: .3em;">
										Gred <b><?php echo e($syaratam->MINGRED); ?></b> bagi matapelajaran
										<?php $__currentLoopData = $subjek; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spm_subjek): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
											<?php if($spm_subjek->kodsubjekspm==$syaratam->KODSUBJEK): ?>
												<b><?php echo e($spm_subjek->ketsubjekspm); ?></b>.
											<?php endif; ?>
										<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
									</li>
								<?php endif; ?>

								<?php
								if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
								?>


								<?php if($syaratam->KODSUBJEK=='F'): ?>
									<li style="padding-left: .3em;"><b><?php echo e($read); ?> (<?php echo e($syaratam->JUMLAH_MIN_SUBJEK); ?>) <?php echo e($syaratam->MINGRED); ?></b> dalam mana-mana matapelajaran yang belum dikira.</li>
								<?php endif; ?>
							<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
						<?php endif; ?>
					</ol>

				<?php elseif((substr($PROGRAM->kod_Program,0,2) =='FB' || substr($PROGRAM->kod_Program,0,2) =='FC') && $jpengajian->KOD_PENGAJIAN =='D2'): ?>
					<ol type="i" style="padding-left: 1.5em;">
						<?php if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN): ?>
							<?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
								<?php if($syaratam->KODSUBJEK!='F' &&  $syaratam->KODSUBJEK!='W' &&  $syaratam->KODSUBJEK!='T'): ?>
									<li style="padding-left: .3em;">
										Gred <b><?php echo e($syaratam->MINGRED); ?></b> bagi matapelajaran
										<?php $__currentLoopData = $subjek; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spm_subjek): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
											<?php if($spm_subjek->kodsubjekspm==$syaratam->KODSUBJEK): ?>
												<b><?php echo e($spm_subjek->ketsubjekspm); ?></b>.
											<?php endif; ?>
										<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
									</li>
								<?php endif; ?>

								<?php
								if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
								?>


								<?php if($syaratam->KODSUBJEK=='F'): ?>
									<li style="padding-left: .3em;"><b><?php echo e($read); ?> (<?php echo e($syaratam->JUMLAH_MIN_SUBJEK); ?>) <?php echo e($syaratam->MINGRED); ?></b> dalam mana-mana matapelajaran yang belum dikira.</li>
								<?php endif; ?>
							<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
						<?php endif; ?>
					</ol>

				<?php elseif((substr($PROGRAM->kod_Program,0,2) != 'FB' && substr($PROGRAM->kod_Program,0,2) != 'FC' && substr($PROGRAM->kod_Program,0,1) != 'U') && ($jpengajian->KOD_PENGAJIAN =='A')): ?>
					<ol type="i" style="padding-left: 1.5em;">
						<?php if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN): ?>
							<?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
								<?php if($syaratam->KODSUBJEK!='F' &&  $syaratam->KODSUBJEK!='W' &&  $syaratam->KODSUBJEK!='T'): ?>
									<li style="padding-left: .3em;">
										Gred <b><?php echo e($syaratam->MINGRED); ?></b> bagi matapelajaran
										<?php $__currentLoopData = $subjek; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spm_subjek): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
											<?php if($spm_subjek->kodsubjekspm==$syaratam->KODSUBJEK): ?>
												<b><?php echo e($spm_subjek->ketsubjekspm); ?></b>.
											<?php endif; ?>
										<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
									</li>
								<?php endif; ?>

								<?php
								if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
								?>


								<?php if($syaratam->KODSUBJEK=='F'): ?>
									<li style="padding-left: .3em;"><b><?php echo e($read); ?> (<?php echo e($syaratam->JUMLAH_MIN_SUBJEK); ?>) <?php echo e($syaratam->MINGRED); ?></b> dalam mana-mana matapelajaran yang belum dikira.</li>
								<?php endif; ?>
							<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
						<?php endif; ?>
					</ol>
				<?php elseif((substr($PROGRAM->kod_Program,0,2) != 'FB' && substr($PROGRAM->kod_Program,0,2) != 'FC' && substr($PROGRAM->kod_Program,0,1) != 'U') && ($jpengajian->KOD_PENGAJIAN =='D2')): ?>

				<?php else: ?>
					<ol type="i" style="padding-left: 1.5em;">
						<?php if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN): ?>
							<?php $__currentLoopData = $syaratam_am; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syaratam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
								<?php if($syaratam->KODSUBJEK!='F' &&  $syaratam->KODSUBJEK!='W' &&  $syaratam->KODSUBJEK!='T'): ?>
									<li style="padding-left: .3em;">
										Gred <b><?php echo e($syaratam->MINGRED); ?></b> bagi matapelajaran
										<?php $__currentLoopData = $subjek; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spm_subjek): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
											<?php if($spm_subjek->kodsubjekspm==$syaratam->KODSUBJEK): ?>
												<b><?php echo e($spm_subjek->ketsubjekspm); ?></b>.
											<?php endif; ?>
										<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
									</li>
								<?php endif; ?>

								<?php
								if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
								?>


								<?php if($syaratam->KODSUBJEK=='F'): ?>
									<li style="padding-left: .3em;"><b><?php echo e($read); ?> (<?php echo e($syaratam->JUMLAH_MIN_SUBJEK); ?>) <?php echo e($syaratam->MINGRED); ?></b> dalam mana-mana matapelajaran yang belum dikira.</li>
								<?php endif; ?>
							<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
						<?php endif; ?>
					</ol>
				<?php endif; ?>



        </ol>
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/syarat_am_spm.blade.php ENDPATH**/ ?>