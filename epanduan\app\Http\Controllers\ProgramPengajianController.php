<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProgramPengajianController extends Controller
{
    public function index(Request $request, $kod)
    {

        session()->forget(['namaProgram', 'carianKategori']);

        //Sesi Program Pengajian
        $sesiakademik = session()->get('sesi_semasa');
        // $sesiSemasa = '2425';

        if (!is_null($request->kategoriCalon)) {
            session(['kategoriCalon' => $request->kategoriCalon]);
        }

        //Carian Nama Program
        $carianNamaProgram = $request->session()->get('fuzzySearch');

        //Carian P<PERSON>ta Merit Program
        $purataMeritProgram = $request->session()->get('meritProgram');
        $purataMeritProgramMin = $request->session()->get('meritProgramMin');
        $purataMeritProgramMax = $request->session()->get('meritProgramMax');

        //Jenis Bidang NEC
        $pBidang = session()->get('jBIDANG');

        if (!is_null($pBidang)) {
            $arr_bidang = $pBidang;
        } else {
            $arr_bidang = [];
        }

        //Jenis IPTA
        $jIPTA = session()->get('jIPTA');
        $jIPTA2 = session()->get('jIPTA2');
        $jIPTA3 = session()->get('jIPTA3');

        if (!is_null($jIPTA)) {
            $arr_ipta = $jIPTA;

        } elseif (!is_null($jIPTA2)) {
            $arr_ipta = [$jIPTA2];

        } elseif (!is_null($jIPTA3)) {
            $arr_ipta = [$jIPTA3];

        } else {
            $arr_ipta = [];

        }

        //Program Bertemuduga
        $pTemuduga = session()->get('jTEMUDUGA');

        if (!is_null($pTemuduga)) {
            $arr_temuduga = $pTemuduga;
        } else {
            $arr_temuduga = [];
        }

        //Program MQA TVET
        $pTVET = session()->get('jTVET');

        if (!is_null($pTVET)) {
            $arr_MQATVET = $pTVET;
        } else {
            $arr_MQATVET = [];
        }

           //Program BUmi
        $pBUMI = session()->get('jBUMI');

        if (!is_null($pBUMI)) {
            $arr_BUMI = $pBUMI;
        } else {
            $arr_BUMI = [];
        }



        //Mod Pengajian
        $ModPengajian = session()->get('jMODpengajian');

        if (!is_null($ModPengajian)) {
            $arr_MODPengajian = $ModPengajian;
        } else {
            $arr_MODPengajian = [];
        }

        //Peringkat Pengajian
        $peringkatPengajian = session()->get('jPERINGKATpengajian');

        if (!is_null($peringkatPengajian)) {
            $arr_PERINGKATPengajian = $peringkatPengajian;
        } else {
            $arr_PERINGKATPengajian = [];
        }

        //Joint / Dual Award / Double Degree
        $doubleDegree = session()->get('jDOUBLE_DEGREE');

        if (!is_null($doubleDegree)) {
            $arr_doubleDgree = $doubleDegree;
        } else {
            $arr_doubleDgree = [];
        }

        $kodkatag = $request->route('kodkatag');
		//dd($kodkatag);


        if (session()->get('jenprog') == 'spm') {

            $SENARAI_PROGRAM = DB::connection('emas')->table('program as Program_SPM')
                ->select(
                    'Program_SPM.KODUNIVERSITI As kod_IPTA',
                    'Program_SPM.KODPROGRAM_PAPAR As kod_Program',
                    'Program_SPM.NAMAPROGRAM As nama_Program',
                    'Program_SPM.KATEGORI AS kategori_Pengajian',
                    'Program_SPM.BIDANG_NEC AS bidang_NEC',
                    'Program_SPM.TEMUDUGA As program_Temuduga',
                    'Program_SPM.MQATVET As program_TVET',
                    'Program_SPM.BUMI As program_BUMI',
					'Program_SPM.STEM As program_STEM',
					'Program_SPM.FEEDER As program_FEEDER',
                    'IPTA_NAMA As nama_IPTA',
                    'Program_SPM.JENIS_PENGAJIAN As jpengajian',
                    'Program_SPM.YURAN As yuran_Pengajian'
                )
                ->when($arr_ipta, function ($query) use ($arr_ipta) {
                    $query->whereIn('Program_SPM.KODUNIVERSITI', $arr_ipta);
                })
                ->when($arr_bidang, function ($query) use ($arr_bidang) {
                    $query->whereIn(DB::raw('substr(Program_SPM.BIDANG_NEC, 1, 2)'), $arr_bidang);
                })
                ->when($arr_MODPengajian, function ($query) use ($arr_MODPengajian) {
                    $query->whereIn('Program_SPM.MOD2U2I', $arr_MODPengajian);
                })
                ->when($arr_temuduga, function ($query) use ($arr_temuduga) {
                    $query->whereIn('Program_SPM.TEMUDUGA', $arr_temuduga);
                })
                ->when($arr_PERINGKATPengajian, function ($query) use ($arr_PERINGKATPengajian) {
                    $query->whereIn('Program_SPM.JENIS_PERINGKAT', $arr_PERINGKATPengajian);
                })
                ->when($arr_MQATVET, function ($query) use ($arr_MQATVET) {
                    $query->whereIn('Program_SPM.MQATVET', $arr_MQATVET);
                })
                  ->when($arr_BUMI, function ($query) use ($arr_BUMI) {
                    $query->whereIn('Program_SPM.BUMI', $arr_BUMI);
                })
                ->when($purataMeritProgramMin !== null && $purataMeritProgramMin !== '' && $purataMeritProgramMax !== null && $purataMeritProgramMax !== '', function ($query) use ($purataMeritProgramMin, $purataMeritProgramMax) {
                    $query->whereNotNull('emas.vw_programpuratamerit_2526.puratameritbyyear')
                          ->whereRaw('CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= ? AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= ?', [$purataMeritProgramMin, $purataMeritProgramMax]);
                    \Log::info('Merit Filter Applied - SPM', [
                        'min' => $purataMeritProgramMin,
                        'max' => $purataMeritProgramMax,
                        'sql' => 'CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= ' . $purataMeritProgramMin . ' AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= ' . $purataMeritProgramMax
                    ]);
                })
                ->when($purataMeritProgram && ($purataMeritProgramMin === null || $purataMeritProgramMin === '' || $purataMeritProgramMax === null || $purataMeritProgramMax === ''), function ($query) use ($purataMeritProgram) {
                    $query->whereRaw('CAST(puratameritbyyear AS DECIMAL(5,2)) >= ?', [$purataMeritProgram]);
                })
                ->where(function ($query) use ($carianNamaProgram) {
                    $query->where('Program_SPM.NAMAPROGRAM', 'like', '%' . $carianNamaProgram . '%')
                        ->orWhere('Program_SPM.KODPROGRAM_PAPAR', 'like', '%' . $carianNamaProgram . '%');
                })
                ->leftJoin('emas.upu_ipta2', 'emas.upu_ipta2.IPTA_KOD', '=', 'Program_SPM.KODUNIVERSITI')
                ->leftJoin('emas.vw_programpuratamerit_2526', 'emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'Program_SPM.KODPROGRAM_PAPAR')
                ->leftJoin('upucodeset.refalldip_bidangnec', 'upucodeset.refalldip_bidangnec.kod', '=', 'Program_SPM.BIDANG_NEC')
                ->where('Program_SPM.LEPASAN', session()->get('jenprog'))
                ->where('Program_SPM.KATEGORI', $kodkatag)
                ->where('Program_SPM.sesi', '=', $sesiakademik)
                ->where('STATUS_TAWAR', '=', 'Y')
                ->whereNotIn('Program_SPM.KODPROGRAM_PAPAR', function ($q) use ($sesiakademik) {
                    $q->select('UDK_KOD_PROGRAM')->from('emas.upu_daftar_kod')
                        ->where('UDK_JENPROG', session()->get('jenprog'))
                        ->where('UDK_SESI', $sesiakademik)
                        ->where('UDK_STATUS', '1');
                })
                ->groupBy('Program_SPM.KODPROGRAM_PAPAR', 'Program_SPM.KATEGORI')
                ->orderBy('Program_SPM.NAMAPROGRAM', 'ASC')
                ->paginate(10);

            $SENARAI_KAMPUS = DB::table('upu_kampus')
                ->select('KOD', 'upucodeset.ref_kampus.kampus As lokasiKampus', 'upucodeset.ref_kampus.negeri As negeri')
                ->leftJoin('upucodeset.ref_kampus', 'emas.upu_kampus.KOD_KAMPUS', '=', 'upucodeset.ref_kampus.kodkampus')
                ->where('JENPROG', session()->get('jenprog'))
                ->get();

            $PUSAT_LATIHAN = DB::table('lantis_pusatlatihan_finox')
                ->select('kodkursus', 'ketpusat', 'pstlatih', 'psttduga', 'statuspusat', 'mesraoku', 'asrama', 'jantinakhas', 'sesi')
                ->join('lantis_unjur_finox_rasmi', 'lantis_pusatlatihan_finox.kodpusatbaru', '=', 'lantis_unjur_finox_rasmi.pusatlatihan')
                ->where('statuspusat', '=', 'Y')
                ->where('sesi', $sesiakademik)
                ->get();

            $MAKLUMAT_PENGAJIAN = DB::connection('emas')->table('program as maklumatPengajianSPM')
                ->select(
                    'maklumatPengajianSPM.KODPROGRAM_PAPAR AS kod_Program',
                    'maklumatPengajianSPM.BILSEMESTER AS tempoh_Pengajian',
					'maklumatPengajianSPM.JENISTEMPOH AS tempoh',
                    'maklumatPengajianSPM.TEMUDUGA AS program_Temuduga',
                    'maklumatPengajianSPM.MOD2U2I AS mod2u2i',
                    'maklumatPengajianSPM.DOUBLE_DEGREE AS double_Degree',
                    'maklumatPengajianSPM.BIDANG_NEC AS bidang_NEC',
                    'upucodeset.refalldip_bidangnec.ket_bidang_bm AS keterangan_bidangNEC',
                    'maklumatPengajianSPM.JENIS_PERINGKAT AS peringkat_Pengajian',
					'maklumatPengajianSPM.KATEGORI AS kategori_Pengajian',
                    'upucodeset.refemas_peringkat.ket_peringkat AS keterangan_peringkat',
                    'maklumatPengajianSPM.REMARKS AS catatan_Program',
                    'emas.vw_programpuratamerit_2526.puratameritbyyear AS merit_Program'
                )
                ->leftJoin('emas.vw_programpuratamerit_2526', 'emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'maklumatPengajianSPM.KODPROGRAM_PAPAR')
                ->leftJoin('upucodeset.refalldip_bidangnec', 'upucodeset.refalldip_bidangnec.kod', '=', 'maklumatPengajianSPM.BIDANG_NEC')
                ->leftJoin('upucodeset.refemas_peringkat', 'upucodeset.refemas_peringkat.kod_peringkat', '=', 'maklumatPengajianSPM.JENIS_PERINGKAT')
                ->where('maklumatPengajianSPM.LEPASAN', '=', 'SPM')
                ->where('maklumatPengajianSPM.KATEGORI', '=', 'A')
                ->where('maklumatPengajianSPM.sesi', '=', $sesiakademik)
                ->where('STATUS_TAWAR', '=', 'Y')
                ->groupBy('maklumatPengajianSPM.KODPROGRAM_PAPAR', 'maklumatPengajianSPM.KATEGORI')
                ->get();

            // $SYARAT_AM = DB::connection('emas')->table('program')
            //     ->select('program.KODPROGRAM_PAPAR As syaratAm_kodProgram', 'program.NAMAPROGRAM', 'program.LEPASAN', 'emas.upu_syarat_am.USA_SYARAT', 'emas.upu_syarat_am.USA_LEPASAN')
            //     ->leftJoin('emas.upu_syarat_am', 'program.LEPASAN', '=', 'emas.upu_syarat_am.USA_JENPROG')
            //     ->where('emas.upu_syarat_am.USA_STATUS_SYARAT', 'Y')
            //     ->groupBy('program.KODPROGRAM_PAPAR')
            //     ->get();


            $LALUAN_KERJAYA = DB::table('upu_kerjaya')
                ->select('UKJ_KOD As kod_Kerjaya', 'UKJ_KERJAYA As program_Kerjaya')
                ->get();

            $jenispengajian = DB::connection('emas')->table('jenis_pengajian')->where('LEPASAN',session()->get('jenprog'))->get();
            $subjek = DB::connection('upu_codeset')->table('refspm_subjek')->whereNotIn('kodsubjekspm',['0000'])->get();

        }


        // ********************************** STPM **********************************

        elseif (session()->get('jenprog') == 'stpm') {


            $SENARAI_PROGRAM = DB::connection('emas')->table('program_stpm as Program_STPM')
                ->select(
                    'Program_STPM.KODUNIVERSITI As kod_IPTA',
                    'Program_STPM.KODPROGRAM_PAPAR As kod_Program',
                    'Program_STPM.NAMAPROGRAM As nama_Program',
                    'Program_STPM.KATEGORI AS kategori_Pengajian',
                    'Program_STPM.JENSETARAF AS jensetaraf',
                    'Program_STPM.BIDANG_NEC AS bidang_NEC',
                    'Program_STPM.TEMUDUGA As program_Temuduga',
                    'Program_STPM.MQATVET As program_TVET',
					'Program_STPM.KOMPETITIF As program_KOMPETITIF',
					'Program_STPM.BTECH As program_BTECH',
                    'IPTA_NAMA As nama_IPTA',
                    'Program_STPM.JENIS_PENGAJIAN As jpengajian',
                    'Program_STPM.YURAN As yuran_Pengajian'
                )
                ->when($arr_ipta, function ($query) use ($arr_ipta) {
                    $query->whereIn('Program_STPM.KODUNIVERSITI', $arr_ipta);
                })
                ->when($arr_bidang, function ($query) use ($arr_bidang) {
                    $query->whereIn(DB::raw('substr(Program_STPM.BIDANG_NEC, 1, 2)'), $arr_bidang);
                })
                ->when($arr_MODPengajian, function ($query) use ($arr_MODPengajian) {
                    $query->whereIn('Program_STPM.MOD2U2I', $arr_MODPengajian);
                })
                ->when($arr_temuduga, function ($query) use ($arr_temuduga) {
                    $query->whereIn('Program_STPM.TEMUDUGA', $arr_temuduga);
                })
                ->when($arr_PERINGKATPengajian, function ($query) use ($arr_PERINGKATPengajian) {
                    $query->whereIn('Program_STPM.JENIS_PERINGKAT', $arr_PERINGKATPengajian);
                })
                ->when($arr_MQATVET, function ($query) use ($arr_MQATVET) {
                    $query->whereIn('Program_STPM.MQATVET', $arr_MQATVET);
                })
                ->when($purataMeritProgramMin !== null && $purataMeritProgramMin !== '' && $purataMeritProgramMax !== null && $purataMeritProgramMax !== '', function ($query) use ($purataMeritProgramMin, $purataMeritProgramMax) {
                    $query->whereNotNull('emas.vw_programpuratamerit_2526.puratameritbyyear')
                          ->whereRaw('CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= ? AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= ?', [$purataMeritProgramMin, $purataMeritProgramMax]);
                    \Log::info('Merit Filter Applied - STPM', [
                        'min' => $purataMeritProgramMin,
                        'max' => $purataMeritProgramMax,
                        'sql' => 'CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) >= ' . $purataMeritProgramMin . ' AND CAST(emas.vw_programpuratamerit_2526.puratameritbyyear AS DECIMAL(5,2)) <= ' . $purataMeritProgramMax
                    ]);
                })
                ->when($purataMeritProgram && ($purataMeritProgramMin === null || $purataMeritProgramMin === '' || $purataMeritProgramMax === null || $purataMeritProgramMax === ''), function ($query) use ($purataMeritProgram) {
                    $query->whereRaw('CAST(puratameritbyyear AS DECIMAL(5,2)) >= ?', [$purataMeritProgram]);
                })
                ->where(function ($query) use ($carianNamaProgram) {
                    $query->where('Program_STPM.NAMAPROGRAM', 'like', '%' . $carianNamaProgram . '%')
                        ->orWhere('Program_STPM.KODPROGRAM_PAPAR', 'like', '%' . $carianNamaProgram . '%');
                })
                ->leftJoin('emas.upu_ipta2', 'emas.upu_ipta2.IPTA_KOD', '=', 'Program_STPM.KODUNIVERSITI')
                // ->leftJoin('emas.vw_programpuratamerit_2526', function ($join) {
                //     $join->on('emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'Program_STPM.KODPROGRAM_PAPAR')
                //         ->whereIn('emas.vw_programpuratamerit_2526.tahap', ['STPM','STAM']);
                // })
                ->leftJoin('emas.vw_programpuratamerit_2526', 'emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'Program_STPM.KODPROGRAM_PAPAR')
                ->when(substr($kodkatag, 0, 1) == 'E' || substr($kodkatag, 0, 1) == 'F' || substr($kodkatag, 0, 1) == 'G', function ($q) use ($kodkatag) {
                    return $q->where('Program_STPM.JENSETARAF', $kodkatag);
                })
                ->when(substr($kodkatag, 0, 1) != 'E' && substr($kodkatag, 0, 1) != 'F' && substr($kodkatag, 0, 1) != 'G', function ($q) use ($kodkatag) {
                    return $q->where('Program_STPM.KATEGORI', $kodkatag);
                })
                ->leftJoin('upucodeset.refalldip_bidangnec', 'upucodeset.refalldip_bidangnec.kod', '=', 'Program_STPM.BIDANG_NEC')
                ->where('Program_STPM.LEPASAN', session()->get('jenprog'))
                // ->where('Program_STPM.KATEGORI', $kodkatag)
                ->where('Program_STPM.sesi', '=', $sesiakademik)
                ->where('STATUS_TAWAR', '=', 'Y')
                ->whereNotIn('Program_STPM.KODPROGRAM_PAPAR', function ($q) use ($sesiakademik) {
                    $q->select('UDK_KOD_PROGRAM')->from('emas.upu_daftar_kod')
                        ->where('UDK_JENPROG', session()->get('jenprog'))
                        ->where('UDK_SESI', $sesiakademik)
                        ->where('UDK_STATUS', '1');
                })
                ->groupBy('Program_STPM.KODPROGRAM_PAPAR', 'Program_STPM.KATEGORI')
                ->orderBy('Program_STPM.NAMAPROGRAM', 'ASC')
                ->paginate(10);


                // dd($SENARAI_PROGRAM);


            // $SENARAI_PROGRAM = DB::connection('emas')->table('upu_kod As Program_STPM')
            //     ->select(
            //         'Program_STPM.UK_KOD_IPTA as kod_IPTA',
            //         'Program_STPM.UK_KOD_PROGRAM as kod_Program',
            //         'Program_STPM.UK_PROGRAM as nama_Program',
            //         'Program_STPM.UK_BIDANG_NEC as bidang_NEC',
            //         'Program_STPM.UK_TDUGA as program_Temuduga',
            //         'Program_STPM.UK_YURAN as yuran_Pengajian',
            //         'Program_STPM.UK_MQATVET as program_TVET',
            //         'emas.upu_ipta2.IPTA_NAMA as nama_IPTA'
            //     )
            //     ->when($arr_ipta, function ($query) use ($arr_ipta) {
            //         $query->whereIn('Program_STPM.UK_KOD_IPTA', $arr_ipta);
            //     })
            //     ->when($arr_bidang, function ($query) use ($arr_bidang) {
            //         $query->whereIn(DB::raw('substr(Program_STPM.UK_BIDANG_NEC, 1, 2)'), $arr_bidang);
            //     })
            //     ->when($arr_MODPengajian, function ($query) use ($arr_MODPengajian) {
            //         $query->whereIn('Program_STPM.UK_MOD2U2I', $arr_MODPengajian);
            //     })
            //     ->when($arr_doubleDgree, function ($query) use ($arr_doubleDgree) {
            //         $query->whereIn('Program_STPM.UK_DOUBLE_DEGREE', $arr_doubleDgree);
            //     })
            //     ->when($arr_temuduga, function ($query) use ($arr_temuduga) {
            //         $query->whereIn('Program_STPM.UK_TDUGA', $arr_temuduga);
            //     })
            //     ->when($arr_MQATVET, function ($query) use ($arr_MQATVET) {
            //         $query->whereIn('Program_STPM.UK_MQATVET', $arr_MQATVET);
            //     })
            //     ->when($purataMeritProgram, function ($query) use ($purataMeritProgram) {
            //         $query->whereRaw('puratameritbyyear >= ?', [$purataMeritProgram]);
            //     })
            //     ->where(function ($query) use ($carianNamaProgram) {
            //         $query->where('Program_STPM.UK_PROGRAM', 'like', '%' . $carianNamaProgram . '%')
            //             ->orWhere('Program_STPM.UK_KOD_PROGRAM', 'like', '%' . $carianNamaProgram . '%');
            //     })
            //     ->when(substr($kodkatag, 0, 1) == 'E' || substr($kodkatag, 0, 1) == 'F' || substr($kodkatag, 0, 1) == 'G', function ($q) use ($kodkatag) {
            //         $q->join('emas.upu_syarat', function ($join) use ($kodkatag) {
            //             $join->on('emas.upu_syarat.US_KOD_PROGRAM', '=', 'Program_STPM.UK_KOD_PROGRAM')
            //                 ->whereRaw('emas.upu_syarat.US_JENSETARAF=? and emas.upu_syarat.US_JENPROG=? and emas.upu_syarat.US_SESI=?', [$kodkatag, session()->get('jenprog'), session()->get('sesiSemasa')]);
            //         });
            //     })
            //     ->when(substr($kodkatag, 0, 1) != 'E' && substr($kodkatag, 0, 1) != 'F' && substr($kodkatag, 0, 1) != 'G', function ($q) use ($kodkatag) {
            //         $q->join('emas.upu_syarat', function ($join) use ($kodkatag) {
            //             $join->on('emas.upu_syarat.US_KOD_PROGRAM', '=', 'Program_STPM.UK_KOD_PROGRAM')
            //                 ->whereRaw('emas.upu_syarat.US_KAT=? and emas.upu_syarat.US_JENPROG=? and emas.upu_syarat.US_SESI=?', [$kodkatag, session()->get('jenprog'), session()->get('sesiSemasa')]);
            //         });
            //     })
            //     ->leftJoin('emas.vw_programpuratamerit_2526', function ($join) {
            //         $join->on('emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'Program_STPM.UK_KOD_PROGRAM')
            //             ->where('emas.vw_programpuratamerit_2526.kategori', '=', 'STPM/MATRIKULASI/ASASI');
            //     })
            //     ->join('emas.upu_ipta2', 'emas.upu_ipta2.IPTA_KOD', '=', 'Program_STPM.UK_KOD_IPTA')
            //     ->where('Program_STPM.UK_JENPROG', session()->get('jenprog'))
            //     ->where('Program_STPM.UK_SESI', '=', $sesiSemasa)
            //     ->whereNotIn('Program_STPM.UK_KOD_PROGRAM', function ($q) use ($sesiSemasa) {
            //         $q->select('UDK_KOD_PROGRAM')->from('emas.upu_daftar_kod')
            //             ->where('UDK_JENPROG', session()->get('jenprog'))
            //             ->where('UDK_SESI', $sesiSemasa)
            //             ->where('UDK_STATUS', '1');
            //     })
            //     ->groupBy('Program_STPM.UK_KOD_PROGRAM')
            //     ->orderBy('Program_STPM.UK_PROGRAM', 'ASC')
            //     ->paginate(10);

            $SENARAI_KAMPUS = DB::table('upu_kampus')
                ->select('KOD', 'upucodeset.ref_kampus.kampus As lokasiKampus', 'upucodeset.ref_kampus.negeri As negeri')
                ->leftJoin('upucodeset.ref_kampus', 'emas.upu_kampus.KOD_KAMPUS', '=', 'upucodeset.ref_kampus.kodkampus')
                ->where('JENPROG', session()->get('jenprog'))
                ->get();

                if($kodkatag=='T')
                {
                    $MAKLUMAT_PENGAJIAN = DB::connection('emas')->table('program_stpm as maklumatPengajianSTPM')
                    ->select(
                        'maklumatPengajianSTPM.KODPROGRAM_PAPAR AS kod_Program',
                        'maklumatPengajianSTPM.BILSEMESTER AS tempoh_Pengajian',
						'maklumatPengajianSTPM.JENISTEMPOH AS tempoh',
                        'maklumatPengajianSTPM.TEMUDUGA AS program_Temuduga',
                        'maklumatPengajianSTPM.MOD2U2I AS mod2u2i',
                        'maklumatPengajianSTPM.DOUBLE_DEGREE AS double_Degree',
                        'maklumatPengajianSTPM.BIDANG_NEC AS bidang_NEC',
                        'upucodeset.refalldip_bidangnec.ket_bidang_bm AS keterangan_bidangNEC',
                        'maklumatPengajianSTPM.JENIS_PERINGKAT AS peringkat_Pengajian',
                        'maklumatPengajianSTPM.KATEGORI AS kategori_Pengajian',
                        'upucodeset.refemas_peringkat.ket_peringkat AS keterangan_peringkat',
                        'maklumatPengajianSTPM.REMARKS AS catatan_Program',
                        'emas.vw_programpuratamerit_2526.puratameritbyyear AS merit_Program',
                        'emas.vw_programpuratamerit_2526.tahap AS tahap'
                    )
                    ->leftJoin('emas.vw_programpuratamerit_2526', function ($join) {
                        $join->on('emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'maklumatPengajianSTPM.KODPROGRAM_PAPAR')
                            ->where('emas.vw_programpuratamerit_2526.kategori', '=', 'STAM');
                    })
                    ->leftJoin('upucodeset.refalldip_bidangnec', 'upucodeset.refalldip_bidangnec.kod', '=', 'maklumatPengajianSTPM.BIDANG_NEC')
                    ->leftJoin('upucodeset.refemas_peringkat', 'upucodeset.refemas_peringkat.kod_peringkat', '=', 'maklumatPengajianSTPM.JENIS_PERINGKAT')
                    ->where('maklumatPengajianSTPM.LEPASAN', '<>', 'SPM')
                    // ->where('maklumatPengajianSTPM.KATEGORI', '=', 'S')
                    ->where('maklumatPengajianSTPM.sesi', '=', $sesiakademik)
                    ->where('STATUS_TAWAR', '=', 'Y')
                    ->when(substr($kodkatag, 0, 1) == 'E' || substr($kodkatag, 0, 1) == 'F' || substr($kodkatag, 0, 1) == 'G', function ($q) use ($kodkatag) {
                        return $q->where('maklumatPengajianSTPM.JENSETARAF', $kodkatag);
                    })
                    ->when(substr($kodkatag, 0, 1) != 'E' && substr($kodkatag, 0, 1) != 'F' && substr($kodkatag, 0, 1) != 'G', function ($q) use ($kodkatag) {
                        return $q->where('maklumatPengajianSTPM.KATEGORI', $kodkatag);
                    })

                    ->groupBy('maklumatPengajianSTPM.KODPROGRAM_PAPAR')
                    ->get();
                }
                else {
                    $MAKLUMAT_PENGAJIAN = DB::connection('emas')->table('program_stpm as maklumatPengajianSTPM')
                    ->select(
                        'maklumatPengajianSTPM.KODPROGRAM_PAPAR AS kod_Program',
                        'maklumatPengajianSTPM.BILSEMESTER AS tempoh_Pengajian',
						'maklumatPengajianSTPM.JENISTEMPOH AS tempoh',
                        'maklumatPengajianSTPM.TEMUDUGA AS program_Temuduga',
                        'maklumatPengajianSTPM.MOD2U2I AS mod2u2i',
                        'maklumatPengajianSTPM.DOUBLE_DEGREE AS double_Degree',
                        'maklumatPengajianSTPM.BIDANG_NEC AS bidang_NEC',
                        'upucodeset.refalldip_bidangnec.ket_bidang_bm AS keterangan_bidangNEC',
                        'maklumatPengajianSTPM.JENIS_PERINGKAT AS peringkat_Pengajian',
                        'maklumatPengajianSTPM.KATEGORI AS kategori_Pengajian',
                        'upucodeset.refemas_peringkat.ket_peringkat AS keterangan_peringkat',
                        'maklumatPengajianSTPM.REMARKS AS catatan_Program',
                        'emas.vw_programpuratamerit_2526.puratameritbyyear AS merit_Program',
                        'emas.vw_programpuratamerit_2526.tahap AS tahap'
                    )
                    ->leftJoin('emas.vw_programpuratamerit_2526', function ($join) {
                        $join->on('emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'maklumatPengajianSTPM.KODPROGRAM_PAPAR')
                            ->where('emas.vw_programpuratamerit_2526.kategori', '=', 'STPM/MATRIKULASI/ASASI');
                    })
                    ->leftJoin('upucodeset.refalldip_bidangnec', 'upucodeset.refalldip_bidangnec.kod', '=', 'maklumatPengajianSTPM.BIDANG_NEC')
                    ->leftJoin('upucodeset.refemas_peringkat', 'upucodeset.refemas_peringkat.kod_peringkat', '=', 'maklumatPengajianSTPM.JENIS_PERINGKAT')
                    ->where('maklumatPengajianSTPM.LEPASAN', '<>', 'SPM')
                    // ->where('maklumatPengajianSTPM.KATEGORI', '=', 'S')
                    ->where('maklumatPengajianSTPM.sesi', '=', $sesiakademik)
                    ->where('STATUS_TAWAR', '=', 'Y')
                    ->when(substr($kodkatag, 0, 1) == 'E' || substr($kodkatag, 0, 1) == 'F' || substr($kodkatag, 0, 1) == 'G', function ($q) use ($kodkatag) {
                        return $q->where('maklumatPengajianSTPM.JENSETARAF', $kodkatag);
                    })
                    ->when(substr($kodkatag, 0, 1) != 'E' && substr($kodkatag, 0, 1) != 'F' && substr($kodkatag, 0, 1) != 'G', function ($q) use ($kodkatag) {
                        return $q->where('maklumatPengajianSTPM.KATEGORI', $kodkatag);
                    })

                    ->groupBy('maklumatPengajianSTPM.KODPROGRAM_PAPAR')
                    ->get();
                }



                $jenispengajian = DB::connection('emas')->table('jenis_pengajian')->where('LEPASAN',session()->get('jenprog'))->get();
                $subjek = DB::connection('upu_codeset')->table('refspm_subjek')->whereNotIn('kodsubjekspm',['0000'])->get();
                $subjek_stpm = DB::connection('upu_codeset')->table('refstpm_subjek')->where('statussubjekstpm','Y')->get();
                $codeset_muet2 = DB::connection('upu_codeset')->table('refmuet_thp_2')->where('statusthpmuet','Y')->get();
                $codeset_stam = DB::connection('upu_codeset')->table('refstam_thp')->get();

                $syaratam_am_spm  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN','SM')->get();
                $syaratam_am_sm1  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN','SM1')->get();
                $syaratam_am_sm2  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN','SM2')->get();
                $syaratam_am_sm3  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN','SM3')->get();
                $syaratam_am_sm4  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN','SM4')->get();
                $syaratam_am_sm5  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN','SM5')->get();
                $syaratam_am_sm6  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN','SM6')->get();
                $syaratam_am_sm7  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN','SM7')->get();

                $syaratam_diploma_sm4  = DB::connection('emas')->table('syarat_am_diploma')->where('KOD_PENGAJIAN','SM4')->get();
                $syaratam_diploma_sm5  = DB::connection('emas')->table('syarat_am_diploma')->where('KOD_PENGAJIAN','SM5')->get();
                $syaratam_diploma_sm6  = DB::connection('emas')->table('syarat_am_diploma')->where('KOD_PENGAJIAN','SM6')->get();
                $syaratam_diploma_sm7  = DB::connection('emas')->table('syarat_am_diploma')->where('KOD_PENGAJIAN','SM7')->get();

                $syaratam_am_lain_matrik  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN','SM1')->where('KATEGORI','matrik')->get();
                $syaratam_am_lain_stpm  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN','SM2')->where('KATEGORI','stpm')->get();
                $syaratam_am_lain_stam  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN','SM3')->where('KATEGORI','stam')->get();
                $syaratam_am_lain_diploma_sm4  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN','SM4')->where('KATEGORI','diploma')->get();
                $syaratam_am_lain_diploma_sm5  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN','SM5')->where('KATEGORI','diploma')->get();
                $syaratam_am_lain_diploma_sm6  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN','SM6')->where('KATEGORI','diploma')->get();
                $syaratam_am_lain_diploma_sm7  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN','SM7')->where('KATEGORI','diploma')->get();


            // $MAKLUMAT_PENGAJIAN = DB::connection('emas')->table('upu_kod as maklumatPengajianSTPM')
            //     ->select(
            //         'maklumatPengajianSTPM.UK_KOD_PROGRAM as kod_Program',
            //         'emas.upu_syarat.US_KAT as ALIRAN',
            //         'maklumatPengajianSTPM.UK_TEMPOH as tempoh_Pengajian',
            //         'maklumatPengajianSTPM.UK_TDUGA as program_Temuduga',
            //         'maklumatPengajianSTPM.UK_MOD2U2I as mod2u2i',
            //         'maklumatPengajianSTPM.UK_DOUBLE_DEGREE as double_Degree',
            //         'maklumatPengajianSTPM.UK_BIDANG_NEC as bidang_NEC',
            //         'maklumatPengajianSTPM.UK_PERINGKAT as peringkat_Pengajian',
            //         'emas.upu_syarat.US_CATATAN as catatan_Program',
            //         'upucodeset.refalldip_bidangnec.ket_bidang_bm as keterangan_bidangNEC',
            //         'upucodeset.refemas_peringkat.ket_peringkat as keterangan_peringkat',
            //         'emas.vw_programpuratamerit_2526.puratameritbyyear as merit_Program'
            //     )
            //     ->join('emas.upu_syarat', function ($join) {
            //         $join->on('emas.upu_syarat.US_KOD_PROGRAM', '=', 'maklumatPengajianSTPM.UK_KOD_PROGRAM')
            //             ->on('emas.upu_syarat.US_JENPROG', '=', 'maklumatPengajianSTPM.UK_JENPROG')
            //             ->on('emas.upu_syarat.US_SESI', '=', 'maklumatPengajianSTPM.UK_SESI');
            //     })
            //     ->leftJoin('emas.vw_programpuratamerit_2526', function ($join) {
            //         $join->on('emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'maklumatPengajianSTPM.UK_KOD_PROGRAM')
            //             ->where('emas.vw_programpuratamerit_2526.kategori', '=', 'STPM/MATRIKULASI/ASASI');
            //     })
            //     ->leftJoin('upucodeset.refalldip_bidangnec', 'upucodeset.refalldip_bidangnec.kod', '=', 'maklumatPengajianSTPM.UK_BIDANG_NEC')
            //     ->leftJoin('upucodeset.refemas_peringkat', 'upucodeset.refemas_peringkat.kod_peringkat', '=', 'maklumatPengajianSTPM.UK_PERINGKAT')
            //     ->where('emas.upu_syarat.US_JENPROG', session()->get('jenprog'))
            //     ->where('emas.upu_syarat.US_SESI', '=', $sesiSemasa)

            //     ->when(substr($kodkatag, 0, 1) == 'E' || substr($kodkatag, 0, 1) == 'F' || substr($kodkatag, 0, 1) == 'G', function ($q) use ($kodkatag) {
            //         return $q->where('emas.upu_syarat.US_JENSETARAF', $kodkatag);
            //     })

            //     ->when(substr($kodkatag, 0, 1) != 'E' && substr($kodkatag, 0, 1) != 'F' && substr($kodkatag, 0, 1) != 'G', function ($q) use ($kodkatag) {
            //         return $q->where('emas.upu_syarat.US_KAT', $kodkatag);
            //     })
            //     ->groupBy('maklumatPengajianSTPM.UK_KOD_PROGRAM')
            //     ->get();

            // $SYARAT_PENGAJIAN = DB::connection('emas')->table('upu_syarat as syaratPengajianSTPM')
            //     ->select('syaratPengajianSTPM.US_KOD_PROGRAM AS kod_Program', 'syaratPengajianSTPM.US_SYARAT AS syarat_KhasProgram')
            //     ->where('syaratPengajianSTPM.US_JENPROG', session()->get('jenprog'))
            //     ->when(substr($kodkatag, 0, 1) == 'E' || substr($kodkatag, 0, 1) == 'F' || substr($kodkatag, 0, 1) == 'G', function ($q) use ($kod) {
            //         return $q->where('syaratPengajianSTPM.US_JENSETARAF', $kod);
            //     })

            //     ->when(substr($kodkatag, 0, 1) != 'E' && substr($kodkatag, 0, 1) != 'F' && substr($kodkatag, 0, 1) != 'G', function ($q) use ($kod) {
            //         return $q->where('syaratPengajianSTPM.US_KAT', $kod);
            //     })
            //     ->where('syaratPengajianSTPM.US_SESI', '=', $sesiSemasa)
            //     ->groupBy('syaratPengajianSTPM.US_KOD_PROGRAM', 'syaratPengajianSTPM.US_KAT')
            //     ->get();


            // $SYARAT_AM = DB::connection('emas')->table('program_stpm')
            //     ->select('program_stpm.KODPROGRAM_PAPAR As syaratAm_kodProgram', 'program_stpm.NAMAPROGRAM', 'program_stpm.LEPASAN', 'emas.upu_syarat_am.USA_SYARAT', 'emas.upu_syarat_am.USA_LEPASAN')
            //     ->leftJoin('emas.upu_syarat_am', 'program_stpm.LEPASAN', '=', 'emas.upu_syarat_am.USA_JENPROG')
            //     ->where('emas.upu_syarat_am.USA_STATUS_SYARAT', 'Y')
            //     ->groupBy('program_stpm.KODPROGRAM_PAPAR')
            //     ->get();

            $LALUAN_KERJAYA = DB::table('upu_kerjaya')
                ->select('UKJ_KOD As kod_Kerjaya', 'UKJ_KERJAYA As program_Kerjaya')
                ->get();

            $BIDANG_NEC = DB::table('upu_bidang_nec')
            ->select('UBN_KOD_PROGRAM As kod_program', 'UBN_KAT As kategori', 'UBN_JENSETARAF As jensetaraf', 'UBN_BIDANG_NEC As bidangnec')
            ->get();

            $CODESET_NEC = DB::connection('upu_codeset')->table('refalldip_bidangnec')->get();


        }

        if (session()->get('jenprog') == 'spm') {

            return view('programPengajian.index', compact('SENARAI_PROGRAM', 'MAKLUMAT_PENGAJIAN', 'SENARAI_KAMPUS', 'LALUAN_KERJAYA', 'PUSAT_LATIHAN','jenispengajian','subjek'));

        } elseif (session()->get('jenprog') == 'stpm') {

            return view('programPengajian.index', compact('SENARAI_PROGRAM', 'MAKLUMAT_PENGAJIAN', 'SENARAI_KAMPUS', 'LALUAN_KERJAYA','jenispengajian','subjek','subjek_stpm','codeset_muet2','codeset_stam',
                        'syaratam_am_spm','syaratam_am_sm1','syaratam_am_sm2','syaratam_am_sm3','syaratam_am_sm4','syaratam_am_sm5','syaratam_am_sm6','syaratam_am_sm7',
                        'syaratam_diploma_sm4','syaratam_diploma_sm5','syaratam_diploma_sm6','syaratam_diploma_sm7','syaratam_am_lain_matrik','syaratam_am_lain_stpm','syaratam_am_lain_stam',
                        'syaratam_am_lain_diploma_sm4','syaratam_am_lain_diploma_sm5','syaratam_am_lain_diploma_sm6','syaratam_am_lain_diploma_sm7','BIDANG_NEC','CODESET_NEC'));
        }

    }

    public function carianProgram(Request $request)
    {
        //Searching Function
        if ($request->has('searching')) {
            $jIPTA = $request->input('carianIPTA');
            $jBIDANG = $request->input('pBidang');
            $jTVET = $request->input('pTVET');
            $jTEMUDUGA = $request->input('pTemuduga');
            $carianNamaProgram = $request->input('fuzzySearch');
            $purataMeritProgram = $request->input('meritProgram');
            $purataMeritProgramMin = $request->input('meritProgramMin');
            $purataMeritProgramMax = $request->input('meritProgramMax');
            $jMODpengajian = $request->input('ModPengajian');
            $jPERINGKATpengajian = $request->input('peringkatPengajian');
            $jDOUBLE_DEGREE = $request->input('pDoubleDegree');

            // Debug: Log the merit values
            \Log::info('Merit Filter Debug', [
                'meritProgram' => $purataMeritProgram,
                'meritProgramMin' => $purataMeritProgramMin,
                'meritProgramMax' => $purataMeritProgramMax,
                'meritProgramMin_type' => gettype($purataMeritProgramMin),
                'meritProgramMax_type' => gettype($purataMeritProgramMax),
                'meritProgramMin_empty' => empty($purataMeritProgramMin),
                'meritProgramMax_empty' => empty($purataMeritProgramMax),
                'meritProgramMin_null' => $purataMeritProgramMin === null,
                'meritProgramMax_null' => $purataMeritProgramMax === null,
                'all_inputs' => $request->all()
            ]);

            $request->session()->put([
                'jIPTA' => $jIPTA,
                'jBIDANG' => $jBIDANG,
                'jTVET' => $jTVET,
                'jTEMUDUGA' => $jTEMUDUGA,
                'fuzzySearch' => $carianNamaProgram,
                'meritProgram' => $purataMeritProgram,
                'meritProgramMin' => $purataMeritProgramMin,
                'meritProgramMax' => $purataMeritProgramMax,
                'jMODpengajian' => $jMODpengajian,
                'jPERINGKATpengajian' => $jPERINGKATpengajian,
                'jDOUBLE_DEGREE' => $jDOUBLE_DEGREE
            ]);
        }

        //Clear Searching
        if ($request->has('clearFiltering')) {
            $request->session()->forget(['jIPTA', 'jIPTA2', 'jBIDANG', 'jTVET', 'fuzzySearch', 'meritProgram', 'meritProgramMin', 'meritProgramMax', 'pTVET', 'jTEMUDUGA', 'jMODpengajian', 'jPERINGKATpengajian', 'jDOUBLE_DEGREE']);
            return redirect()->back();
        }

        return back()->withInput();
    }

    public function modal_syarat(Request $request, $kodkatag, $kod)
    {
		$sesiakademik = session()->get('sesi_semasa');
        if (session()->get('jenprog') == 'spm') {

            $SENARAI_PROGRAM = DB::connection('emas')->table('program as Program_SPM')
                ->select(
                    'Program_SPM.KODUNIVERSITI As kod_IPTA',
                    'Program_SPM.KODPROGRAM_PAPAR As kod_Program',
                    'Program_SPM.NAMAPROGRAM As nama_Program',
                    'Program_SPM.KATEGORI AS kategori_Pengajian',
                    'Program_SPM.BIDANG_NEC AS bidang_NEC',
                    'Program_SPM.TEMUDUGA As program_Temuduga',
                    'IPTA_NAMA As nama_IPTA',
                    'Program_SPM.JENIS_PENGAJIAN As jpengajian',
                    'Program_SPM.YURAN As yuran_Pengajian'
                )
                ->leftJoin('emas.upu_ipta2', 'emas.upu_ipta2.IPTA_KOD', '=', 'Program_SPM.KODUNIVERSITI')
                ->leftJoin('emas.vw_programpuratamerit_2526', 'emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'Program_SPM.KODPROGRAM_PAPAR')
                ->leftJoin('upucodeset.refalldip_bidangnec', 'upucodeset.refalldip_bidangnec.kod', '=', 'Program_SPM.BIDANG_NEC')
                ->where('Program_SPM.LEPASAN', session()->get('jenprog'))
                ->where('Program_SPM.KATEGORI', $kodkatag)
                ->where('Program_SPM.KODPROGRAM_PAPAR', $kod)
                ->where('Program_SPM.sesi', '=', $sesiakademik)
                ->where('STATUS_TAWAR', '=', 'Y')
                ->groupBy('Program_SPM.KODPROGRAM_PAPAR', 'Program_SPM.KATEGORI')
                ->paginate(10);

            $SYARAT_PENGAJIAN = DB::connection('emas')->table('program as syaratPengajianSPM')
                ->select('syaratPengajianSPM.KODPROGRAM_PAPAR AS kod_Program', 'syaratPengajianSPM.KATEGORI AS kategori_Pengajian')
                ->get();


            return view('programPengajian.syarat_spm', compact('SYARAT_PENGAJIAN', 'SENARAI_PROGRAM'));

        }

        if (session()->get('jenprog') == 'stpm') {

            $codeset_muet = DB::connection('upu_codeset')->table('refmuet_thp')->get();
            $codeset_muet2 = DB::connection('upu_codeset')->table('refmuet_thp_2')->get();

            $SENARAI_PROGRAM = DB::connection('emas')->table('program_stpm as Program_STPM')
                ->select(
                    'Program_STPM.KODUNIVERSITI As kod_IPTA',
                    'Program_STPM.KODPROGRAM_PAPAR As kod_Program',
                    'Program_STPM.NAMAPROGRAM As nama_Program',
                    'Program_STPM.KATEGORI AS kategori_Pengajian',
                    'Program_STPM.JENSETARAF AS jensetaraf',
                    'Program_STPM.BIDANG_NEC AS bidang_NEC',
                    'Program_STPM.TEMUDUGA As program_Temuduga',
                    'IPTA_NAMA As nama_IPTA',
                    'Program_STPM.JENIS_PENGAJIAN As jpengajian',
                    'Program_STPM.YURAN As yuran_Pengajian'
                )
                ->leftJoin('emas.upu_ipta2', 'emas.upu_ipta2.IPTA_KOD', '=', 'Program_STPM.KODUNIVERSITI')
                ->leftJoin('emas.vw_programpuratamerit_2526', 'emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'Program_STPM.KODPROGRAM_PAPAR')
                ->leftJoin('upucodeset.refalldip_bidangnec', 'upucodeset.refalldip_bidangnec.kod', '=', 'Program_STPM.BIDANG_NEC')
                ->where('Program_STPM.LEPASAN', session()->get('jenprog'))
                ->where('Program_STPM.KATEGORI', $kodkatag)
                ->where('Program_STPM.KODPROGRAM_PAPAR', $kod)
                ->where('Program_STPM.sesi', '=', $sesiakademik)
                ->where('STATUS_TAWAR', '=', 'Y')
                ->groupBy('Program_STPM.KODPROGRAM_PAPAR', 'Program_STPM.KATEGORI')
                ->paginate(10);

            $SYARAT_PENGAJIAN = DB::connection('emas')->table('program_stpm as syaratPengajianSTPM')
                ->select('syaratPengajianSTPM.KODPROGRAM_PAPAR AS kod_Program', 'syaratPengajianSTPM.KATEGORI AS kategori_Pengajian')
                ->get();


            return view('programPengajian.syarat_stpm', compact('SYARAT_PENGAJIAN', 'SENARAI_PROGRAM','codeset_muet','codeset_muet2'));


        }


    }


    public function modal_syarat_diploma(Request $request, $kodkatag, $kodsetaraf, $kod)
    {
		$sesiakademik = session()->get('sesi_semasa');
        if (session()->get('jenprog') == 'stpm') {

            $codeset_muet = DB::connection('upu_codeset')->table('refmuet_thp')->get();
            $codeset_muet2 = DB::connection('upu_codeset')->table('refmuet_thp_2')->get();

            $SENARAI_PROGRAM = DB::connection('emas')->table('program_stpm as Program_STPM')
                ->select(
                    'Program_STPM.KODUNIVERSITI As kod_IPTA',
                    'Program_STPM.KODPROGRAM_PAPAR As kod_Program',
                    'Program_STPM.NAMAPROGRAM As nama_Program',
                    'Program_STPM.KATEGORI AS kategori_Pengajian',
                    'Program_STPM.JENSETARAF AS jensetaraf',
                    'Program_STPM.BIDANG_NEC AS bidang_NEC',
                    'Program_STPM.TEMUDUGA As program_Temuduga',
                    'IPTA_NAMA As nama_IPTA',
                    'Program_STPM.JENIS_PENGAJIAN As jpengajian',
                    'Program_STPM.YURAN As yuran_Pengajian'
                )
                ->leftJoin('emas.upu_ipta2', 'emas.upu_ipta2.IPTA_KOD', '=', 'Program_STPM.KODUNIVERSITI')
                ->leftJoin('emas.vw_programpuratamerit_2526', 'emas.vw_programpuratamerit_2526.programtawaranupu', '=', 'Program_STPM.KODPROGRAM_PAPAR')
                ->leftJoin('upucodeset.refalldip_bidangnec', 'upucodeset.refalldip_bidangnec.kod', '=', 'Program_STPM.BIDANG_NEC')
                ->where('Program_STPM.LEPASAN', session()->get('jenprog'))
                ->where('Program_STPM.KATEGORI', $kodkatag)
                ->where('Program_STPM.JENSETARAF', $kodsetaraf)
                ->where('Program_STPM.KODPROGRAM_PAPAR', $kod)
                ->where('Program_STPM.sesi', '=', $sesiakademik)
                ->where('STATUS_TAWAR', '=', 'Y')
                ->groupBy('Program_STPM.KODPROGRAM_PAPAR', 'Program_STPM.KATEGORI')
                ->paginate(10);



            $SYARAT_PENGAJIAN = DB::connection('emas')->table('program_stpm as syaratPengajianSTPM')
                ->select('syaratPengajianSTPM.KODPROGRAM_PAPAR AS kod_Program', 'syaratPengajianSTPM.KATEGORI AS kategori_Pengajian')
                ->get();


            return view('programPengajian.syarat_stpm', compact('SYARAT_PENGAJIAN', 'SENARAI_PROGRAM','codeset_muet','codeset_muet2'));


        }
    }

}
