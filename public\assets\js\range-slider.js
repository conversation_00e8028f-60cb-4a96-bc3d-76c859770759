document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const meritMin = document.getElementById('meritMin');
    const meritMax = document.getElementById('meritMax');

    // Check if elements exist
    if (!meritMin || !meritMax) {
        console.log('Merit filter elements not found, skipping initialization');
        return;
    }

    console.log('Merit filter initialized successfully');

    // Initialize values
    let minValue = parseFloat(meritMin.value) || 0;
    let maxValue = parseFloat(meritMax.value) || 100;

    // Validation function
    function validateInputs() {
        // Ensure min doesn't exceed max
        if (minValue > maxValue) {
            minValue = maxValue;
            meritMin.value = minValue;
        }
        
        // Ensure max doesn't go below min
        if (maxValue < minValue) {
            maxValue = minValue;
            meritMax.value = maxValue;
        }

        // Ensure values are within bounds
        if (minValue < 0) {
            minValue = 0;
            meritMin.value = minValue;
        }
        if (maxValue > 100) {
            maxValue = 100;
            meritMax.value = maxValue;
        }

        console.log('Values validated - Min:', minValue, 'Max:', maxValue);
    }

    // Input event listeners
    meritMin.addEventListener('input', function() {
        minValue = parseFloat(this.value) || 0;
        validateInputs();
    });

    meritMin.addEventListener('blur', function() {
        if (this.value === '') {
            this.value = 0;
            minValue = 0;
        }
    });

    meritMax.addEventListener('input', function() {
        maxValue = parseFloat(this.value) || 100;
        validateInputs();
    });

    meritMax.addEventListener('blur', function() {
        if (this.value === '') {
            this.value = 100;
            maxValue = 100;
        }
    });

    // Form submission logging
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            console.log('Form submitting with merit values:');
            console.log('meritProgramMin:', meritMin.value);
            console.log('meritProgramMax:', meritMax.value);
        });
    }

    // Initialize validation
    validateInputs();

    console.log('Merit filter setup complete - Use main Cari button to apply filters');
});