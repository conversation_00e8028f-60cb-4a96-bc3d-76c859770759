@foreach ($syarat_khas_kump_3 as  $syarat_kump3)

@php
    $syarat_kesetaraan = DB::connection('kpt')->table('syarat_kesetaraan')->where('PARENT_KUMPULAN_KOD',$syarat_kump3->kodsubjek)->orderby('ORDERID','ASC')->get();

    if($syarat_kump3->jum_subjek=='1') { $read3='SATU'; }
    elseif($syarat_kump3->jum_subjek=='2') { $read3='DUA'; }
    elseif($syarat_kump3->jum_subjek=='3') { $read3='TIGA'; }
    elseif($syarat_kump3->jum_subjek=='4') { $read3='EMPAT'; }
    elseif($syarat_kump3->jum_subjek=='5') { $read3='LIMA'; }
    elseif($syarat_kump3->jum_subjek=='6') { $read3='ENAM'; }
    elseif($syarat_kump3->jum_subjek=='7') { $read3='TUJUH'; }
    elseif($syarat_kump3->jum_subjek=='8') { $read3='LAPAN'; }
    elseif($syarat_kump3->jum_subjek=='9') { $read3='SEMBILAN'; }
    
@endphp


    @if($syarat_kump3->kumpulan=='N' && $syarat_kump3->sub_kumpulan=='N')

        @if(substr($syarat_kump3->kodsubjek,0,1)!='K')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam mata pelajaran 
            <b>
                @foreach($sksubjek as $sk_subjek)
                    @if($syarat_kump3->kodsubjek==$sk_subjek->kodsubjekspm)
                    {{ucwords(strtolower($sk_subjek->ketsubjekspm))}}.
                    @endif
                @endforeach 
            </b>
        </li>
        @endif

        @if(substr($syarat_kump3->kodsubjek,0,1)=='K')
            <li style="padding-left: .3em;"> 
                Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam <b>{{$read}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran berikut :
                <div class="card bg-light text-dark">
                    <div class="card-body p-2">
                        <div style="margin-left:.35rem;">
                            <span style="margin-right: 6px;font-size: 7px;">&#9679;</span>
                            @foreach ($syarat_kesetaraan as $bil_no => $setara)
                                @if($syarat_kump3->kodsubjek==$setara->PARENT_KUMPULAN_KOD)
                                    @foreach($sksubjek as  $sk_subjek)
                                        @if($setara->KOD==$sk_subjek->kodsubjekspm)
                                            {{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }}  @if($bil_no+1 != count($syarat_kesetaraan)) / @endif 
                                        @endif
                                    @endforeach  
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            </li>
        @endif
    @endif

    @if($syarat_kump3->kumpulan=='F' && $syarat_kump3->sub_kumpulan=='F')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam mana-mana <b>{{$read3}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran. 
        </li>
    @endif

    @if($syarat_kump3->kumpulan=='F' && $syarat_kump3->sub_kumpulan=='X')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam mana-mana <b>{{$read3}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran yang belum diambil kira.
        </li>
    @endif

    @if($syarat_kump3->kumpulan=='F' && $syarat_kump3->sub_kumpulan=='Y')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam mana-mana <b>{{$read3}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran selain diatas
        </li>
    @endif

    @if($syarat_kump3->kumpulan=='F2' && $syarat_kump3->sub_kumpulan=='F')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam mana-mana <b>{{$read3}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran. 
        </li>
    @endif

    @if($syarat_kump3->kumpulan=='F2' && $syarat_kump3->sub_kumpulan=='X')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam mana-mana <b>{{$read3}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran yang belum diambil kira.
        </li>
    @endif

    @if($syarat_kump3->kumpulan=='F2' && $syarat_kump3->sub_kumpulan=='Y')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam mana-mana <b>{{$read3}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran selain diatas
        </li>
    @endif

    @if($syarat_kump3->kumpulan=='F3' && $syarat_kump3->sub_kumpulan=='F3')
        @php
            $syarat_xkesetaraan = DB::connection('kpt')->table('upuplus_xsub_kumpulan_subjek')->where('kodprogram',$syarat_kump3->kodprogram)->where('kategori',$syarat_kump3->kategori)->orderby('orderid','ASC')->get();
        @endphp

        <li style="padding-left: .3em;">
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam <b>{{$read3}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran tidak termasuk mata pelajaran berikut :      
            
            <div class="card bg-light text-dark">
                <div class="card-body p-2">
                    @foreach ($syarat_xkesetaraan as $bil_no => $kumpulan_x_subjek)
                        @if($syarat_kump3->kodprogram==$kumpulan_x_subjek->kodprogram)                                                                    
                            @foreach($sksubjek as $sk_subjek)
                                @if($kumpulan_x_subjek->kodsubjek==$sk_subjek->kodsubjekspm)

                                <ul style="list-style-type:disc" class="ml-n3">
                                    <li>{{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }}</li>
                                </ul>
                                {{-- {{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }} @if($bil_no+1 != count($syarat_xkesetaraan)) / @endif  --}}
                                @endif
                            @endforeach                                                                      
                        @endif
                    @endforeach
                </div>
            </div>
        </li>
    @endif

    
    @if($syarat_kump3->kumpulan=='Y' && $syarat_kump3->sub_kumpulan=='N')

        @php
            // $syarat_sub_kumpulan = DB::connection('kpt')->table('syarat_sub_kumpulan_subjek_kump_3')->where('kodprogram',$syarat_kump3->kodprogram)->where('kategori',$syarat_kump3->kategori)->where('kategori',$syarat_kump3->kategori)->groupby('kod_group')->orderby('orderid','ASC')->get();
    
            $syarat_khas_g1 = DB::connection('kpt')->table('upuplus_syarat_khas_kump_3')->where('kodprogram',$print->kodprogram)->where('kategori',$print->kategori)->where('aliran','3')->whereIn('kod_group',['G1','GA'])->orderby('kod_group','ASC')->get();
            $syarat_khas_g2 = DB::connection('kpt')->table('upuplus_syarat_khas_kump_3')->where('kodprogram',$print->kodprogram)->where('kategori',$print->kategori)->where('aliran','3')->whereIn('kod_group',['G2','GB'])->orderby('kod_group','ASC')->get();
            $syarat_khas_g3 = DB::connection('kpt')->table('upuplus_syarat_khas_kump_3')->where('kodprogram',$print->kodprogram)->where('kategori',$print->kategori)->where('aliran','3')->where('kod_group','G3')->get();
            
                if((!empty($syarat_khas_g1[0]) && $syarat_khas_g1[0]->jum_subjek=='1') || (!empty($syarat_khas_g2[0]) && $syarat_khas_g2[0]->jum_subjek=='1')) { $read_g1='SATU'; }
                elseif((!empty($syarat_khas_g1[0]) && $syarat_khas_g1[0]->jum_subjek=='2') || (!empty($syarat_khas_g2[0]) && $syarat_khas_g2[0]->jum_subjek=='2')) { $read_g1='DUA'; }
                elseif((!empty($syarat_khas_g1[0]) && $syarat_khas_g1[0]->jum_subjek=='3') || (!empty($syarat_khas_g2[0]) && $syarat_khas_g2[0]->jum_subjek=='3')) { $read_g1='TIGA'; }
                elseif((!empty($syarat_khas_g1[0]) && $syarat_khas_g1[0]->jum_subjek=='4') || (!empty($syarat_khas_g2[0]) && $syarat_khas_g2[0]->jum_subjek=='4')) { $read_g1='EMPAT'; }
                elseif((!empty($syarat_khas_g1[0]) && $syarat_khas_g1[0]->jum_subjek=='5') || (!empty($syarat_khas_g2[0]) && $syarat_khas_g2[0]->jum_subjek=='5')) { $read_g1='LIMA'; }
                elseif((!empty($syarat_khas_g1[0]) && $syarat_khas_g1[0]->jum_subjek=='6') || (!empty($syarat_khas_g2[0]) && $syarat_khas_g2[0]->jum_subjek=='6')) { $read_g1='ENAM'; }
                elseif((!empty($syarat_khas_g1[0]) && $syarat_khas_g1[0]->jum_subjek=='7') || (!empty($syarat_khas_g2[0]) && $syarat_khas_g2[0]->jum_subjek=='7')) { $read_g1='TUJUH'; }
                elseif((!empty($syarat_khas_g1[0]) && $syarat_khas_g1[0]->jum_subjek=='8') || (!empty($syarat_khas_g2[0]) && $syarat_khas_g2[0]->jum_subjek=='8')) { $read_g1='LAPAN'; }
                elseif((!empty($syarat_khas_g1[0]) && $syarat_khas_g1[0]->jum_subjek=='9') || (!empty($syarat_khas_g2[0]) && $syarat_khas_g2[0]->jum_subjek=='9')) { $read_g1='SEMBILAN'; }

                if((!empty($syarat_khas_g1[1]) && $syarat_khas_g1[1]->jum_subjek=='1') || (!empty($syarat_khas_g2[1]) && $syarat_khas_g2[1]->jum_subjek=='1')) { $read_g2='SATU'; }
                elseif((!empty($syarat_khas_g1[1]) && $syarat_khas_g1[1]->jum_subjek=='2') || (!empty($syarat_khas_g2[1]) && $syarat_khas_g2[1]->jum_subjek=='2')) { $read_g2='DUA'; }
                elseif((!empty($syarat_khas_g1[1]) && $syarat_khas_g1[1]->jum_subjek=='3') || (!empty($syarat_khas_g2[1]) && $syarat_khas_g2[1]->jum_subjek=='3')) { $read_g2='TIGA'; }
                elseif((!empty($syarat_khas_g1[1]) && $syarat_khas_g1[1]->jum_subjek=='4') || (!empty($syarat_khas_g2[1]) && $syarat_khas_g2[1]->jum_subjek=='4')) { $read_g2='EMPAT'; }
                elseif((!empty($syarat_khas_g1[1]) && $syarat_khas_g1[1]->jum_subjek=='5') || (!empty($syarat_khas_g2[1]) && $syarat_khas_g2[1]->jum_subjek=='5')) { $read_g2='LIMA'; }
                elseif((!empty($syarat_khas_g1[1]) && $syarat_khas_g1[1]->jum_subjek=='6') || (!empty($syarat_khas_g2[1]) && $syarat_khas_g2[1]->jum_subjek=='6')) { $read_g2='ENAM'; }
                elseif((!empty($syarat_khas_g1[1]) && $syarat_khas_g1[1]->jum_subjek=='7') || (!empty($syarat_khas_g2[1]) && $syarat_khas_g2[1]->jum_subjek=='7')) { $read_g2='TUJUH'; }
                elseif((!empty($syarat_khas_g1[1]) && $syarat_khas_g1[1]->jum_subjek=='8') || (!empty($syarat_khas_g2[1]) && $syarat_khas_g2[1]->jum_subjek=='8')) { $read_g2='LAPAN'; }
                elseif((!empty($syarat_khas_g1[1]) && $syarat_khas_g1[1]->jum_subjek=='9') || (!empty($syarat_khas_g2[1]) && $syarat_khas_g2[1]->jum_subjek=='9')) { $read_g2='SEMBILAN'; }

            $syarat_sub_kumpulan2 = DB::connection('kpt')->table('syarat_sub_kumpulan_subjek_kump_3')->where('kodprogram',$syarat_kump3->kodprogram)->where('kategori',$syarat_kump3->kategori)->where('aliran',$syarat_kump3->aliran)->where('kod_group',$syarat_kump3->kod_group)->orderby('orderid','ASC')->get();          
            
            @endphp

            {{-- @if(count($syarat_khas_g1) == '1' || count($syarat_khas_g2) == '1' || count($syarat_khas_g3) == '1') --}}
            @if(count($syarat_khas_g3) == '1')

                <li style="padding-left: .3em;">
                    Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam <b>{{$read3}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran berikut :
                    <div class="card bg-light text-dark">
                        <div class="card-body p-2">

                            @foreach ($syarat_sub_kumpulan2 as $bil_no => $syarat_kumpulan)

                                @if(substr($syarat_kumpulan->kodsubjek,0,1)!='K')
                                    @if($syarat_kump3->kodprogram==$syarat_kumpulan->kodprogram)

                                        @foreach($sksubjek as $sk_subjek)
                                            @if($syarat_kumpulan->kodsubjek==$sk_subjek->kodsubjekspm)

                                            <ul style="list-style-type:disc" class="ml-n3">
                                                <li style="margin-left: -1rem !important;">{{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }}</li>
                                            </ul>
                                            @endif
                                        @endforeach     

                                    @endif                        
                                @endif

                                @if(substr($syarat_kumpulan->kodsubjek,0,1)=='K')
                                    @php
                                        $syarat_kesetaraan = DB::connection('kpt')->table('syarat_kesetaraan')->where('PARENT_KUMPULAN_KOD',$syarat_kumpulan->kodsubjek)->orderby('orderid','ASC')->get();
                                    @endphp

                                    <div style="margin-left:.35rem;">
                                        <span style="margin-right: 6px;font-size: 7px;">&#9679;</span>
                                        @foreach ($syarat_kesetaraan as $bil_no => $syarat_setara)
                                            @if($syarat_kumpulan->kodsubjek==$syarat_setara->PARENT_KUMPULAN_KOD)                                                                                                                    
                                                @foreach($sksubjek as $sk_subjek)
                                                    @if($syarat_setara->KOD==$sk_subjek->kodsubjekspm)
                                                    {{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }} @if($bil_no+1 != count($syarat_kesetaraan)) / @endif 
                                                    @endif
                                                @endforeach                                                                                              
                                            @endif
                                        @endforeach    
                                    </div>                                                         
                                @endif 
                            
                            @endforeach
                        </div>
                    </div>
                </li>
            @endif

            @if(count($syarat_khas_g3) > 3)
            {{-- @if(count($syarat_khas_g1) > '1' || count($syarat_khas_g2) > '1' || count($syarat_khas_g3) > '1') --}}
                @if ($loop->even)
                    <li style="padding-left: .3em;">
                        Mendapat sekurang-kurangnya <b>{{$read_g1}} ({{$syarat_kump3->jum_subjek}})</b> Gred <b>{{$syarat_kump3->mingred}}</b> <b>dan</b> <b>{{$read_g2}} ({{$syarat_khas_g1[0]->jum_subjek}})</b> Gred <b>{{$syarat_khas_g1[0]->mingred}}</b> dalam mata pelajaran berikut :

                        {{-- Mendapat sekurang-kurangnya Gred <b>{{$syarat_kump3->mingred}}</b> dalam <b>{{$read}} ({{$syarat_kump3->jum_subjek}})</b> mata pelajaran berikut : --}}
                        <div class="card bg-light text-dark">
                            <div class="card-body p-2">

                                @foreach ($syarat_sub_kumpulan1 as $bil_no => $syarat_kumpulan)
                            
                                    @if(substr($syarat_kumpulan->kodsubjek,0,1)!='K')
                                        @if($syarat_kump3->kodprogram==$syarat_kumpulan->kodprogram)

                                            @foreach($sksubjek as $sk_subjek)
                                                @if($syarat_kumpulan->kodsubjek==$sk_subjek->kodsubjekspm)

                                                <ul style="list-style-type:disc" class="ml-n3">
                                                    <li style="margin-left: -1rem !important;">{{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }}</li>
                                                </ul>
                                                @endif
                                            @endforeach     

                                        @endif                        
                                    @endif

                                    @if(substr($syarat_kumpulan->kodsubjek,0,1)=='K')
                                        @php
                                            $syarat_kesetaraan = DB::connection('kpt')->table('syarat_kesetaraan')->where('PARENT_KUMPULAN_KOD',$syarat_kumpulan->kodsubjek)->orderby('orderid','ASC')->get();
                                        @endphp

                                        <div style="margin-left:.35rem;">
                                            <span style="margin-right: 6px;font-size: 7px;">&#9679;</span>
                                            @foreach ($syarat_kesetaraan as $bil_no => $syarat_setara)
                                                @if($syarat_kumpulan->kodsubjek==$syarat_setara->PARENT_KUMPULAN_KOD)                                                                                                                    
                                                    @foreach($sksubjek as $sk_subjek)
                                                        @if($syarat_setara->KOD==$sk_subjek->kodsubjekspm)
                                                        {{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }} @if($bil_no+1 != count($syarat_kesetaraan)) / @endif 
                                                        @endif
                                                    @endforeach                                                                                              
                                                @endif
                                            @endforeach    
                                        </div>                                                         
                                    @endif 
                                    
                                @endforeach
                            </div>
                        </div>
                    </li>
                @endif
            @endif
        {{-- @endif --}}

@endif


@endforeach

    