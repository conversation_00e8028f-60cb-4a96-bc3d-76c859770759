<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\maklumatAgensi;

class MaklumatAgensiController extends Controller
{
    public function index(Request $request, $nama_ipta)
    {
        session()->forget(['jIPTA', 'jIPTA3','jBIDANG', 'jTEMUDUGA', 'fuzzySearch', 'namaProgram', 'carianKategori', 'meritProgram', 'jTVET', 'kategoriProgram', 'jMODpengajian', 'jPERINGKATpengajian', 'jDOUBLE_DEGREE']);

        $request->session()->put([
            'jIPTA2' => $nama_ipta
        ]);

        $jIPTA = session()->get('jIPTA2');

        $SENARAI_UA = maklumatAgensi::whereNotIn('IPTA_KOD', ['11', '22'])
            ->where('IPTA_KOD', $nama_ipta)
            ->where(function ($query) {
                $query->whereIn('IPTA_KOD', ['UM', 'US', 'UK', 'UP', 'UT', 'UY', 'UW', 'UH', 'UQ', 'UE', 'UD', 'UU', 'UA', 'UG', 'UL', 'UZ', 'UB', 'UC', 'UR', 'UJ'])->where('IPTA_JENPROG', 'stpm')
                    ->orWhereIn('IPTA_KOD', ['FB', 'FC','OP'])->where('IPTA_JENPROG', 'spm');
            })
            ->get();


        $NEGERI = DB::connection('upu_codeset')->table('refperibadi_negeri')->get();

        return view('maklumatAgensi.index', compact('SENARAI_UA', 'NEGERI'));
    }
}
