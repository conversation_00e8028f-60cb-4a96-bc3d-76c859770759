
 @foreach ($syaratkhas_f4 as $syarat_khas_f4)
 
    <li style="padding-left: .3em; margin-bottom:8px;"> 

        Mendapat sekurang-kurangnya 
        
            @if($PROGRAM->kategori_Pengajian=='T')
                @foreach ($codeset_tstam as $tahap_stam)
                    @if($syarat_khas_f4->MINGRED == $tahap_stam->kodthpstam)
                        <b>{{$tahap_stam->ketthpstam}}</b>
                    @endif
                @endforeach
            @else
            Gred <b>{{$syarat_khas_f4->MINGRED}}</b> 
            @endif

        dalam mana-mana <b>{{$syarat_khas_f4->KET_JUMLAH_MIN_SUBJEK}} ({{$syarat_khas_f4->JUMLAH_MIN_SUBJEK}})</b> 
        
        @if($syarat_khas_f4->SUB_KUMPULAN=='F') mata pelajaran.
        @elseif($syarat_khas_f4->SUB_KUMPULAN=='X') mata pelajaran yang belum diambil kira
        @elseif ($syarat_khas_f4->SUB_KUMPULAN=='Y')  mata pelajaran selain diatas
        @endif
        
        pada peringkat 
        
        <b>
            @if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') STPM.
            @elseif($PROGRAM->kategori_Pengajian=='T') STAM.
            @else Matrikulasi / Asasi.
            @endif         
        </b> 
    </li>
 
@endforeach
