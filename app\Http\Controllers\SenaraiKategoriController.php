<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\kategoriLepasanCalon;

class SenaraiKategoriController extends Controller
{
    public function index(Request $request)
    {
        session()->forget(['jIPTA', 'jIPTA3', 'jBIDANG', 'jTEMUDU<PERSON>', 'fuzzySearch', 'namaProgram', 'carianKategori', 'meritProgram', 'jTVET', 'jMODpengajian', 'jPERINGKATpengajian', 'jDOUBLE_DEGREE']);

        $request->session()->put(['jenprog' => $request->jenprog]);

        $JENIS_KATEGORI1 = DB::connection('upu_codeset')
        ->table('refall_katag')
        ->select('jenprog','kodkatag','ketkatag','sorting')
        ->where('jenprog', $request->jenprog)
        ->where('statuskatag', 'Y')
        ->whereNotIn('kodkatag', ['E', 'F', 'G'])
        ->orderBy('sorting','ASC');
		
        $JENIS_KATEGORI2 = DB::connection('upu_codeset')
        ->table('refijzdip_jensetaraf')
        ->where('statuslyksetaraf', 'Y')
        ->select(DB::raw("'stpm' as jenprog"), 'kodjensetaraf AS kodkatag','ketjensetaraf AS ketkatag','sorting');

        if(session()->get('jenprog') == 'stpm')
        { $JENIS_KATEGORI = $JENIS_KATEGORI1->unionAll($JENIS_KATEGORI2)->orderby('sorting','ASC')->get(); }
        else { $JENIS_KATEGORI = $JENIS_KATEGORI1->orderby('sorting','ASC')->get(); }

        return view('kategoriCalon.index', compact('JENIS_KATEGORI'));
    }
}