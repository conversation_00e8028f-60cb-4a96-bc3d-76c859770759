<script>

    $("select#ALIRAN").on("change", function() { 
        var stem_pakej=$(this).val();
        
        if(stem_pakej=='STEMA'){
            document.getElementById("MPP01").selectedIndex='';
            document.getElementById("MPP02").selectedIndex='';
            $('#MPT01').val('').select2();
            $('#MPT02').val('').select2();    
            document.getElementById("GRDGRED5").selectedIndex='';
            document.getElementById("GRDGRED6").selectedIndex='';
            document.getElementById("GRDGRED7").selectedIndex='';
            document.getElementById("GRDGRED8").selectedIndex='';
            $('#MRKKOKO').val('10.00').select2();
            $('#header_merit').hide();

            $("select#ALIRAN option").removeAttr("disabled").show();			
            $("select#MPT01 option").removeAttr("disabled").show();
            $("select#MPT02 option").removeAttr("disabled").show();
            
            $("tr#stem-c").show();  
            $("select[name='MPP01']").val('').show();
            $("select[name='MPP02']").val('').show();
            $("select[name='MPP01']").children().removeAttr("disabled").show().not("option[stem-type='PA'],option[stem-type='info']").attr("disabled","disabled").hide();
            $("select[name='MPP02']").children().removeAttr("disabled").show().not("option[stem-type='PA'],option[stem-type='info']").attr("disabled","disabled").hide();

            $("select[name='MPT01']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
            //$("select#MPT01 option[value='1511']").attr("disabled","disabled").hide();
            //$("select#MPT01 option[value='3472']").attr("disabled","disabled").hide();
            
            $("select[name='MPT02']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
            //$("select#MPT02 option[value='1511']").attr("disabled","disabled").hide();
            //$("select#MPT01 option[value='3472']").attr("disabled","disabled").hide();
            
        }
        else if(stem_pakej=='STEMB'){
            document.getElementById("MPP01").selectedIndex='';
            document.getElementById("MPP02").selectedIndex='';
            $('#MPT01').val('').select2();
            $('#MPT02').val('').select2();    
            document.getElementById("GRDGRED5").selectedIndex='';
            document.getElementById("GRDGRED6").selectedIndex='';
            document.getElementById("GRDGRED7").selectedIndex='';
            document.getElementById("GRDGRED8").selectedIndex='';
            $('#MRKKOKO').val('10.00').select2();
            $('#header_merit').hide();

            $("select#ALIRAN option").removeAttr("disabled").show();
            $("select#MPT01 option").removeAttr("disabled").show();
            $("select#MPT02 option").removeAttr("disabled").show();
            
            $("tr#stem-c").show();  
            $("select[name='MPP01']").val('').show();
            $("select[name='MPP02']").val('').show();
            $("select[name='MPP01']").children().removeAttr("disabled").show().not("option[stem-type='PA'],option[stem-type='PB'],option[stem-type='info']").attr("disabled","disabled").hide();
            $("select[name='MPP02']").children().removeAttr("disabled").show().not("option[stem-type='PA'],option[stem-type='PB'],option[stem-type='info']").attr("disabled","disabled").hide();      

            $("select[name='MPT01']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
            //$("select#MPT01 option[value='1511']").attr("disabled","disabled").hide();
            
            $("select[name='MPT02']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
            //$("select#MPT02 option[value='1511']").attr("disabled","disabled").hide();

        }
        else if(stem_pakej=='STEMC'){
            document.getElementById("MPP01").selectedIndex='';
            document.getElementById("MPP02").selectedIndex='';
            $('#MPT01').val('').select2();
            $('#MPT02').val('').select2();    
            document.getElementById("GRDGRED5").selectedIndex='';
            document.getElementById("GRDGRED6").selectedIndex='';
            document.getElementById("GRDGRED7").selectedIndex='';
            document.getElementById("GRDGRED8").selectedIndex='';
            $('#MRKKOKO').val('10.00').select2();
            $('#header_merit').hide();

            $("select#ALIRAN option").removeAttr("disabled").show();
            $("select#MPT01 option").removeAttr("disabled").show();
            $("select#MPT02 option").removeAttr("disabled").show();
            
            $("tr#stem-c").hide();  
            $("select[name='MPP01']").val('').show();
            $("select[name='MPP02']").val('').show();
            $("select[name='MPP01']").children().removeAttr("disabled").show().not("option[stem-type='PB'],option[stem-type='PC'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
            $("select[name='MPP02']").children().removeAttr("disabled").show().not("option[stem-type='PB'],option[stem-type='PC'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
            
            $("select[name='MPT01']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
            $("select[name='MPT02']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
        }
        else if(stem_pakej=='KSI'){
            document.getElementById("MPP01").selectedIndex='';
            document.getElementById("MPP02").selectedIndex='';
            $('#MPT01').val('').select2();
            $('#MPT02').val('').select2();    
            document.getElementById("GRDGRED5").selectedIndex='';
            document.getElementById("GRDGRED6").selectedIndex='';
            document.getElementById("GRDGRED7").selectedIndex='';
            document.getElementById("GRDGRED8").selectedIndex='';
            $('#MRKKOKO').val('10.00').select2();
            $('#header_merit').hide();

            $("select#ALIRAN option").removeAttr("disabled").show();
            $("select#MPT01 option").removeAttr("disabled").show();
            $("select#MPT02 option").removeAttr("disabled").show();
            
            $("tr#stem-c").show();  
            $("select[name='MPP01']").val('').show();
            $("select[name='MPP02']").val('').show();
            $("select[name='MPP01']").children().removeAttr("disabled").show().not("option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
            $("select[name='MPP02']").children().removeAttr("disabled").show().not("option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
            
            $("select[name='MPT01']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();				
            $("select[name='MPT02']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();			
        }
    });


    $(document).ready(function() 
    {	
        $('#spm01').hide();
        $('#spm02').hide();
        $('#spm03').hide();
        $('#spm04').hide();
        $('#header_merit').hide();
        $('#MRKKOKO').val('10.00').select2();
        
        $("select[name='MPP01']").children().removeAttr("disabled").show().not("option[stem-type='PA'],option[stem-type='info']").attr("disabled","disabled").hide();
        $("select[name='MPP02']").children().removeAttr("disabled").show().not("option[stem-type='PA'],option[stem-type='info']").attr("disabled","disabled").hide();
        
        $("select[name='MPT01']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
        //$("select#MPT01 option[value='1511']").attr("disabled","disabled").hide();
        //$("select#MPT01 option[value='3472']").attr("disabled","disabled").hide();
        
        $("select[name='MPT02']").children().removeAttr("disabled").show().not("option[stem-type='ST'],option[stem-type='PA'],option[stem-type='PB'],option[stem-type='PC'],option[stem-type='KB'],option[stem-type='KP'],option[stem-type='KS'],option[stem-type='PK'],option[stem-type='info']").attr("disabled","disabled").hide();
        //$("select#MPT02 option[value='1511']").attr("disabled","disabled").hide();
        //$("select#MPT01 option[value='3472']").attr("disabled","disabled").hide();
    });

    function hidespm1()
    {
        $('#spm01').hide();
    }

    function hidespm2()
    {
        $('#spm02').hide();
    }
    function hidespm3()
    {
        $('#spm03').hide();
    }
    function hidespm4()
    {
        $('#spm04').hide();
    }

    function kiraSPM()
    {

        var vSUBJEK1 = document.getElementById("MPP01").value;
        var vSUBJEK2 = document.getElementById("MPP02").value;
        var vSUBJEK3 = document.getElementById("MPT01").value;
        var vSUBJEK4 = document.getElementById("MPT02").value;
        
        var vGRED1 = document.getElementById("GRDGRED1").selectedIndex;
        var vGRED2 = document.getElementById("GRDGRED2").selectedIndex;
        var vGRED3 = document.getElementById("GRDGRED3").selectedIndex;
        var vGRED4 = document.getElementById("GRDGRED4").selectedIndex;
        var vGRED5 = document.getElementById("GRDGRED5").selectedIndex;
        var vGRED6 = document.getElementById("GRDGRED6").selectedIndex;
        var vGRED7 = document.getElementById("GRDGRED7").selectedIndex;
        var vGRED8 = document.getElementById("GRDGRED8").selectedIndex;
        var vKOKO = document.getElementById("MRKKOKO").value;

        var stem_pakej = document.getElementById("ALIRAN").value;

        if((vSUBJEK1!='' && vGRED5=='') || (vSUBJEK1=='' && vGRED5!=''))
        {
            $('#spm01').show();
            document.getElementById("spm01").innerHTML='Ruangan Subjek dan Gred perlu diisi sekiranya mengisi salah satu daripada maklumat tersebut'; 
        }
        else if((vSUBJEK2!='' && vGRED6=='') || (vSUBJEK2=='' && vGRED6!=''))
        {
            $('#spm02').show();
            document.getElementById("spm02").innerHTML='Ruangan Subjek dan Gred perlu diisi sekiranya mengisi salah satu daripada maklumat tersebut'; 
        }
        else if((vSUBJEK3!='' && vGRED7=='') || (vSUBJEK3=='' && vGRED7!=''))
        {
            $('#spm03').show();
            document.getElementById("spm03").innerHTML='Ruangan Subjek dan Gred perlu diisi sekiranya mengisi salah satu daripada maklumat tersebut'; 
        }	
        else if((vSUBJEK4!='' && vGRED8=='') || (vSUBJEK4=='' && vGRED8!=''))
        {
            $('#spm04').show();
            document.getElementById("spm04").innerHTML='Ruangan Subjek dan Gred perlu diisi sekiranya mengisi salah satu daripada maklumat tersebut'; 
        }	
        else if(vSUBJEK2!='' && vSUBJEK2==vSUBJEK1  && stem_pakej!='STEMC')
        {
            $('#spm02').show();
            document.getElementById("spm02").innerHTML='Sila pilih subjek yang berbeza.'; 
        }
        else if(vSUBJEK3!='' && ((vSUBJEK3==vSUBJEK2 && stem_pakej!='STEMC') || vSUBJEK3==vSUBJEK1))
        {
            $('#spm03').show();
            document.getElementById("spm03").innerHTML='Sila pilih subjek yang berbeza.'; 
        }
        else if(vSUBJEK4!='' && (vSUBJEK4==vSUBJEK3 || vSUBJEK4==vSUBJEK2 || vSUBJEK4==vSUBJEK1) && stem_pakej!='STEMC')
        {
            $('#spm04').show();
            document.getElementById("spm04").innerHTML='Sila pilih subjek yang berbeza.'; 
        }
        else if(vSUBJEK4!='' && (vSUBJEK4==vSUBJEK3 || vSUBJEK4==vSUBJEK1) && stem_pakej=='STEMC')
        {
            $('#spm04').show();
            document.getElementById("spm04").innerHTML='Sila pilih subjek yang berbeza.'; 
        }
        else
        {
            $('#spm01').hide();
            $('#spm02').hide();
            $('#spm03').hide();
            $('#spm04').hide();
            
            if(vGRED1==1){MPU01 = 18;}
            else if(vGRED1==2){MPU01 = 16;}
            else if(vGRED1==3){MPU01 = 14;}
            else if(vGRED1==4){MPU01 = 12;}
            else if(vGRED1==5){MPU01 = 10;}	
            else if(vGRED1==6){MPU01 = 8;}
            else if(vGRED1==7){MPU01 = 6;}	
            else if(vGRED1==8){MPU01 = 4;}
            else if(vGRED1==9){MPU01 = 2;}
            else {MPU01 = 0;}
            
            if(vGRED2==1){MPU02= 18;}
            else if(vGRED2==2){MPU02= 16;}
            else if(vGRED2==3){MPU02= 14;}
            else if(vGRED2==4){MPU02= 12;}
            else if(vGRED2==5){MPU02= 10;}	
            else if(vGRED2==6){MPU02= 8;}
            else if(vGRED2==7){MPU02= 6;}	
            else if(vGRED2==8){MPU02= 4;}
            else if(vGRED2==9){MPU02= 2;}
            else {MPU02= 0;}
            
            if(vGRED3==1){MPU03 = 18;}
            else if(vGRED3==2){MPU03 = 16;}
            else if(vGRED3==3){MPU03 = 14;}
            else if(vGRED3==4){MPU03 = 12;}
            else if(vGRED3==5){MPU03 = 10;}	
            else if(vGRED3==6){MPU03 = 8;}
            else if(vGRED3==7){MPU03 = 6;}	
            else if(vGRED3==8){MPU03 = 4;}
            else if(vGRED3==9){MPU03 = 2;}
            else {MPU03 = 0;}
            
            if(vGRED4==1){MPU04 = 18;}
            else if(vGRED4==2){MPU04 = 16;}
            else if(vGRED4==3){MPU04 = 14;}
            else if(vGRED4==4){MPU04 = 12;}
            else if(vGRED4==5){MPU04 = 10;}	
            else if(vGRED4==6){MPU04 = 8;}
            else if(vGRED4==7){MPU04 = 6;}	
            else if(vGRED4==8){MPU04 = 4;}
            else if(vGRED4==9){MPU04 = 2;}
            else {MPU04 = 0;}

            if(vGRED5==1){MPP01 = 18;}
            else if(vGRED5==2){MPP01 = 16;}
            else if(vGRED5==3){MPP01 = 14;}
            else if(vGRED5==4){MPP01 = 12;}
            else if(vGRED5==5){MPP01 = 10;}	
            else if(vGRED5==6){MPP01 = 8;}
            else if(vGRED5==7){MPP01 = 6;}	
            else if(vGRED5==8){MPP01 = 4;}
            else if(vGRED5==9){MPP01 = 2;}
            else {MPP01 = 0;}
            
            if(vGRED6==1){MPP02 = 18;}
            else if(vGRED6==2){MPP02 = 16;}
            else if(vGRED6==3){MPP02 = 14;}
            else if(vGRED6==4){MPP02 = 12;}
            else if(vGRED6==5){MPP02 = 10;}	
            else if(vGRED6==6){MPP02 = 8;}
            else if(vGRED6==7){MPP02 = 6;}	
            else if(vGRED6==8){MPP02 = 4;}
            else if(vGRED6==9){MPP02 = 2;}
            else {MPP02 = 0;}

            if(vGRED7==1){MPT01 = 18;}
            else if(vGRED7==2){MPT01 = 16;}
            else if(vGRED7==3){MPT01 = 14;}
            else if(vGRED7==4){MPT01 = 12;}
            else if(vGRED7==5){MPT01 = 10;}	
            else if(vGRED7==6){MPT01 = 8;}
            else if(vGRED7==7){MPT01 = 6;}	
            else if(vGRED7==8){MPT01 = 4;}
            else if(vGRED7==9){MPT01 = 2;}
            else {MPT01 = 0;}
            
            if(vGRED8==1){MPT02 = 18;}
            else if(vGRED8==2){MPT02 = 16;}
            else if(vGRED8==3){MPT02 = 14;}
            else if(vGRED8==4){MPT02 = 12;}
            else if(vGRED8==5){MPT02 = 10;}	
            else if(vGRED8==6){MPT02 = 8;}
            else if(vGRED8==7){MPT02 = 6;}	
            else if(vGRED8==8){MPT02 = 4;}
            else if(vGRED8==9){MPT02 = 2;}
            else {MPT02 = 0;}  

            var MPU_TOTAL = parseFloat(MPU01) + parseFloat(MPU02) + parseFloat(MPU03) + parseFloat(MPU04);
            var MPU_PERCENT = (MPU_TOTAL/72)*40;

            if(stem_pakej=='STEMC'){
                var MPP_TOTAL = parseFloat(MPP01);
                var MPP_PERCENT = (MPP_TOTAL/18)*30;
            }
            else
            {
                var MPP_TOTAL = parseFloat(MPP01) + parseFloat(MPP02);
                var MPP_PERCENT = (MPP_TOTAL/36)*30;
            }
            
            //var MPT_TOTAL = parseFloat(MPT01.toFixed(2)) + parseFloat(MPT02.toFixed(2));
            //var MPT_PERCENT = (MPT_TOTAL/36)*10;
            //var TOTAL=parseFloat(MPU_PERCENT.toFixed(2)) + parseFloat(MPP_PERCENT.toFixed(2)) + parseFloat(MPT_PERCENT.toFixed(2));
            //var CONVERT = (TOTAL/80)*90;
            //var MERIT=parseFloat(CONVERT.toFixed(2)) + parseFloat(vKOKO);
            
            var MPT_TOTAL = parseFloat(MPT01) + parseFloat(MPT02);
            var MPT_PERCENT = (MPT_TOTAL/36)*10;
            var TOTAL=parseFloat(MPU_PERCENT) + parseFloat(MPP_PERCENT) + parseFloat(MPT_PERCENT);
            var CONVERT = (TOTAL/80)*90;
            var MERIT=parseFloat(CONVERT) + parseFloat(vKOKO);
            


            if(stem_pakej=='STEMA'){
                document.getElementById("pakej").innerHTML='Aliran : STEM A'; 
            }
            else if(stem_pakej=='STEMB'){
                document.getElementById("pakej").innerHTML='Aliran : STEM B'; 
            }
            else if(stem_pakej=='STEMC'){
                document.getElementById("pakej").innerHTML='Aliran : STEM C'; 
            }
            else if(stem_pakej=='KSI'){
                document.getElementById("pakej").innerHTML='Aliran : KEMANUSIAAN DAN SASTERA IKHTISAS / LAIN-LAIN'; 
            }

            document.getElementById("merit").innerHTML=parseFloat(Number(MERIT)).toFixed(2) + ' %'; 
            $('#header_merit').show();
        }
        
    }


    function resetSPM()
    {
        document.getElementById("MPP01").selectedIndex='';
        document.getElementById("MPP02").selectedIndex='';
        $('#MPT01').val('').select2();
        $('#MPT02').val('').select2();    
        document.getElementById("GRDGRED1").selectedIndex='';
        document.getElementById("GRDGRED2").selectedIndex='';
        document.getElementById("GRDGRED3").selectedIndex='';
        document.getElementById("GRDGRED4").selectedIndex='';
        document.getElementById("GRDGRED5").selectedIndex='';
        document.getElementById("GRDGRED6").selectedIndex='';
        document.getElementById("GRDGRED7").selectedIndex='';
        document.getElementById("GRDGRED8").selectedIndex='';
        $('#MRKKOKO').val('10.00').select2();
        $('#header_merit').hide();
        $('#spm01').hide();
        $('#spm02').hide();
        $('#spm03').hide();
        $('#spm04').hide();
    }


</script>