@if(count($syaratkhas_ga) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_ga[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_ga[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_ga[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_ga as $syarat_khas_ga)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_ga->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_ga->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
</li>
@endif

@if(count($syaratkhas_gb) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_gb[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_gb[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_gb[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_gb as $syarat_khas_gb)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_gb->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_gb->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
</li>
@endif


{{-- @if(count($syaratkhas_g3) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g3 as $syarat_khas_g3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif --}}

{{-- ############################################################################################### --}}


@if(count($syaratkhas_k1_ga) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_ga[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_ga[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_ga[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k1_ga as $syarat_khas_k1_ga)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k1_ga->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k1_ga->KODSUBJEK_2)) }}</span>
                </div>
            @endforeach
        </div>
    </div>
</li>
@endif


@if(count($syaratkhas_k1_gb) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_gb[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_gb[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_gb[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k1_gb as $syarat_khas_k1_gb)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k1_gb->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k1_gb->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
</li>
@endif

{{-- @if(count($syaratkhas_k1_g3) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k1_g3 as $syarat_khas_k1_g3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_k1_g3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif --}}

@if(count($syaratkhas_k2_ga) > 0 || count($syaratkhas_k2_gb) > 0)
<br>
<p style="text-align:center;"><b>ATAU</b></p>
@endif


@if(count($syaratkhas_k2_ga) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_ga[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_ga[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_ga[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k2_ga as $syarat_khas_k2_ga)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k2_ga->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k2_ga->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
@endif


@if(count($syaratkhas_k2_gb) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_gb[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_gb[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_gb[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k2_gb as $syarat_khas_k2_gb)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k2_gb->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k2_gb->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
@endif

{{-- @if(count($syaratkhas_k2_g3) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k2_g3 as $syarat_khas_k2_g3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_k2_g3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif --}}


@if(count($syaratkhas_k3_ga) > 0 || count($syaratkhas_k3_gb) > 0)
<br>
<p style="text-align:center;"><b>ATAU</b></p>
@endif


@if(count($syaratkhas_k3_ga) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_ga[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_ga[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_ga[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k3_ga as $syarat_khas_k3_ga)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k3_ga->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k3_ga->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
@endif

@if(count($syaratkhas_k3_gb) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_gb[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_gb[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_gb[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k3_gb as $syarat_khas_k3_gb)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k3_gb->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k3_gb->KODSUBJEK_2)) }}</span>
                </div>
            @endforeach
        </div>
    </div>
@endif

{{-- @if(count($syaratkhas_k3_g3) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k3_g3 as $syarat_khas_k3_g3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_k3_g3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif --}}

{{-- ############################################################################################### --}}