//
// bootstrap-custom.scss
//
//li (list inline item)
.list-inline-item:not(:last-child) {
    margin-left: 0px;
    margin-right: auto;
}
.list-unstyled {
    padding-right: 0 !important;
    padding-left: auto !important;
}
.list-inline {
    padding-right: 0;
    list-style: none;
}
//modal
.modal-open {
    padding-left: 0 !important;
}
.input-group>.custom-select:not(:last-child), .input-group>.form-control:not(:last-child) {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

//spinner-border
@keyframes spinner-border {  
    to { 
        transform: rotate(-180deg); 
    }
}

//Dropdown
.dropdown-menu {
    &.show {
        transform: translate3d(0px, 55px, 0px) !important;
    }
}

//margin padding
.ml-auto {
    margin-right: auto;
    margin-left: 0 !important;
}
.mr-auto {
    margin-left: auto;
    margin-right: 0 !important;
}
.mr-1 {
    margin-left: 4px !important;
    margin-right: auto !important;
}
.mr-2 {
    margin-left: 8px !important;
    margin-right: auto !important;
}
.mr-3 {
    margin-left: 16px !important;
    margin-right: auto !important;
}
.mr-4 {
    margin-left: 24px !important;
    margin-right: auto !important;
}
.mr-5 {
    margin-left: 48px !important;
    margin-right: auto !important;
}

.ml-1 {
    margin-right: 4px !important;
    margin-left: auto !important;
}
.ml-2 {
    margin-right: 8px !important;
    margin-left: auto !important;
}
.ml-3 {
    margin-right: 16px !important;
    margin-left: auto !important;
}
.ml-4 {
    margin-right: 24px !important;
    margin-left: auto !important;
}
.ml-5 {
    margin-right: 48px !important;
    margin-left: auto !important;
}

//padding
.pr-1 {
    padding-left: 4px !important;
    padding-right: auto !important;
}
.pr-2 {
    padding-left: 8px !important;
    padding-right: auto !important;
}
.pr-3 {
    padding-left: 16px !important;
    padding-right: auto !important;
}
.pr-4 {
    padding-left: 24px !important;
    padding-right: auto !important;
}
.pr-5 {
    padding-left: 48px !important;
    padding-right: auto !important;
}

.pl-1 {
    padding-right: 4px !important;
    padding-left: auto !important;
}
.pl-2 {
    padding-right: 8px !important;
    padding-left: auto !important;
}
.pl-3 {
    padding-right: 16px !important;
    padding-left: auto !important;
}
.pl-4 {
    padding-right: 24px !important;
    padding-left: auto !important;
}
.pl-5 {
    padding-right: 48px !important;
    padding-left: auto !important;
}
@media (min-width: 992px) {
    .mr-lg-2 {
        margin-left: 8px !important;
        margin-right: auto !important;
    }
    .mr-lg-3 {
        margin-left: 16px !important;
        margin-right: auto !important;
    }
    .mr-lg-4 {
        margin-left: 24px !important;
        margin-right: auto !important;
    }
    .mr-lg-5 {
        margin-left: 48px !important;
        margin-right: auto !important;
    }
    
    .ml-lg-3 {
        margin-right: 16px !important;
        margin-left: auto !important;
    }
    .ml-lg-4 {
        margin-right: 24px !important;
        margin-left: auto !important;
    }
    .ml-lg-5 {
        margin-right: 48px !important;
        margin-left: auto !important;
    }

    .pl-lg-3 {
        padding-right: 16px !important;
        padding-left: auto !important;
    }

    .text-lg-left {
        text-align: right !important;
    }

    .offset-lg-1 {
        margin-left: auto;
        margin-right: 8.333333%;
    }

    .offset-lg-4 {
        margin-right: 33.333333%;
    }
        
    .offset-lg-6 {
        margin-right: 50%;
    }
}

@media (min-width: 768px){
    .ml-md-4 {
        margin-right: 24px !important;
        margin-left: 0 !important;
    }
    .mr-md-4 {
        margin-left: 24px !important;
        margin-right: 0 !important;
    }
    .pr-md-3 {
        padding-left: 16px !important;
        padding-right: 0 !important;
    }
    .pl-md-3 {
        padding-right: 16px !important;
        padding-left: 0 !important;
    }
    .pl-lg-5 {
        padding-right: 48px !important;
        padding-left: 0 !important;
    }
    .text-md-left {
        text-align: right!important;
    }
    .text-md-right {
        text-align: left!important;
    }
}