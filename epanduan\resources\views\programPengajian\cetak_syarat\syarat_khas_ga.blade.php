@if(count($syaratkhas_ga) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_ga[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_ga[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_ga[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_ga as $syarat_khas_ga)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_ga->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div> 
</li>
@endif

@if(count($syaratkhas_gb) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_gb[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_gb[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_gb[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_gb as $syarat_khas_gb)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_gb->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div> 
</li>
@endif

{{-- ############################################################################################### --}}

@if(count($syaratkhas_k1_ga) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_ga[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_ga[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_ga[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k1_ga as $syarat_khas_k1_ga)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k1_ga->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div> 
</li>
@endif


@if(count($syaratkhas_k1_gb) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_gb[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_gb[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_gb[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k1_gb as $syarat_khas_k1_gb)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k1_gb->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div> 
</li>
@endif

@if(count($syaratkhas_k2_ga) > 0 || count($syaratkhas_k2_gb) > 0)
<br>
<p style="text-align:center;"><b>ATAU</b></p>
@endif

@if(count($syaratkhas_k2_ga) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_ga[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_ga[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_ga[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k2_ga as $syarat_khas_k2_ga)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k2_ga->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
@endif


@if(count($syaratkhas_k2_gb) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_gb[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_gb[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_gb[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k2_gb as $syarat_khas_k2_gb)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k2_gb->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div> 
@endif

@if(count($syaratkhas_k3_ga) > 0 || count($syaratkhas_k3_gb) > 0)
<br>
<p style="text-align:center;"><b>ATAU</b></p>
@endif

@if(count($syaratkhas_k3_ga) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_ga[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_ga[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_ga[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k3_ga as $syarat_khas_k3_ga)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k3_ga->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>    
@endif

@if(count($syaratkhas_k3_gb) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_gb[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_gb[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_gb[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k3_gb as $syarat_khas_k3_gb)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k3_gb->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div> 
@endif

{{-- ############################################################################################### --}}