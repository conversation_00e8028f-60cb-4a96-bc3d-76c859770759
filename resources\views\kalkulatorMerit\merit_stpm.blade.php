    <div class="row" style="font-size:.875rem;">

        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
            
            <table class="table table-bordered table-sm" width="100%" cellspacing="0">
               
                <tbody style="color: #000;">
                   
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;"><PERSON><PERSON><PERSON> (PNGK)</td>
    
                        <td width="90px">
                            {!! Form::text('MRKPNGKSTPM', 0, ['id'=>'MRKPNGKSTPM', 'class' => 'form-control form-control-sm maxmin', 'maxlength' => 4, 'min' => 0, 'max' => 4, 'onkeypress'=>'return isNumber(event)', 'style'=>'text-align:center;']) !!}
                        </td>
                    </tr>

                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Jumlah markah akademik (90%)</td>
    
                        <td width="90px">
                            {!! Form::text('MRKMERITSTPM1', 0, ['id'=>'MRKMERITSTPM1', 'class' => 'form-control form-control-sm', 'maxlength' => 4, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
                    <tr style="background:#ffc19a;">
                        <td style="vertical-align: middle; font-weight:bold;">Markah Ko-kurikulum (10%)</td>
    
                        {{-- <td>
                            {!! Form::text('MRKKOKOSTPM', 0, ['id'=>'MRKKOKOSTPM', 'class' => 'form-control form-control-sm maxmin', 'maxlength' => 4, 'min' => 0, 'max' => 10, 'onkeypress'=>'return isNumber(event)', 'style'=>'text-align:center;']) !!}
                        </td> --}}

                        <td>
                            <select name="MRKKOKOSTPM" id="MRKKOKOSTPM" class="form-control form-control-sm subjekspm">
                                @for ($i = 10.00; $i >= 0; $i -= 0.01)
                                    <option value="{{ number_format($i,2)}}">{{ number_format($i,2) }}</option>
                                @endfor
                            </select>
                        </td>

                    </tr>
    
                    <tr style="background:#6ae9fd;">
                        <td colspan="1" style="vertical-align: middle; text-align:right; font-weight:bold;">Jumlah (Markah Merit) (%)</td>
    
                        <td>
                            {!! Form::text('TOTALSTPM1', 0, ['id'=>'TOTALSTPM1', 'class' => 'form-control form-control-sm', 'maxlength' => 4, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent; font-weight:bold;']) !!}
                        </td>
                    </tr>  
    
                </tbody>
            
            </table>
    
        </div>
    </div>


    
    
    
        