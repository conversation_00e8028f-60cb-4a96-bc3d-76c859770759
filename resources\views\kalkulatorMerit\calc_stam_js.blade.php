    <script>
        function kiraSTAM()
        {
            var MARKAH_STAM = document.getElementById("MRKPNGSTAM").value;

            if(MARKAH_STAM=='1'){ JUMA='4.00'; }
            if(MARKAH_STAM=='2'){ JUMA='3.00'; }
            if(MARKAH_STAM=='3'){ JUMA='2.00'; }
            if(MARKAH_STAM=='4'){ JUMA='1.00'; }
            if(MARKAH_STAM=='5'){ JUMA='0.00'; }

            var JUMLAHALLA = (JUMA/4)*90;
            document.getElementById("MRKMERITSTAM1").value=parseFloat(JUMLAHALLA).toFixed(2); 
            
            var JUMB = document.getElementById("MRKKOKOSTAM").value;
            var JUMLAHALLB = parseFloat(JUMLAHALLA) + parseFloat(JUMB);

            document.getElementById("TOTALSTAM1").value=parseFloat(JUMLAHALLB).toFixed(2); 
            
        }

        function btnRESETSTAM()
        {
            document.getElementById("MRKPNGSTAM").selectedIndex='';
            document.getElementById("MRKMERITSTAM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTAM').val('10.00').select2();
            document.getElementById("TOTALSTAM1").value = parseFloat(Number("0.0")).toFixed(2); 
        }

    </script>
    
    <script>
        $(document).ready(function()
        {	
            document.getElementById("MRKPNGSTAM").selectedIndex='';
            document.getElementById("MRKMERITSTAM1").value = parseFloat(Number("0")).toFixed(2); 
            document.getElementById("MRKKOKOSTAM").value = parseFloat(Number("0.0")).toFixed(2); 
            document.getElementById("TOTALSTAM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTAM').val('10.00').select2();
        });
    </script>
