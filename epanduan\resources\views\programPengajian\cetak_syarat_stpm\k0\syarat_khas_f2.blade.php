@foreach ($syaratkhas_f2 as $syarat_khas_f2)

    <li style="padding-left: .3em; margin-bottom:8px;">
        Mendapat sekurang-kurangnya
        @if($PROGRAM->ka<PERSON><PERSON><PERSON>_Pengajian=='T')
        @if($syarat_khas_f2->codeset_tstam == '1') MUMTAZ
        @elseif($syarat_khas_f2->codeset_tstam == '2') JAYYID JIDDAN
        @elseif($syarat_khas_f2->codeset_tstam == '3') JAYYID
        @elseif($syarat_khas_f2->codeset_tstam == '4') MAQBUL
        @elseif($syarat_khas_f2->codeset_tstam == '5') RASIB
        @else TIADA
        @endif
    @else
        Gred <b>{{ $syarat_khas_f2->MINGRED }}</b>
    @endif


        dalam mana-mana <b>{{$syarat_khas_f2->KET_JUMLAH_MIN_SUBJEK}} ({{$syarat_khas_f2->JUMLAH_MIN_SUBJEK}})</b>

        @if($syarat_khas_f2->SUB_KUMPULAN=='F') mata pelajaran
        @elseif($syarat_khas_f2->SUB_KUMPULAN=='X') mata pelajaran yang belum diambil kira
        @elseif ($syarat_khas_f2->SUB_KUMPULAN=='Y') mata pelajaran selain diatas
        @endif

        pada peringkat

        <b>
            @if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') STPM.
            @elseif($PROGRAM->kategori_Pengajian=='T') STAM.
            @else Matrikulasi / Asasi.
            @endif
        </b>
    </li>

@endforeach

