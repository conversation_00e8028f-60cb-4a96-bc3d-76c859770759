
@foreach ($syaratkhas_sk_2 as $syarat_khas_sk_2)
    @if ($loop->first)
        <div style="padding-left: .3em; margin-bottom:8px;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_khas_sk_2->MINGRED_2}}</b> dalam <b>{{$syarat_khas_sk_2->KET_JUMLAH_MIN_SUBJEK_2}} ({{$syarat_khas_sk_2->JUMLAH_MIN_SUBJEK_2}})</b> mata pelajaran <b>DAN</b> Gred <b>{{$syarat_khas_sk_2->MINGRED_2}}</b> dalam <b>{{$syarat_khas_sk_2->KET_JUMLAH_MIN_SUBJEK_2}} ({{$syarat_khas_sk_2->JUMLAH_MIN_SUBJEK_2}})</b> mata pelajaran di peringkat 
            
            <b>
                @if($PROGRAM->kate<PERSON>i_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') STPM
                @elseif($PROGRAM->kategori_Pengajian=='T') STAM
                @elseif($PROGRAM->kategori_Pengajian=='N') Matrikulasi / Asasi
                @elseif($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='J') Matrikulasi
                @else Asasi
                @endif
            </b> :
			<div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
				<table cellpadding="2" width="100%">
					@foreach ($syaratkhas_sk_2 as $syaratkhas_sk2)
						<tr>
							<td style="vertical-align:top;">&#8226;</td>
							<td style="vertical-align:top; width:95%">{{ ucwords(strtolower($syaratkhas_sk2->KODSUBJEK_2)) }}</td>
						</tr>
					@endforeach
				</table>
			</div>
        </div>
    @endif
@endforeach




