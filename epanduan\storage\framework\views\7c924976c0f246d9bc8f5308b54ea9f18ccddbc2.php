<?php if(!empty($syarat_umur[0])): ?>
<li style="padding-left: .3em;">     
    
    <?php $__currentLoopData = $syarat_umur; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $umur): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
        <?php if($loop->first): ?>
            <?php if($umur->Operasi_Umur=='>'): ?>   Calon hendaklah berumur melebihi<b><?php echo e($umur->Umur1); ?> tahun <?php if($umur->bulan1=='0'): ?> <?php else: ?> <?php echo e($umur->bulan1); ?> bulan <?php endif; ?></b> semasa mengemukakan permohonan.
            <?php elseif($umur->Operasi_Umur=='<'): ?>   <PERSON>on hendaklah berumur tidak melebihi <b><?php echo e($umur->Umur1); ?> tahun <?php if($umur->bulan1=='0'): ?>  <?php else: ?> <?php echo e($umur->bulan1); ?> bulan <?php endif; ?></b> semasa mengemukakan permohonan.
            <?php elseif($umur->Operasi_Umur=='B'): ?>   Calon hendaklah berumur diantara<b><?php echo e($umur->Umur1); ?> tahun <?php if($umur->bulan1!='0'): ?> <?php echo e($umur->bulan1); ?> bulan <?php endif; ?> hingga <?php echo e($umur->Umur2); ?> tahun <?php if($umur->bulan2!='0'): ?> <?php echo e($umur->bulan2); ?> bulan <?php endif; ?></b> semasa mengemukakan permohonan.
            <?php endif; ?>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    
    
</li>
<?php endif; ?>

<?php if(!empty($syarat_3M[0])): ?>                       
<?php $__currentLoopData = $syarat_3M; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status_3M): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
	<?php if($loop->first): ?>
		<?php if($status_3M->syarat_3M=='Y'): ?> 
			<li style="padding-left: .3em;">Menduduki SPM.</li>  
			<li style="padding-left: .3em;">Boleh Membaca, Menulis, Mengira.</li> 
		<?php endif; ?>
	<?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>


<?php if(!empty($syarat_kahwin[0])): ?>
<li style="padding-left: .3em;">                           
    <?php $__currentLoopData = $syarat_kahwin; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status_kahwin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
        <?php if($loop->first): ?>
            <?php if($status_kahwin->Taraf_perkahwinan=='B'): ?> Taraf Perkahwinan : <b>Bujang</b> <?php endif; ?>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</li>
<?php endif; ?>

<?php if(!empty($syarat_jantina[0])): ?>
<li style="padding-left: .3em;">                           
    <?php $__currentLoopData = $syarat_jantina; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status_jantina): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
        <?php if($loop->first): ?>
            <?php if($status_jantina->Jantina=='L'): ?>  Jantina : <b>Lelaki</b> <?php elseif($status_jantina->Jantina=='P'): ?> Jantina : <b>Perempuan</b> <?php endif; ?>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</li>
<?php endif; ?>

<?php $__currentLoopData = $program; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $senarai_program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<div>
    <?php if($senarai_program->TEMUDUGA=='Y'): ?>
    <li style="padding-left: .3em;">
    <b>Lulus ujian dan / atau temu duga</b> yang ditetapkan.    
    </li>                  
    <?php endif; ?>
</div>

<div>
    <?php if($senarai_program->OKU=='T'): ?>
    <li style="padding-left: .3em;">
    <b>TIDAK</b> mempunyai kurang upaya fizikal / anggota sehingga menyukarkan kerja-kerja amali.   
    </li>                   
    <?php endif; ?>
</div>

<div>
    <?php if($senarai_program->BUMI=='Y'): ?>
        <li style="padding-left: .3em;">
        Berketurunan Melayu, Anak Negeri Sabah, Anak Negeri Sarawak dan Orang Asli sahaja. 
        </li>                     
    <?php endif; ?>
</div>

<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/cetak_syarat/syarat_lain.blade.php ENDPATH**/ ?>