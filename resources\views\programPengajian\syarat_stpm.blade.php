@foreach ($SENARAI_PROGRAM as $PROGRAM)
    @php
        $sessionSesi = session()->get('sesi_semasa');
        include(app_path() . '/Http/Controllers/include_papar_stpm/jum_aliran.php');

        // KUMPULAN 0
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan0/syarat_1119.php');


        // KUMPULAN 1
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan1/syarat_1119.php');

        // // KUMPULAN 2
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan2/syarat_1119.php');

        // // KUMPULAN 3
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan3/syarat_1119.php');

        // // KUMPULAN 4
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_nn_stpm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_nn_spm.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_g1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_ga.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_g2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_g3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_sk1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f1.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f2.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f3.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f4.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syaratkhas_f5.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syarat_muet.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/kumpulan4/syarat_1119.php');

        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_umur_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_umur.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/program_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/program.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_kahwin_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_kahwin.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_diploma.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_stpm_matrik_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_stpm_matrik.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_stam_0.php');
        include(app_path() . '/Http/Controllers/include_papar_stpm/syarat_stam.php');

    @endphp
@endforeach

{{-- @if($PROGRAM->kategori_Pengajian!='G' && $PROGRAM->kategori_Pengajian!='E' && $PROGRAM->kategori_Pengajian!='F') --}}
  @if(isset($PROGRAM) && $PROGRAM->kategori_Pengajian != 'G' && $PROGRAM->kategori_Pengajian != 'E' && $PROGRAM->kategori_Pengajian != 'F')

    <ol style="padding-left: 2em; line-height: 1.5rem;" style="list-style-type:decimal;">

        @include('programPengajian.cetak_syarat_stpm.syarat_pngk_0')
        @include('programPengajian.cetak_syarat_stpm.syarat_pngk')

        {{-- KUMPULAN 0 --}}
        {{-- ################################################################################################################ --}}


        @if(count($syaratkhas_nn_stpm) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_stpm_nn')
        @endif

        @if(count($syaratkhas_g1) > 0 )
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_g1')
        @endif
{{--
         @if(count($syaratkhas_g1) == 0 && count($syaratkhas_ga) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_ga')
        @endif --}}


          {{-- @if(count($syaratkhas_g1) > 0 && count($syaratkhas_ga) > 0 )
            @if(count($syaratkhas_g1) > 0)
                @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_sk1')
            @endif
        @endif --}}





        @if(count($syaratkhas_f5) > 0)
            @if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5) > 0) && !empty($syaratkhas_f5[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f5')
            @endif
        @endif

        @if(count($syaratkhas_f2) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f2')
        @endif

        @if(count($syaratkhas_f4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f4')
        @endif

        @if(count($syaratkhas_nn_spm) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_spm_nn')
        @endif


        @if(count($syaratkhas_g2) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_g2')
        @endif

        @if(count($syaratkhas_g3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_g3')
        @endif


        @if(count($syaratkhas_f1) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f1')
        @endif

        @if(count($syaratkhas_f3) == '1')
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f3')
        @endif

        @if(count($syarat_muet) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_muet')
        @endif


        {{-- KUMPULAN 1 --}}
        {{-- ################################################################################################################ --}}

        @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) &&  count($syaratkhas_nn_stpm_1) > 0 && $syaratkhas_g1_1[0]->KODSUBJEK_1 == $syaratkhas_g1_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
        @endif


        @if(count($syaratkhas_nn_stpm_1) > 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_stpm_nn')
        @endif


        {{-- SYARAT FLESIBLE 2 STPM DI SUSUN KE ATAS SEKIRANYA HANYA TERDAPAT 1 KUMPULAN SHAJA --}}
        @if(count($syaratkhas_f5_1) > 0)
            @if((count($syaratkhas_f5_1) > 0 && count($syaratkhas_f5_2) > 0) && $syaratkhas_f5_1[0]->KODSUBJEK_1 == $syaratkhas_f5_2[0]->KODSUBJEK_1)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f5')
            @endif
        @endif


        @if(count($syaratkhas_g1_1) > 0 && count($syaratkhas_ga_1) == 0)



            @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) &&  count($syaratkhas_nn_stpm_1) == 0 && $syaratkhas_g1_1[0]->KODSUBJEK_1 == $syaratkhas_g1_2[0]->KODSUBJEK_1)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
            @endif



           {{-- @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && $syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1 && (count($syaratkhas_g1_1) == count($syaratkhas_g1_2)) && count($syaratkhas_g2_2) == 0)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1_0')
            @endif--}}

            @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && $syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1 && (count($syaratkhas_g1_1) == count($syaratkhas_g1_2)) && count($syaratkhas_g2_2) > 0)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
            @endif

            @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && $syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1 && count($syaratkhas_g1_1) != count($syaratkhas_g1_2))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
            @endif

           {{-- @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) == 0) && !empty($syaratkhas_g1_1[0]->KODSUBJEK_1) && count($syaratkhas_nn_stpm_1) > 0)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1_0')
            @endif --}}

            @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) == 0) && !empty($syaratkhas_g1_1[0]->KODSUBJEK_1) && count($syaratkhas_nn_stpm_1) == 0)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
            @endif
            {{-- @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1') --}}

        @endif

        @if(count($syaratkhas_g1_1) == 0 && count($syaratkhas_ga_1) > 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_ga')
        @endif

        {{-- @if(count($syaratkhas_g1_1) > 0 && count($syaratkhas_ga_1) > 0 )
            @if(count($syaratkhas_sk_1) > 0)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_sk1')
            @endif
        @endif --}}

        {{-- trimuntuk error sk1 --}}
        @if(isset($syaratkhas_g1_1) && isset($syaratkhas_ga_1) &&
              count($syaratkhas_g1_1) > 0 && count($syaratkhas_ga_1) > 0)
            @if(isset($syaratkhas_sk_1) && count($syaratkhas_sk_1) > 0)
               @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_sk1')
            @endif
         @endif


        @if(count($syaratkhas_f2_1) > 0)

            @if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) > 0) && $syaratkhas_f2_1[0]->KUMPULAN == $syaratkhas_f2_2[0]->KUMPULAN)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f2')
            @endif

            @if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) == 0) &&  !empty($syaratkhas_f2_1[0]->KUMPULAN))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f2')
            @endif

        @endif

        @if(count($syaratkhas_f4_1) > 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f4')
        @endif


        @if((count($jum_aliran)=='2' && count($syaratkhas_f5_1) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f5_1) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f5_1) =='4'))
            {{-- @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f5_0') --}}
        @endif


        @if(count($syaratkhas_nn_spm_1) > 0)

            @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && $syaratkhas_nn_spm_1[0]->KODSUBJEK_1 != $syaratkhas_nn_spm_2[0]->KODSUBJEK_1)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
            @endif

            @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) == 0) &&  !empty($syaratkhas_nn_spm_1[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
            @endif

            @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) == 0) &&  !empty($syaratkhas_nn_spm_2[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
            @endif

            @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && ($syaratkhas_nn_spm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_spm_2[0]->KODSUBJEK_1) && ($syaratkhas_nn_spm_1[0]->MINGRED != $syaratkhas_nn_spm_2[0]->MINGRED))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
            @endif

        @endif


        @if(count($syaratkhas_g2_1) > 0)

            @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 != $syaratkhas_g2_2[0]->KODSUBJEK_1)
                {{-- @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2_0') --}}
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
            @endif

            @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 == $syaratkhas_g2_2[0]->KODSUBJEK_1 && $syaratkhas_g2_1[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_g2_2[0]->KET_JUMLAH_MIN_SUBJEK)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
            @endif

            @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) == 0 && count($syaratkhas_g1_1) == 0 && count($syaratkhas_g2_1) == 0) && !empty($syaratkhas_g2_1[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2_0')
            @endif

            @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) == 0 && count($syaratkhas_g1_1) == 0 && count($syaratkhas_g2_1) != 0) && !empty($syaratkhas_g2_1[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
            @endif

            @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) == 0 && count($syaratkhas_g1_1) > 0) && !empty($syaratkhas_g2_1[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
            @endif

            @if((count($syaratkhas_g2_1) == 0 && count($syaratkhas_g2_2) > 0) && !empty($syaratkhas_g2_2[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
            @endif

        @endif



        @if(count($syaratkhas_g3_1) > 0)
            @if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) > 0) && $syaratkhas_g3_1[0]->KODSUBJEK_1 != $syaratkhas_g3_2[0]->KODSUBJEK_1)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3')
            @endif

            @if((count($syaratkhas_g3_1) == 0 && count($syaratkhas_g3_2) > 0) && !empty($syaratkhas_g3_2[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3')
            @endif

            @if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) ==  0) && !empty($syaratkhas_g3_1[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3')
            @endif
        @endif


        {{-- @if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) > 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f1')
        @endif
        --}}

        @if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) == 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f1')
        @endif


        @if((count($jum_aliran)=='2' && count($syaratkhas_f3_1) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_1) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_1) =='4'))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f3_0')
        @endif




        @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) && $syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND && $syarat_muet_1[0]->MUET2_Band != $syarat_muet_2[0]->MUET2_Band)


            @if((count($syarat_1119_1) > 0 && count($syarat_1119_2) > 0) &&  $syarat_1119_1[0]->gred_1119 != $syarat_1119_2[0]->gred_1119 )
            @endif

            @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) &&  ($syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND) && (empty($syarat_1119_1[0]->gred_1119) && empty($syarat_1119_2[0]->gred_1119)))
            @endif



            @include('programPengajian.cetak_syarat_stpm.k1.syarat_muet')

        @endif

        {{-- @if((count($syarat_muet_1) == 0 && count($syarat_muet_2) > 0))
            {!! $operator400 !!}
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_muet_0')
        @endif --}}


        {{-- @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) == 0))
            {!! $operator400 !!}
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_muet_0')
        @endif --}}



        {{-- KUMPULAN 2 --}}
        {{-- ################################################################################################################ --}}


        @if(count($jum_aliran) > 1)
            <p style="text-align:center; padding-top:12px;"><b>ATAU</b></p>
        @endif


        @if(count($syaratkhas_nn_stpm_2) > 0)

            @if((count($syaratkhas_nn_stpm_1) > 0 && count($syaratkhas_nn_stpm_2) > 0) && $syaratkhas_nn_stpm_1[0]->KODSUBJEK_1 != $syaratkhas_nn_stpm_2[0]->KODSUBJEK_1)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_stpm_nn')
            @endif

            @if((count($syaratkhas_nn_stpm_1) > 0 && count($syaratkhas_nn_stpm_2) > 0) && $syaratkhas_nn_stpm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_stpm_2[0]->KODSUBJEK_1 && $syaratkhas_nn_stpm_1[0]->MINGRED != $syaratkhas_nn_stpm_2[0]->MINGRED)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_stpm_nn')
            @endif

            @if((count($syaratkhas_nn_stpm_1) == 0 && count($syaratkhas_nn_stpm_2) > 0) &&  !empty($syaratkhas_nn_stpm_2[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_stpm_nn')
            @endif


        @endif


        @if(count($syaratkhas_g1_2) > 0 && count($syaratkhas_ga_2) == 0)

            @if((!empty($syaratkhas_g1_1[1]) && $syaratkhas_g1_1[1]->KODSUBJEK_1 != '') && (!empty($syaratkhas_g1_2[1]) && $syaratkhas_g1_2[1]->KODSUBJEK_1 != ''))

                {{-- @if(count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0)
                    @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
                @endif --}}

                @if(count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0)

                    @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) || (count($syarat_muet_1) == 0 && count($syarat_muet_2) == 0))
                        @if($syarat_muet_1[0]->MUET1_BAND == $syarat_muet_2[0]->MUET1_BAND)
                            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
                        @endif
                    @endif

                @endif

            @else

                @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && ($syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1) && ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK))
                    @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
                @endif

                @if(count($syaratkhas_g1_1) == 0 && count($syaratkhas_g1_2) > 0)
                    @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
                @endif





                @if(count($jum_aliran) == 2 && (count($syaratkhas_g1_1) > 0 && (count($syaratkhas_g1_2) > 0 && count($syaratkhas_g2_2) > 0))  && ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK) && (count($syaratkhas_g1_1)!=count($syaratkhas_g1_2)))
                    @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
                @endif



                {{-- @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && ($syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1) && ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
            @endif

                @if((count($syaratkhas_g1_1) > 0 && (count($syaratkhas_g1_2) > 0 && count($syaratkhas_nn_spm_2) > 0)  && count($syaratkhas_nn_stpm_1) == 0 ))
                    @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
                @endif --}}

                {{-- Code add for duplicate K2 g1 UB6441001 --}}
                @php
                // Reset the flag on every page load
                $term1_has_data = false;
                @endphp

                {{-- Term 1 --}}
                @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) &&
                ($syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1) &&
                ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK))

                 @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')

                 @php
                $term1_has_data = true; // Only affects this page load
                @endphp
                @endif

                {{-- Term 2 (Only execute if Term 1 has no data in this request) --}}
                @if(!$term1_has_data &&
                 (count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0 && count($syaratkhas_nn_spm_2) > 0) &&
                    count($syaratkhas_nn_stpm_1) == 0)

                 @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')

                @endif




            @endif


        @endif

        @if(count($syaratkhas_g1_2) == 0 && count($syaratkhas_ga_2) > 0)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_ga')


        @endif

        @if(count($syaratkhas_g1_2) > 0 && count($syaratkhas_ga_2) > 0 )
            @if(count($syaratkhas_sk_2) > 0)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_sk1')
            @endif

        @endif


        @if(count($syaratkhas_f2_2))
            @if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) > 0) && $syaratkhas_f2_1[0]->KUMPULAN != $syaratkhas_f2_2[0]->KUMPULAN)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2')
            @endif

            @if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) == 0) &&  !empty($syaratkhas_f2_2[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2')
            @endif


            @if(count($jum_aliran) == 2 && (count($syaratkhas_f2_1) == 0 && count($syaratkhas_f2_2) > 0) && count($syaratkhas_g2_2) > 0)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2')
            @endif

            @if((count($syaratkhas_f2_2) > 0 && count($syaratkhas_f2_3) > 0) && $syaratkhas_f2_2[0]->KUMPULAN == $syaratkhas_f2_3[0]->KUMPULAN)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2')
            @endif


        @endif


        @if(count($syaratkhas_f5_2) > 0)
            @if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5_2) > 0) && !empty($syaratkhas_f5_2[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f5')
            @endif

        @endif


        @if(count($syaratkhas_f4_2) > 0)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f4')
        @endif


        @if(count($syaratkhas_nn_spm_2) > 0)

            @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && $syaratkhas_nn_spm_1[0]->KODSUBJEK_1 != $syaratkhas_nn_spm_2[0]->KODSUBJEK_1)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_spm_nn')
            @endif

            @if((count($syaratkhas_nn_spm_1) == 0 && count($syaratkhas_nn_spm_2) > 0) &&  !empty($syaratkhas_nn_spm_2[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_spm_nn')
            @endif

            @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && ($syaratkhas_nn_spm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_spm_2[0]->KODSUBJEK_1) && ($syaratkhas_nn_spm_1[0]->MINGRED != $syaratkhas_nn_spm_2[0]->MINGRED))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_spm_nn')
            @endif


        @endif


        {{-- PAPAR BILA KOMBINASI TUNGGAL BERBEZA : UG6314001  --}}
        @if(count($syaratkhas_g2_1) > 0)

            @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && ($syaratkhas_g2_1[0]->KODSUBJEK_1 == $syaratkhas_g2_2[0]->KODSUBJEK_1) && $syaratkhas_g2_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g2_2[0]->KET_JUMLAH_MIN_SUBJEK)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
            @endif

        @endif


        @if(count($syaratkhas_g2_2) > 0)

            @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 != $syaratkhas_g2_2[0]->KODSUBJEK_1)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g2')
            @endif

            @if((count($syaratkhas_g2_1) == 0 && count($syaratkhas_g2_2) > 0) && !empty($syaratkhas_g2_2[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g2')
            @endif

            @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 == $syaratkhas_g2_2[0]->KODSUBJEK_1 && $syaratkhas_g2_1[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_g2_2[0]->KET_JUMLAH_MIN_SUBJEK)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g2')
            @endif


        @endif

        @if(count($syaratkhas_g3_2) > 0)
            @if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) > 0) && $syaratkhas_g3_1[0]->KODSUBJEK_1 == $syaratkhas_g3_2[0]->KODSUBJEK_1)
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3')
            @endif

            @if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) > 0) && $syaratkhas_g3_1[0]->KODSUBJEK_1 != $syaratkhas_g3_2[0]->KODSUBJEK_1)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g3')
            @endif

            @if((count($syaratkhas_g3_1) == 0 && count($syaratkhas_g3_2) > 0) && !empty($syaratkhas_g3_2[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g3')
            @endif
        @endif

        @if(count($syaratkhas_nn_spm_1) > 0)
            @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && ($syaratkhas_nn_spm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_spm_2[0]->KODSUBJEK_1) && ($syaratkhas_nn_spm_1[0]->MINGRED == $syaratkhas_nn_spm_2[0]->MINGRED))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
            @endif
        @endif

        @if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) > 0 && ($syaratkhas_f1_1[0]->MINGRED == $syaratkhas_f1_2[0]->MINGRED) && ($syaratkhas_f1_1[0]->JUMLAH_MIN_SUBJEK == $syaratkhas_f1_2[0]->JUMLAH_MIN_SUBJEK))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f1')
        @endif

        @if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) > 0 && ($syaratkhas_f1_1[0]->MINGRED == $syaratkhas_f1_2[0]->MINGRED) && ($syaratkhas_f1_1[0]->JUMLAH_MIN_SUBJEK != $syaratkhas_f1_2[0]->JUMLAH_MIN_SUBJEK))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f1')
        @endif

        @if((count($jum_aliran)=='2' && count($syaratkhas_f3_2) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_2) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_2) =='4'))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f3_0')
        @endif



        @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) && $syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND && $syarat_muet_1[0]->MUET2_Band != $syarat_muet_2[0]->MUET2_Band)

            @if((count($syarat_1119_1) > 0 && count($syarat_1119_2) > 0) &&  $syarat_1119_1[0]->gred_1119 != $syarat_1119_2[0]->gred_1119 )

            @endif

            @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) &&  ($syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND) && (empty($syarat_1119_1[0]->gred_1119) && empty($syarat_1119_2[0]->gred_1119)))

            @endif

            @include('programPengajian.cetak_syarat_stpm.k2.syarat_muet')
        @endif



        {{-- KUMPULAN 3 --}}
        {{-- ################################################################################################################ --}}

        @if(count($jum_aliran) > 2)
            <p style="text-align:center; padding-top:12px;"><b>ATAU</b></p>
        @endif


        @if(count($syaratkhas_nn_stpm_3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_stpm_nn')
        @endif


        @if(count($syaratkhas_g1_3) > 0 && count($syaratkhas_ga_3) == 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_g1')

        @endif

        @if(count($syaratkhas_g1_3) == 0 && count($syaratkhas_ga_3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_ga')


        @endif

        @if(count($syaratkhas_g1_3) > 0 && count($syaratkhas_ga_3) > 0 )
            @if(count($syaratkhas_sk_3) > 0)
                @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_sk1')
            @endif


        @endif


        @if(count($syaratkhas_f5_3) > 0)
            @if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5_3) > 0) && !empty($syaratkhas_f5_3[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f5')
            @endif

        @endif

        @if(count($syaratkhas_f2_3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f2')

        @endif

        @if(count($syaratkhas_f4_3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f4')
        @endif


        @if(count($syaratkhas_nn_spm_3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_spm_nn')
        @endif


        @if(count($syaratkhas_g2_3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_g2')

        @endif

        @if(count($syaratkhas_g3_3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_g3')
        @endif


        @if(count($syaratkhas_f1_3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f1')
        @endif

        @if((count($jum_aliran)=='2' && count($syaratkhas_f3_3) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_3) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_3) =='4'))
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f3_0')
        @endif

        @if((count($jum_aliran) > 1 && count($syaratkhas_f3_1) == '1'))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f3')
        @endif



        {{-- KUMPULAN 4 --}}
        {{-- ################################################################################################################ --}}

        @if(count($jum_aliran) > 3)
            <p style="text-align:center; padding-top:12px;"><b>ATAU</b></p>
        @endif


        @if(count($syaratkhas_nn_stpm_4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_stpm_nn')
        @endif

        @if(count($syaratkhas_g1_4) > 0 && count($syaratkhas_ga_4) == 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_g1')


        @endif

        @if(count($syaratkhas_g1_4) == 0 && count($syaratkhas_ga_4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_ga')

        @endif

        @if(count($syaratkhas_g1_4) > 0 && count($syaratkhas_ga_4) > 0 )
            @if(count($syaratkhas_sk_4) > 0)
                @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_sk1')
            @endif
        @endif


        @if(count($syaratkhas_f5_4) > 0)
            @if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5_4) > 0) && !empty($syaratkhas_f5_4[0]->KODSUBJEK_1))
                @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f5')
            @endif
        @endif



        @if(count($syaratkhas_f2_4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f2')



        @endif

        @if(count($syaratkhas_f4_4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f4')
        @endif


        @if((count($jum_aliran)=='2' && count($syaratkhas_f5_4) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f5_4) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f5_4) =='4'))
            {{-- @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f5_0') --}}
        @endif


        @if(count($syaratkhas_nn_spm_4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_spm_nn')
        @endif


        @if(count($syaratkhas_g2_4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_g2')
        @endif

        @if(count($syaratkhas_g3_4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_g3')
        @endif


        @if(count($syaratkhas_f1_4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f1')
        @endif

        @if((count($jum_aliran)=='2' && count($syaratkhas_f3_4) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_4) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_4) =='4'))
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f3_0')
        @endif


        @if((count($jum_aliran) > 1 && count($syaratkhas_f4_1) == '1'))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f3')
        @endif


        @if(count($syarat_muet_1) > 0 || count($syarat_muet_2) > 0 || count($syarat_muet_3) > 0 || count($syarat_muet_4) > 0)

            @if(count($syarat_muet_1) > 0 && count($syarat_muet_2) == 0)

                @if((count($syarat_1119_1) > 0 && count($syarat_1119_2) == 0))
                    @include('programPengajian.cetak_syarat_stpm.k2.syarat_muet')
                @endif

                @if((count($syarat_1119_1) == 0 && count($syarat_1119_2) > 0))
                    @include('programPengajian.cetak_syarat_stpm.k2.syarat_muet')
                @endif

                @if((count($syarat_1119_1) == 0 && count($syarat_1119_2) == 0))
                    @include('programPengajian.cetak_syarat_stpm.k1.syarat_muet')
                @endif

            @endif

            @if((count($syarat_muet_1) == 0 && count($syarat_muet_2) > 0))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_muet')
            @endif

            @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0))
                @if($syarat_muet_1[0]->MUET1_BAND == $syarat_muet_2[0]->MUET1_BAND)
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_muet')
                @endif
            @endif


            {{-- @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_muet')
            @endif --}}

        @endif

        @include('programPengajian.cetak_syarat_stpm.syarat_lain')
        @include('programPengajian.cetak_syarat_stpm.syarat_catatan')

        @include('programPengajian.cetak_syarat_stpm.syarat_lain_0')
        @include('programPengajian.cetak_syarat_stpm.syarat_catatan_0')

    </ol>
@endif

@if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F')
    @foreach ($syarat_diploma as $diploma)
        <span style="font-size:1rem !important; font-weight: normal; color: #000;">
            {!! $diploma->SYARAT !!}
        </span>
    @endforeach
@endif

