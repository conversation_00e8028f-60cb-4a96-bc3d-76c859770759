{{-- SYARAT FLEKSIBEL F1 --}}

@if(SUBSTR($syaratkhas_f1[0]->PROGRAMKOD,-3,1)=='1')
	
<li style="padding-left: .3em;"> 
    {{-- @foreach ($syaratkhas_f1 as $syarat_khas_f1) --}}
        Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_f1[0]->MINGRED}}</b> dalam mana-mana <b>{{$syaratkhas_f1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_f1[0]->JUMLAH_MIN_SUBJEK}})</b> 
		
        @if($syaratkhas_f1[0]->SUB_KUMPULAN=='F') mata pelajaran
        @elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='X') mata pelajaran yang belum diambil kira
        @elseif ($syaratkhas_f1[0]->SUB_KUMPULAN=='Y')  mata pelajaran selain diatas
        @endif
		
    {{-- @endforeach --}}
</li>

@elseif(SUBSTR($syaratkhas_f1[0]->PROGRAMKOD,-3,1)=='2')

	<!-- br -->
	<!-- p style="text-align:center;"><b>ATAU</b></p -->

	Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_f1[0]->MINGRED}}</b> dalam mana-mana <b>{{$syaratkhas_f1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_f1[0]->JUMLAH_MIN_SUBJEK}})</b> 
	
	@if($syaratkhas_f1[0]->SUB_KUMPULAN=='F') mata pelajaran
	@elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='X') mata pelajaran yang belum diambil kira
	@elseif ($syaratkhas_f1[0]->SUB_KUMPULAN=='Y')  mata pelajaran selain diatas
	@endif


@elseif(SUBSTR($syaratkhas_f1[0]->PROGRAMKOD,-3,1)=='3')

	<!-- br -->
	<!-- p style="text-align:center;"><b>ATAU</b></p -->

	Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_f1[0]->MINGRED}}</b> dalam mana-mana <b>{{$syaratkhas_f1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_f1[0]->JUMLAH_MIN_SUBJEK}})</b> 
	
	@if($syaratkhas_f1[0]->SUB_KUMPULAN=='F') mata pelajaran
	@elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='X') mata pelajaran yang belum diambil kira
	@elseif ($syaratkhas_f1[0]->SUB_KUMPULAN=='Y')  mata pelajaran selain diatas
	@endif
	
@else
	
<li style="padding-left: .3em;"> 
    {{-- @foreach ($syaratkhas_f1 as $syarat_khas_f1) --}}
        Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_f1[0]->MINGRED}}</b> dalam mana-mana <b>{{$syaratkhas_f1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_f1[0]->JUMLAH_MIN_SUBJEK}})</b> 
		
        @if($syaratkhas_f1[0]->SUB_KUMPULAN=='F') mata pelajaran
        @elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='X') mata pelajaran yang belum diambil kira
        @elseif ($syaratkhas_f1[0]->SUB_KUMPULAN=='Y')  mata pelajaran selain diatas
        @endif
		
    {{-- @endforeach --}}
</li>


@endif
