
@foreach ($syaratkhas_g1_1 as $syarat_khas_g1_1)
    @if ($loop->first)
        <li style="padding-left: .3em; margin-bottom:8px;">
           Mendapat sekurang-kurangnya Gred <b>{{$syarat_khas_g1_1->MINGRED}}</b> dalam <b>{{$syarat_khas_g1_1->KET_JUMLAH_MIN_SUBJEK}} ({{$syarat_khas_g1_1->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran di peringkat


                 {{-- try  tapi masih tidak filter by kate<PERSON>i hanya papar untuk S--}}


                 @if (!empty($syarat_khas_g1->MINGRED_2) && !empty($syarat_khas_g1->KET_JUMLAH_MIN_SUBJEK_2) && !empty($syarat_khas_g1->JUMLAH_MIN_SUBJEK_2))
                 <b>DAN</b> Gred <b>{{ $syarat_khas_g1->MINGRED_2 }}</b> dalam
                 <b>{{ $syarat_khas_g1->KET_JUMLAH_MIN_SUBJEK_2 }} ({{ $syarat_khas_g1->JUMLAH_MIN_SUBJEK_2 }})</b>
                 mata pelajaran
             @endif

           <b>
                @if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') STPM
                @elseif($PROGRAM->kategori_Pengajian=='T') STAM
                @elseif($PROGRAM->kategori_Pengajian=='N') Matrikulasi / Asasi
                @elseif($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='J') Matrikulasi
                @else Asasi
                @endif
            </b> :
            <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
                <table cellpadding="2" width="100%">
                    @foreach ($syaratkhas_g1_1 as $syarat_khas_g1)
                        <tr>
                            <td style="vertical-align:top;">&#8226;</td>
                            <td style="vertical-align:top; width:95%">{{ ucwords(strtolower($syarat_khas_g1->KODSUBJEK_2)) }}</td>
                        </tr>
                    @endforeach
                </table>
            </div>
        </li>
    @endif
@endforeach

