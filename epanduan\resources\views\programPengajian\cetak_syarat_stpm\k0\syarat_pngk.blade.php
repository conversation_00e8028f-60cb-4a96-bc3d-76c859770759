@if($PROGRAM->kategori_Pengajian=='T')
        @if(!empty($syarat_stam[0]))                                         
            <li style="padding-left: .3em; margin-bottom:8px;">                           
                @foreach ($syarat_stam as $status_stam)                                       
                Mendapat sekurang-kurangnya 
                <b>
                    @if($status_stam->tahap_STAM=='1') MUMTAZ
                    @elseif($status_stam->tahap_STAM=='2') JAYYID JIDDAN
                    @elseif($status_stam->tahap_STAM=='3') JAYYID
                    @elseif($status_stam->tahap_STAM=='4') MAQBUL
                    @elseif($status_stam->tahap_STAM=='5') RASIB
                    @endif
                </b> 
                pada peringkat <b>STAM</b>.                                                                   
                @endforeach
            </li>
            
        @endif
    @else
        @if(!empty($syarat_stpm_matrik[0]))                                         
            <li style="padding-left: .3em; margin-bottom:8px;">                           
                @foreach ($syarat_stpm_matrik as $status_pngk) 									
                Mendapat sekurang-kurangnya <b>PNGK {{$status_pngk->PNGK_STPM}} </b> pada peringkat 																				                                               
                    @if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') <b>STPM</b>. 
                    @elseif($PROGRAM->kategori_Pengajian=='T') <b>STAM</b>. 
                    @elseif($PROGRAM->kategori_Pengajian=='N') <b>Matrikulasi / Asasi</b>. 
                    @elseif($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='J') <b>Matrikulasi</b>. 
                    @else <b>Asasi</b>. 
                    @endif																																											
                @endforeach
            </li>
        @endif
@endif