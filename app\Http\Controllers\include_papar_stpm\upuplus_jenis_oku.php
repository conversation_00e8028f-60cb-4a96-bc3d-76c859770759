<?php
$sessionSesi = session()->get('sesi_semasa');
$sessionJenprog = session()->get('login_jenprog');

$upuplus_jenis_oku = DB::connection('emas')->select(
    "SELECT * FROM (
        SELECT
            SUBSTR(KODPROGRAM, 1, 2) AS kodipta,
            SUBSTR(KODPROGRAM, 1, 9) AS kodprogram,
            KODPROGRAM AS kodprogram2,
            KOD_OKU AS kod_oku,
            SESI AS sesi,
            JENPROG AS jenprog
        FROM program_jenisoku
        WHERE SESI = :sessionSesi
    ) AS TEMP
    WHERE jenprog = :sessionJenprog
    GROUP BY kodprogram, kod_oku",
    [
        'sessionSesi' => $sessionSesi,
        'sessionJenprog' => $sessionJenprog
    ]
);