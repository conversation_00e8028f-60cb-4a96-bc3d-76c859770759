    <script>
        function kiraSTPM()
        {
            var JUMA = document.getElementById("MRKPNGKSTPM").value;

            var JUMLAHALLA = (JUMA/4)*90;
            document.getElementById("MRKMERITSTPM1").value=parseFloat(JUMLAHALLA).toFixed(2); 
            
            var JUMB = document.getElementById("MRKKOKOSTPM").value;
            var JUMLAHALLB = parseFloat(JUMLAHALLA) + parseFloat(JUMB);

            document.getElementById("TOTALSTPM1").value=parseFloat(JUMLAHALLB).toFixed(2); 
            
        }

        function btnRESETSTPM()
        {
            document.getElementById("MRKPNGKSTPM").value = parseFloat(Number("0.0")).toFixed(2); 
            document.getElementById("MRKMERITSTPM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTPM').val('10.00').select2();
            document.getElementById("TOTALSTPM1").value = parseFloat(Number("0.0")).toFixed(2); 
        }

    </script>
    
    <script>
        $(document).ready(function()
        {	
            document.getElementById("MRKPNGKSTPM").value = parseFloat(Number("0.0")).toFixed(2); 
            document.getElementById("MRKMERITSTPM1").value = parseFloat(Number("0")).toFixed(2); 
            document.getElementById("MRKKOKOSTPM").value = parseFloat(Number("0.0")).toFixed(2); 
            document.getElementById("TOTALSTPM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTPM').val('10.00').select2();
        });
    </script>

    <script type="text/javascript">
        $(".maxmin").each(function () {

        var thisJ = $(this);
        var max = thisJ.attr("max") * 1;
        var min = thisJ.attr("min") * 1;
        var intOnly = String(thisJ.attr("intOnly")).toLowerCase() == "true";

        var test = function (str) {
            return str == "" || /* (!intOnly && str == ".") || */
                ($.isNumeric(str) && str * 1 <= max && str * 1 >= min &&
                (!intOnly || str.indexOf(".") == -1) && str.match(/^0\d/) == null);
                // commented out code would allow entries like ".7"
        };

        thisJ.keydown(function () {
            var str = thisJ.val();
            if (test(str)) thisJ.data("dwnval", str);
        });

        thisJ.keyup(function () {
            var str = thisJ.val();
            if (!test(str)) thisJ.val(thisJ.data("dwnval"));
        })
        });
    </script>