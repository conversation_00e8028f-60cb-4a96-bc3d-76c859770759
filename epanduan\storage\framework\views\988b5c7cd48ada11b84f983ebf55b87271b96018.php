<!-- Modal Maklumat Program Pengajian -->
<div class="modal fade" id="maklumatProgram__<?php echo e($PROGRAM->kod_Program); ?>" tabindex="-1" role="dialog"
    aria-labelledby="login-title" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
            <div class="modal-body p-0">
                <div class="container-fluid py-5">
                    <h4 class="text-center">Maklumat Program</h4>
                    <div class="row align-items-center no-gutters">
                        <?php $__currentLoopData = $MAKLUMAT_PENGAJIAN; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $maklumat_Pengajian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($maklumat_Pengajian->kod_Program == $PROGRAM->kod_Program): ?>
                                <div class="col-lg-6 col-md-5">
                                    <form class="login-form p-4">
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-graduation-cap"
                                                style="color: #4579E7"></span>
                                            Peringkat Pengajian :
                                            <b><?php echo e(Str::title($maklumat_Pengajian->keterangan_peringkat)); ?></b>
                                        </p>
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-tags"
                                                style="color: #4579E7"></span> Mod
                                            Pengajian :
                                            <?php if($maklumat_Pengajian->mod2u2i == 'Y'): ?>
                                                <b>2U2I/3U1I</b>
                                            <?php else: ?>
                                                <b>Konvensional</b>
                                            <?php endif; ?>
                                        </p>
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-hourglass-end"
                                                style="color: #4579E7"></span>
                                            Tempoh Pengajian :
											<b>
											<?php echo e(Str::title($maklumat_Pengajian->tempoh_Pengajian)); ?> 
											<?php if($maklumat_Pengajian->tempoh=='01'): ?> Semester <?php endif; ?>
											<?php if($maklumat_Pengajian->tempoh=='02'): ?> Tahun <?php endif; ?>
											<?php if($maklumat_Pengajian->tempoh=='03'): ?> Bulan <?php endif; ?>
											</b>
                                        </p>
                                        
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-sitemap"
                                                style="color: #4579E7"></span> Bidang
                                            NEC :
                                            <b><?php echo e(Str::title($maklumat_Pengajian->bidang_NEC)); ?>

                                                <?php echo e(Str::title($maklumat_Pengajian->keterangan_bidangNEC)); ?></b>
                                        </p>
                                    </form>
                                </div>
                                <!--end col-->

                                <div class="col-lg-6 col-md-7">
                                    <form class="login-form p-4">
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-hashtag"
                                                style="color: #4579E7"></span> Bertemu
                                            duga / Ujian:
                                            <?php if($maklumat_Pengajian->program_Temuduga == 'Y'): ?>
                                                <b>Ya</b>
                                            <?php else: ?>
                                                <b>Tidak</b>
                                            <?php endif; ?>
                                        </p>
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-certificate"
                                                style="color: #4579E7"></span>
                                            Joint / Dual / Double Degree :
                                            <?php if($maklumat_Pengajian->double_Degree == 'Y'): ?>
                                                <b>Ya</b>
                                            <?php else: ?>
                                                <b>Tidak</b>
                                            <?php endif; ?>
                                        </p>
										
										<?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-bar-chart"
                                                style="color: #4579E7"></span>
                                            Purata Markah Merit:

                                            <?php if($maklumat_Pengajian->kategori_Pengajian == 'T'): ?>
                                            <b><?php if($maklumat_Pengajian->merit_Program==''): ?> Tiada (Program Baru) <?php else: ?> <?php echo $maklumat_Pengajian->merit_Program; ?>% <?php endif; ?></b>
                                            <?php endif; ?>

                                            <?php if($maklumat_Pengajian->kategori_Pengajian != 'T'): ?>
                                            <b><?php if($maklumat_Pengajian->merit_Program==''): ?> Tiada (Program Baru) <?php else: ?> <?php echo $maklumat_Pengajian->merit_Program; ?>% <?php endif; ?></b>
                                            <?php endif; ?>

                                        </p>
										<?php endif; ?>
										
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-sticky-note"
                                                style="color: #4579E7"></span>
                                            Catatan:
                                            <?php if(is_null($maklumat_Pengajian->catatan_Program) || empty($maklumat_Pengajian->catatan_Program)): ?>
                                                <b>Tidak Berkenaan</b>
                                            <?php else: ?>
                                                <b><?php echo $maklumat_Pengajian->catatan_Program; ?></b>
                                            <?php endif; ?>
                                        </p>
                                    </form>
                                </div>
                                <!--end col-->
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
					
					<?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
					<div style="color:red;">Penafian : Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata. Kejayaan mendapat tawaran bergantung kepada kedudukan merit, memenuhi syarat am dan syarat khas, bilangan tempat yang disediakan dan lulus temu duga dan/atau ujian bagi program pengajian yang menetapkan keperluan berkenaan.</div>
                    <?php endif; ?>
					
					<!--end row-->
                </div>
                <!--end container-->
            </div>
        </div>
    </div>
</div>

<!-- Modal Kampus @ Pusat Latihan-->
<?php if(substr($PROGRAM->kod_Program, 0, 1) === 'U'): ?>
    <div class="modal fade" id="kampus__<?php echo e($PROGRAM->kod_Program); ?>" tabindex="-1" role="dialog"
        aria-labelledby="kampus-title" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
                <div class="modal-body py-5">
                    <div class="text-left">
                        <h4 class="text-center">Kampus</h4>
                        <?php $__currentLoopData = $SENARAI_KAMPUS; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Kampus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($Kampus->KOD == $PROGRAM->kod_Program): ?>
                                <b class="text-muted"> <br>- <?php echo e(Str::title($Kampus->lokasiKampus)); ?>,
                                    <?php echo Str::title($Kampus->negeri); ?> </b>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <?php if(session()->get('jenprog') == 'spm' && substr($PROGRAM->kod_Program, 0, 1) != 'U'): ?>
                        <div class="text-left" style="padding-top: 3rem; margin-bottom: -2rem">
                            <p class="para-desc text-muted mb-1"><b>Rujukan Fasiliti di Politeknik, Kolej Komuniti &
                                    ILKA
                                    Sahaja</b></p>
                            <p class="para-desc text-muted mb-1" style="text-indent: 0.3rem"><span
                                    class="mdi mdi-bed-empty" style="color: black"></span> Asrama disediakan</p>

                            <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                    class="mdi mdi-gender-male" style="color: #4579E7"></span> Hanya Asrama lelaki
                                disediakan</p>

                            <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                    class="mdi mdi-gender-female" style="color: #E775BF"></span> Hanya Asrama perempuan
                                disediakan
                            </p>

                            <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                    class="mdi mdi-wheelchair-accessibility" style="color: #4579E7"></span> Kampus mesra
                                OKU
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="modal fade" id="kampus__<?php echo e($PROGRAM->kod_Program); ?>" tabindex="-1" role="dialog"
        aria-labelledby="kampus-title" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
                <div class="modal-body py-5">
                    <div class="text-left">
                        <h4 class="text-center">Kampus</h4>
                        <?php $__currentLoopData = $PUSAT_LATIHAN; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pusatLatihan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($pusatLatihan->kodkursus == $PROGRAM->kod_Program): ?>
                                <b class="text-muted"> <br>- <?php echo e(Str::title($pusatLatihan->ketpusat)); ?> </b>
                                <?php if($pusatLatihan->asrama == 'Y'): ?>
                                    <span class="mdi mdi-bed-empty"></span>
                                <?php endif; ?>

                                <?php if($pusatLatihan->jantinakhas == 'L'): ?>
                                    <span class="mdi mdi-gender-male" style="color: #4579E7"></span>
                                <?php elseif($pusatLatihan->jantinakhas == 'P'): ?>
                                    <span class="mdi mdi-gender-female" style="color: #E775BF"></span>
                                <?php endif; ?>

                                <?php if($pusatLatihan->mesraoku == 'Y'): ?>
                                    <span class="mdi mdi-wheelchair-accessibility" style="color: #4579E7"></span>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="text-left" style="padding-top: 3rem; margin-bottom: -2rem">
                        <p class="para-desc text-muted mb-1"><b>Rujukan Fasiliti di Politeknik, Kolej Komuniti & ILKA
                                Sahaja</b></p>
                        <p class="para-desc text-muted mb-1" style="text-indent: 0.3rem"><span class="mdi mdi-bed-empty"
                                style="color: black"></span> Asrama disediakan</p>

                        <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                class="mdi mdi-gender-male" style="color: #4579E7"></span> Hanya Asrama lelaki
                            disediakan
                        </p>

                        <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                class="mdi mdi-gender-female" style="color: #E775BF"></span> Hanya Asrama perempuan
                            disediakan</p>

                        <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                class="mdi mdi-wheelchair-accessibility" style="color: #4579E7"></span> Kampus mesra
                            OKU</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Modal Yuran Pengajian-->
<div class="modal fade" id="yuran-pengajian" tabindex="-1" role="dialog" aria-labelledby="yuran-pengajian-title"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
            <div class="modal-body py-5">
                <div class="text-left">
                    <h4 class="text-center">Yuran Pengajian</h4>
                    <p class="text-muted" style="text-align: left">
					<b class="text-muted">	
					Yuran Pengajian adalah ditetapkan oleh pihak UA/IPTA. Maklumat yuran akan dipaparkan di dalam Surat Tawaran Rasmi yang akan dikeluarkan oleh UA/IPTA masing-masing.
												
					</b>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Syarat Program Pengajian -->


<!-- Modal Laluan Kerjaya-->
<div class="modal fade" id="laluan-kerjaya_<?php echo e($PROGRAM->kod_Program); ?>" tabindex="-1" role="dialog"
    aria-labelledby="yuran-pengajian-title" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
            <div class="modal-body py-5">
                <div class="text-left">
                    <h4 class="text-center">Laluan Kerjaya</h4>
                    <p class="text-muted" style="text-align: left"><br>
                        <?php $__currentLoopData = $LALUAN_KERJAYA; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $laluanKerjaya): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($laluanKerjaya->kod_Kerjaya == $PROGRAM->kod_Program): ?>
                                <b class="text-muted mb-0"><?php echo $laluanKerjaya->program_Kerjaya; ?></b>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if(session()->get('jenprog') == 'stpm'): ?>
<!-- Modal Bidang NEC-->
<div class="modal fade" id="bidangNEC_<?php echo e($PROGRAM->kod_Program); ?>" tabindex="-1" role="dialog"
    aria-labelledby="yuran-pengajian-title" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
            <div class="modal-body py-5">
                <div class="text-left">
                    <h5 class="text-center">Bidang NEC yang layak dipertimbangkan</h5>
                    <p class="text-muted" style="text-align: left"><br>

                        <?php $showErrorMessage = 0; ?>
                        <?php $__currentLoopData = $BIDANG_NEC; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bidang_nec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($bidang_nec->kod_program == $PROGRAM->kod_Program && $bidang_nec->jensetaraf == Request::route('kodkatag')): ?>
                                <?php $__currentLoopData = $CODESET_NEC; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $nec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($bidang_nec->bidangnec == $nec->kod): ?>
                                        <?php $showErrorMessage = 1;?>
                                        <b class="text-muted mb-0"><?php echo e($nec->kod); ?></b>  - <?php echo e($nec->ket_bidang_bm); ?> <br>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <?php if($showErrorMessage == 0): ?>
                         <?php echo e('TAWAR SEMUA BIDANG'); ?>

                        <?php endif; ?>

                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/modal.blade.php ENDPATH**/ ?>