

<?php if(SUBSTR($syaratkhas_f1[0]->PROGRAMKOD,-3,1)=='1'): ?>
	
<li style="padding-left: .3em;"> 
    
        Mendapat sekurang-kurangnya Gred <b><?php echo e($syaratkhas_f1[0]->MINGRED); ?></b> dalam mana-mana <b><?php echo e($syaratkhas_f1[0]->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syaratkhas_f1[0]->JUMLAH_MIN_SUBJEK); ?>)</b> 
		
        <?php if($syaratkhas_f1[0]->SUB_KUMPULAN=='F'): ?> mata pelajaran
        <?php elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='X'): ?> mata pelajaran yang belum diambil kira
        <?php elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='Y'): ?>  mata pelajaran selain diatas
        <?php endif; ?>
		
    
</li>

<?php elseif(SUBSTR($syaratkhas_f1[0]->PROGRAMKOD,-3,1)=='2'): ?>

	<!-- br -->
	<!-- p style="text-align:center;"><b>ATAU</b></p -->

	Mendapat sekurang-kurangnya Gred <b><?php echo e($syaratkhas_f1[0]->MINGRED); ?></b> dalam mana-mana <b><?php echo e($syaratkhas_f1[0]->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syaratkhas_f1[0]->JUMLAH_MIN_SUBJEK); ?>)</b> 
	
	<?php if($syaratkhas_f1[0]->SUB_KUMPULAN=='F'): ?> mata pelajaran
	<?php elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='X'): ?> mata pelajaran yang belum diambil kira
	<?php elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='Y'): ?>  mata pelajaran selain diatas
	<?php endif; ?>


<?php elseif(SUBSTR($syaratkhas_f1[0]->PROGRAMKOD,-3,1)=='3'): ?>

	<!-- br -->
	<!-- p style="text-align:center;"><b>ATAU</b></p -->

	Mendapat sekurang-kurangnya Gred <b><?php echo e($syaratkhas_f1[0]->MINGRED); ?></b> dalam mana-mana <b><?php echo e($syaratkhas_f1[0]->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syaratkhas_f1[0]->JUMLAH_MIN_SUBJEK); ?>)</b> 
	
	<?php if($syaratkhas_f1[0]->SUB_KUMPULAN=='F'): ?> mata pelajaran
	<?php elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='X'): ?> mata pelajaran yang belum diambil kira
	<?php elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='Y'): ?>  mata pelajaran selain diatas
	<?php endif; ?>
	
<?php else: ?>
	
<li style="padding-left: .3em;"> 
    
        Mendapat sekurang-kurangnya Gred <b><?php echo e($syaratkhas_f1[0]->MINGRED); ?></b> dalam mana-mana <b><?php echo e($syaratkhas_f1[0]->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syaratkhas_f1[0]->JUMLAH_MIN_SUBJEK); ?>)</b> 
		
        <?php if($syaratkhas_f1[0]->SUB_KUMPULAN=='F'): ?> mata pelajaran
        <?php elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='X'): ?> mata pelajaran yang belum diambil kira
        <?php elseif($syaratkhas_f1[0]->SUB_KUMPULAN=='Y'): ?>  mata pelajaran selain diatas
        <?php endif; ?>
		
    
</li>


<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/cetak_syarat/syarat_khas_f1.blade.php ENDPATH**/ ?>