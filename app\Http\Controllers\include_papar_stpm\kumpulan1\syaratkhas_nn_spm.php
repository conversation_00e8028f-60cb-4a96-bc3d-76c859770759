<?php
$syaratkhas_nn_spm_1  = DB::connection('emas')->select("SELECT * FROM 
(   
SELECT
    a.PROGRAMKOD AS PROGRAMKOD,
    CASE WHEN SUBSTR(PROGRAMKOD, -3, 1) IN ('1', '2', '3', '4') THEN SUBSTR(PROGRAMKOD, -3, 1) ELSE 'X' END AS ALIRAN,
    a.<PERSON><PERSON><PERSON>BJEK AS KODSUBJEK_1,
    c.<PERSON><PERSON><PERSON><PERSON><PERSON>ARAN AS KODSUBJEK_2,
    a.MINGRED AS MINGRED,
    a.KUMPULAN AS KUMPULAN,
    a.SUB_<PERSON>UMPULAN AS SUB_KUMPULAN,
    CASE
        WHEN a.JUMLAH_MIN_SUBJEK = '1'
        THEN 'SATU'
        WHEN a.JUMLAH_MIN_SUBJEK = '2'
        THEN 'DUA'
        WHEN a.JUMLAH_MIN_SUBJEK = '3'
        THEN 'TIGA'
        WHEN a.JUMLAH_MIN_SUBJEK = '4'
        THEN 'EMPAT'
        WHEN a.JUMLAH_MIN_SUBJEK = '5'
        THEN 'LIMA'
        WHEN a.JUMLAH_MIN_SUBJEK = '6'
        THEN 'ENAM'
        WHEN a.JUMLAH_MIN_SUBJEK = '7'
        THEN 'TUJUH'
        WHEN a.JUMLAH_MIN_SUBJEK = '8'
        THEN 'LAPAN'
        ELSE 'SEMBILAN'
    END AS KET_JUMLAH_MIN_SUBJEK,
    a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
    a.SESI AS SESI,
    a.ORDERID AS ORDERID,
    NULL AS ORDERID2
    FROM emas.syarat_khas_stpm a
    LEFT JOIN upuplus_all_subjek c ON (a.KODSUBJEK = c.KOD)
    WHERE a.KUMPULAN = 'N'
    AND a.SUB_KUMPULAN = 'N'
    AND a.KODSUBJEK NOT LIKE 'K-%'
    AND a.SESI = '$sessionSesi'
    AND a.KATEGORI = 'SPM'
    
    UNION ALL

    SELECT
    a.PROGRAMKOD AS PROGRAMKOD,
    CASE WHEN SUBSTR(PROGRAMKOD, -3, 1) IN ('1', '2', '3', '4') THEN SUBSTR(PROGRAMKOD, -3, 1) ELSE 'X' END AS ALIRAN,
    a.KODSUBJEK AS KODSUBJEK_1,
    GROUP_CONCAT(c.MATAPELAJARAN ORDER BY b.ORDERID ASC SEPARATOR ' / ') AS KODSUBJEK_2,
    a.MINGRED AS MINGRED,
    a.KUMPULAN AS KUMPULAN,
    a.SUB_KUMPULAN AS SUB_KUMPULAN,
    CASE
        WHEN a.JUMLAH_MIN_SUBJEK = '1'
        THEN 'SATU'
        WHEN a.JUMLAH_MIN_SUBJEK = '2'
        THEN 'DUA'
        WHEN a.JUMLAH_MIN_SUBJEK = '3'
        THEN 'TIGA'
        WHEN a.JUMLAH_MIN_SUBJEK = '4'
        THEN 'EMPAT'
        WHEN a.JUMLAH_MIN_SUBJEK = '5'
        THEN 'LIMA'
        WHEN a.JUMLAH_MIN_SUBJEK = '6'
        THEN 'ENAM'
        WHEN a.JUMLAH_MIN_SUBJEK = '7'
        THEN 'TUJUH'
        WHEN a.JUMLAH_MIN_SUBJEK = '8'
        THEN 'LAPAN'
        ELSE 'SEMBILAN'
    END AS KET_JUMLAH_MIN_SUBJEK,
    a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
    a.SESI AS SESI,
    a.ORDERID AS ORDERID,
    b.ORDERID AS ORDERID2
    FROM emas.syarat_khas_stpm a
    LEFT JOIN emas.syarat_kesetaraan_stpm b ON (a.KODSUBJEK = b.PARENT_KUMPULAN_KOD AND a.SESI = b.SESI)
    LEFT JOIN upuplus_all_subjek c ON (b.KOD = c.KOD)
    WHERE a.KUMPULAN = 'N'
    AND a.SUB_KUMPULAN = 'N'
    AND a.KODSUBJEK LIKE 'K-%'
    AND a.SESI = '$sessionSesi'
    AND a.KATEGORI = 'SPM'
    GROUP BY a.PROGRAMKOD,a.KODSUBJEK
    ) AS temp 
    WHERE PROGRAMKOD LIKE '$PROGRAM->kod_Program%' 
    AND PROGRAMKOD LIKE '%$PROGRAM->kategori_Pengajian)'
    AND SUBSTR(PROGRAMKOD, -3, 1) = '1'
    GROUP BY SUBSTR(PROGRAMKOD, 1, 9),SUBSTR(PROGRAMKOD, -2, 1), KODSUBJEK_1
    ORDER BY ORDERID ASC");
    
     