<?php
$cetak = DB::connection('emas')->table('program')
->select(
    'KODPROGRAM_PAPAR AS kodprogram',
    'KATEGORI AS kategori',
    DB::raw("CONCAT(KODPROGRAM_PAPAR, '(', KATEGOR<PERSON>, ')') AS kodprogram2"),
    'LEPASAN AS lepasan',
    'NAMAPROGRAM AS program',
    'KODUNIVERSITI AS kodipta',
    'BILSEMESTER AS semester',
    'JENISTEMPOH AS tempoh',
    'YURAN AS yuran',
    'OKU AS status_oku',
    'BUMI AS bumiputera',
    'TEMUDUGA AS tduga',
    'STATUS_TAWAR AS status_tawar',
    'STATUS_RAYU AS status_rayu',
    'JENIS_PENGAJIAN AS jenis_pengajian',
    'REMARKS AS catatan'
)
->where('sesi', '=', $sessionSesi)
->where('STATUS_TAWAR', '=', 'Y')
->whereIn('kategori', $kat)
->where('lepasan', session()->get('login_jenprog'))
->when((in_array(Auth::user()->ipta, ['11', '22','FA','OI','OP','IA'])) && session()->get('ipta') != '', function ($q) {
    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'like', '%'.session()->get('ipta').'%')
            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
})
->when((in_array(Auth::user()->ipta, ['11', '22'])) && session()->get('ipta') == '', function ($q) {
    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'UA')
            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
}) 

->when((in_array(Auth::user()->ipta, ['FA'])) && session()->get('ipta') == '', function ($q) {
    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'FB')
            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
})
->when((in_array(Auth::user()->ipta, ['OI'])) && session()->get('ipta') == '', function ($q) {
    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OI')
            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
})
->when((in_array(Auth::user()->ipta, ['OP'])) && session()->get('ipta') == '', function ($q) {
    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'OP')
            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
})
->when((in_array(Auth::user()->ipta, ['IA'])) && session()->get('ipta') == '', function ($q) {
    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), 'IA')
            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
})
->when(!in_array(Auth::user()->ipta, ['11','22','FA','OI','OP','IA']), function ($q) {
    return $q->where(DB::raw('substr(KODPROGRAM_PAPAR, 1, 2)'), Auth::user()->ipta)
            ->where('KODPROGRAM_PAPAR', 'like', '%'.session()->get('program').'%');
})
->orderby('kodprogram', 'ASC')
->orderby('kategori', 'ASC')
->groupby('kodprogram', 'kategori') // Grouping by both kodprogram and kategori
->get();
