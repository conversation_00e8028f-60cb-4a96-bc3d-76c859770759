var selectedCategory = '';
var selectedProgramCategory = '';

document.addEventListener("DOMContentLoaded", function () {
    document.getElementById('carianKategori').addEventListener('change', function () {
        selectedCategory = this.value;
        var kategoriProgram = document.getElementById('kategoriProgram');
        kategoriProgram.innerHTML = "";

        switch (selectedCategory) {
            case 'spm':
                kategoriProgram.appendChild(new Option('-- Pilih kategori --', ''));
                kategoriProgram.appendChild(new Option('SPM TAHUN SEMASA', 'A'));
                kategoriProgram.appendChild(new Option('SPM BUKAN TAHUN SEMASA', 'B'));
                break;

            case 'stpm':
                kategoriProgram.appendChild(new Option('-- Pilih kategori --', ''));
                kategoriProgram.appendChild(new Option('A-LEVEL', 'F1'));
                kategoriProgram.appendChild(new Option('AUSMAT / SAM / CPU', 'F7'));
                kategoriProgram.appendChild(new Option('ASASI (KEJURUTERAAN)', 'K'));
                kategoriProgram.appendChild(new Option('ASASI (TESL)', 'L'));
                kategoriProgram.appendChild(new Option('ASASI (UNDANG-UNDANG)', 'U'));
                kategoriProgram.appendChild(new Option('ASASI (SAINS SOSIAL)', 'M'));
                kategoriProgram.appendChild(new Option('ASASI (TVET POLITEKNIK)', 'V'));
                kategoriProgram.appendChild(new Option('DKM / DLKM', 'E1'));
                kategoriProgram.appendChild(new Option('DIPLOMA ILKA / IPTS / LUAR NEGARA / LAIN-LAIN DIPLOMA', 'E3'));
                kategoriProgram.appendChild(new Option('DIPLOMA POLITEKNIK', 'G2'));
                kategoriProgram.appendChild(new Option('DIPLOMA VOKASIONAL MALAYSIA (DVM)', 'E2'));
                kategoriProgram.appendChild(new Option('DIPLOMA UA', 'G1'));
                kategoriProgram.appendChild(new Option('FOUNDATION IPTS', 'F8'));
                kategoriProgram.appendChild(new Option('INTERNATIONAL BACCALAUREATE DIPLOMA (IBD)', 'F2'));
                kategoriProgram.appendChild(new Option('MATRIKULASI (KEJURUTERAAN)', 'J'));
                kategoriProgram.appendChild(new Option('MATRIKULASI (PERAKAUNAN)', 'P'));
                kategoriProgram.appendChild(new Option('MATRIKULASI / ASASI (SAINS)', 'N'));
                kategoriProgram.appendChild(new Option('MATRIKULASI / ASASI BUKAN TAHUN SEMASA', 'F5'));
                kategoriProgram.appendChild(new Option('SEKOLAH SUKAN', 'F3'));
                kategoriProgram.appendChild(new Option('STAM BUKAN TAHUN 2022 / 2021', 'F6'));
                kategoriProgram.appendChild(new Option('STAM TAHUN SEMASA', 'T'));
                kategoriProgram.appendChild(new Option('STPM BUKAN TAHUN 2022', 'F4'));
                kategoriProgram.appendChild(new Option('STPM TAHUN SEMASA (SASTERA)', 'A'));
                kategoriProgram.appendChild(new Option('STPM TAHUN SEMASA (SAINS)', 'S'));
                break;

            default:
                kategoriProgram.appendChild(new Option('-- Pilih kategori --', ''));
                break;
        }
    });

    document.getElementById('kategoriProgram').addEventListener('change', function () {
        selectedProgramCategory = this.value;
    });

    document.getElementById('carian_TawaranProgram').addEventListener('submit', function () {
        document.getElementById('selectedCategory').value = selectedCategory;
        document.getElementById('selectedProgramCategory').value = selectedProgramCategory;
    });
});
