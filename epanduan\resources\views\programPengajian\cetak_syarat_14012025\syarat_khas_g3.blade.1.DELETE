

@if(count($syarat_khas_yn_g3) > 0)

    @foreach ($syarat_khas_yn_g3 as  $syarat_g3)
    @if ($loop->first)
        @php

            $syarat_sub_kumpulan = DB::connection('kpt')->table('upuplus_sub_kumpulan_subjek')
            ->where('kodprogram',$syarat_g3->kodprogram)
            ->where('kategori',$syarat_g3->kategori)
            ->where('kod_group',$syarat_g3->kod_group)->orderby('orderid','ASC')
            ->groupby('kodprogram')
            ->groupby('kategori')
            ->groupby('kodsubjek')
            ->get();

            if($syarat_g3->jum_subjek=='1') { $read='SATU'; }
            elseif($syarat_g3->jum_subjek=='2') { $read='DUA'; }
            elseif($syarat_g3->jum_subjek=='3') { $read='TIGA'; }
            elseif($syarat_g3->jum_subjek=='4') { $read='EMPAT'; }
            elseif($syarat_g3->jum_subjek=='5') { $read='LIMA'; }
            elseif($syarat_g3->jum_subjek=='6') { $read='ENAM'; }
            elseif($syarat_g3->jum_subjek=='7') { $read='TUJUH'; }
            elseif($syarat_g3->jum_subjek=='8') { $read='LAPAN'; }
            elseif($syarat_g3->jum_subjek=='9') { $read='SEMBILAN'; }
        @endphp


        @if($syarat_g3->kumpulan=='Y' && $syarat_g3->sub_kumpulan=='N')
        <li style="padding-left: .3em;">
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_g3->mingred}}</b> dalam <b>{{$read}} ({{$syarat_g3->jum_subjek}})</b> mata pelajaran berikut :
            <div class="card bg-light text-dark">
                <div class="card-body p-2">

                    @foreach ($syarat_sub_kumpulan as $bil_no => $syarat_kumpulan)
                    {{-- @if ($loop->first) --}}
                        @if(substr($syarat_kumpulan->kodsubjek,0,1)!='K')
                            @if($syarat_g3->kodprogram==$syarat_kumpulan->kodprogram)

                                @foreach($sksubjek as  $sk_subjek)
                                    @if($syarat_kumpulan->kodsubjek==$sk_subjek->kodsubjekspm)

                                    <ul style="list-style-type:disc" class="ml-n3">
                                        <li  style="margin-left: -1rem !important;">{{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }}</li>
                                    </ul>
                                    @endif
                                @endforeach     

                            @endif                        
                        @endif

                        @if(substr($syarat_kumpulan->kodsubjek,0,1)=='K')
                            @php
                                $syarat_kesetaraan = DB::connection('kpt')->table('syarat_kesetaraan')->where('PARENT_KUMPULAN_KOD',$syarat_kumpulan->kodsubjek)->orderby('orderid','ASC')->get();
                            @endphp

                            <div style="margin-left:.35rem;">
                                <span style="margin-right: 6px;font-size: 12px;">&#9679;</span>
                                @foreach ($syarat_kesetaraan as $bil_no => $syarat_setara)
                                    @if($syarat_kumpulan->kodsubjek==$syarat_setara->PARENT_KUMPULAN_KOD)                                                                                                                    
                                        @foreach($sksubjek as $sk_subjek)
                                            @if($syarat_setara->KOD==$sk_subjek->kodsubjekspm)
                                            {{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }} @if($bil_no+1 != count($syarat_kesetaraan)) / @endif 
                                            @endif
                                        @endforeach                                                                                              
                                    @endif
                                @endforeach    
                            </div>                                                         
                        @endif 
                        {{-- @endif --}}
                    @endforeach
                </div>
            </div>
        </li>
        @endif
    @endif
    @endforeach

@endif
