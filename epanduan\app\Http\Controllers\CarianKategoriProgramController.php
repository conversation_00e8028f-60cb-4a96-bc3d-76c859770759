<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CarianKategoriProgramController extends Controller
{
    public function index(Request $request)
    {
        //Sesi Program Pengajian
        $sesiSemasa = session('sesiSemasa');

        session()->forget(['jIPTA', 'jIPTA2', 'jBIDANG', 'jTEMUDUGA', 'fuzzySearch', 'meritProgram', 'jTVET', 'jMODpengajian', 'jPERINGKATpengajian', 'jDOUBLE_DEGREE']);

        $lepasanProgram = $request->session()->get('carianKategori');
        $kategoriTerbuka = $request->session()->get('kategoriProgram');
        $jenisIPTA = $request->session()->get('carianIPTA');
        $namaProgram = $request->session()->get('namaProgram');

        $CARIAN_KATEGORI_PROGRAM = [];

        if (session()->get('carianKategori') == 'spm') {

            $CARIAN_KATEGORI_PROGRAM = DB::connection('emas')->table('program as carianProgram_SPM')
                ->select(
                    'carianProgram_SPM.KODUNIVERSITI As kod_IPTA',
                    'carianProgram_SPM.KODPROGRAM_PAPAR As kod_Program',
                    'carianProgram_SPM.NAMAPROGRAM As nama_Program',
                    'carianProgram_SPM.sesi As sesi_Pengajian',
                    'carianProgram_SPM.TEMUDUGA As program_Temuduga',
                    'emas.upu_ipta2.IPTA_SINGKATAN As namaSingkatan_IPTA',
                    'carianProgram_SPM.KATEGORI As kod_Kategori'
                )
                ->where(function ($query) use ($namaProgram) {
                    $query->where('carianProgram_SPM.NAMAPROGRAM', 'like', '%' . $namaProgram . '%')
                        ->orWhere('carianProgram_SPM.KODPROGRAM_PAPAR', 'like', '%' . $namaProgram . '%')
                        ->orWhere('emas.upu_ipta2.IPTA_SINGKATAN', 'like', '%' . $namaProgram . '%');
                })
                ->where(function ($query) use ($kategoriTerbuka) {
                    $query->where('carianProgram_SPM.KATEGORI', 'like', '%' . $kategoriTerbuka . '%');
                })
                ->where(function ($query) use ($jenisIPTA) {
                    $query->where('carianProgram_SPM.KODUNIVERSITI', 'like', '%' . $jenisIPTA . '%');
                })
                ->where('carianProgram_SPM.sesi', $sesiSemasa)
                ->where('carianProgram_SPM.STATUS_TAWAR', 'Y')
                ->where('carianProgram_SPM.NAMAPROGRAM', 'like', '%' . $namaProgram . '%')
                ->whereNotIn('carianProgram_SPM.KODPROGRAM_PAPAR', function ($q) use ($sesiSemasa) {
                    $q->select('UDK_KOD_PROGRAM')->from('emas.upu_daftar_kod')
                        ->where('UDK_JENPROG', session()->get('carianKategori'))
                        ->where('UDK_SESI', $sesiSemasa)
                        ->where('UDK_STATUS', '1');
                })
                ->join('emas.upu_ipta2', 'emas.upu_ipta2.IPTA_KOD', '=', 'carianProgram_SPM.KODUNIVERSITI')
                ->groupBy('carianProgram_SPM.KODPROGRAM_PAPAR')
                ->orderBy('carianProgram_SPM.NAMAPROGRAM', 'ASC')
                ->paginate(10);

            $all_kod = DB::connection('emas')
                ->table('program')
                ->select('KODPROGRAM_PAPAR As kodProgram', 'KATEGORI')
                ->where(function ($query) use ($kategoriTerbuka) {
                    $query->where('KATEGORI', 'like', '%' . $kategoriTerbuka . '%');
                })
                ->where(function ($query) use ($jenisIPTA) {
                    $query->where('KODUNIVERSITI', 'like', '%' . $jenisIPTA . '%');
                })
                ->where('sesi', $sesiSemasa)
                ->where('STATUS_TAWAR', 'Y')
                ->where('LEPASAN', session()->get('carianKategori'))
                ->groupBy('KODPROGRAM_PAPAR')
                ->groupBy('KATEGORI')
                ->get();

            $codeset_katag = DB::connection('upu_codeset')
                ->table('refall_katag')
                ->select('kodkatag', 'ketkatag')
                ->where('statuskatag', 'Y')
                ->where('jenprog', session()->get('carianKategori'))
                ->get();

        } elseif (session()->get('carianKategori') == 'stpm') {

            $CARIAN_KATEGORI_PROGRAM = DB::connection('emas')->table('program_stpm as carianProgram_STPM')
                ->select(
                    'carianProgram_STPM.KODUNIVERSITI As kod_IPTA',
                    'carianProgram_STPM.KODPROGRAM_PAPAR As kod_Program',
                    'carianProgram_STPM.NAMAPROGRAM As nama_Program',
                    'carianProgram_STPM.sesi As sesi_Pengajian',
                    'carianProgram_STPM.TEMUDUGA As program_Temuduga',
                    'emas.upu_ipta2.IPTA_SINGKATAN As namaSingkatan_IPTA',
                    'carianProgram_STPM.KATEGORI As kod_Kategori',
                    'carianProgram_STPM.JENSETARAF As kod_setaraf'
                )
                ->where(function ($query) use ($namaProgram) {
                    $query->where('carianProgram_STPM.NAMAPROGRAM', 'like', '%' . $namaProgram . '%')
                        ->orWhere('carianProgram_STPM.KODPROGRAM_PAPAR', 'like', '%' . $namaProgram . '%')
                        ->orWhere('emas.upu_ipta2.IPTA_SINGKATAN', 'like', '%' . $namaProgram . '%');
                })
                ->where(function ($query) use ($kategoriTerbuka) {
                    $query->where('carianProgram_STPM.KATEGORI', 'like', '%' . $kategoriTerbuka . '%')
                          ->orWhere('carianProgram_STPM.JENSETARAF', 'like', '%' . $kategoriTerbuka . '%');
                })
                ->where(function ($query) use ($jenisIPTA) {
                    $query->where('carianProgram_STPM.KODUNIVERSITI', 'like', '%' . $jenisIPTA . '%');
                })
                ->where('carianProgram_STPM.sesi', $sesiSemasa)
                ->where('carianProgram_STPM.STATUS_TAWAR', 'Y')
                ->where('carianProgram_STPM.NAMAPROGRAM', 'like', '%' . $namaProgram . '%')
                ->whereNotIn('carianProgram_STPM.KODPROGRAM_PAPAR', function ($q) use ($sesiSemasa) {
                    $q->select('UDK_KOD_PROGRAM')->from('emas.upu_daftar_kod')
                        ->where('UDK_JENPROG', session()->get('carianKategori'))
                        ->where('UDK_SESI', $sesiSemasa)
                        ->where('UDK_STATUS', '1');
                })
                ->join('emas.upu_ipta2', 'emas.upu_ipta2.IPTA_KOD', '=', 'carianProgram_STPM.KODUNIVERSITI')
                ->leftJoin('upucodeset.refijzdip_jensetaraf', function ($join) {
                    $join->on('carianProgram_STPM.JENSETARAF', '=', 'upucodeset.refijzdip_jensetaraf.kodjensetaraf');
                })
                ->leftJoin('upucodeset.refemas_katag', function ($join) {
                    $join->on('carianProgram_STPM.KATEGORI', '=', 'upucodeset.refemas_katag.kodkatag')
                        ->where('upucodeset.refemas_katag.jenprog', '=', 'stpm');
                })
                ->groupBy('carianProgram_STPM.KODPROGRAM_PAPAR')
                ->orderBy('carianProgram_STPM.NAMAPROGRAM', 'ASC')
                ->paginate(10);

                // dd($CARIAN_KATEGORI_PROGRAM);

                $all_kod = DB::connection('emas')
                ->table('program_stpm')
                ->select('KODPROGRAM_PAPAR As kodProgram', 'KATEGORI', 'JENSETARAF')
                ->where(function ($query) use ($kategoriTerbuka) {
                    $query->where('KATEGORI', 'like', '%' . $kategoriTerbuka . '%')
                          ->orWhere('JENSETARAF', 'like', '%' . $kategoriTerbuka . '%');
                })
                ->where(function ($query) use ($jenisIPTA) {
                    $query->where('KODUNIVERSITI', 'like', '%' . $jenisIPTA . '%');
                })
                ->where('sesi', $sesiSemasa)
                ->where('STATUS_TAWAR', 'Y')
                ->where('LEPASAN', session()->get('carianKategori'))
                ->groupBy('KODPROGRAM_PAPAR')
                ->groupBy('KATEGORI')
                ->groupBy('JENSETARAF')
                ->get();


            // $CARIAN_KATEGORI_PROGRAM = DB::table('upu_kod as carianProgram_STPM')
            //     ->select(
            //         'carianProgram_STPM.UK_KOD_IPTA as kod_IPTA',
            //         'carianProgram_STPM.UK_KOD_PROGRAM as kod_Program',
            //         'carianProgram_STPM.UK_PROGRAM as nama_Program',
            //         'carianProgram_STPM.UK_TDUGA As program_Temuduga',
            //         'emas.upu_ipta2.IPTA_SINGKATAN as namaSingkatan_IPTA',
            //         'upu_syarat.US_KAT As kod_Kategori',
            //         'upu_syarat.US_JENSETARAF As kod_setaraf'
            //     )
            //     ->where(function ($query) use ($namaProgram) {
            //         $query->where('carianProgram_STPM.UK_PROGRAM', 'like', '%' . $namaProgram . '%')
            //             ->orWhere('carianProgram_STPM.UK_KOD_PROGRAM', 'like', '%' . $namaProgram . '%')
            //             ->orWhere('emas.upu_ipta2.IPTA_SINGKATAN', 'like', '%' . $namaProgram . '%');
            //     })
            //     ->where(function ($query) use ($kategoriTerbuka) {
            //         $query->where('emas.upu_syarat.US_KAT', 'like', '%' . $kategoriTerbuka . '%')
            //             ->orWhere('emas.upu_syarat.US_JENSETARAF', 'like', '%' . $kategoriTerbuka . '%');
            //     })
            //     ->where([
            //         ['carianProgram_STPM.UK_SESI', '=', "2324"],
            //         ['carianProgram_STPM.UK_JENPROG', '=', "stpm"],
            //         ['carianProgram_STPM.UK_STATUS_TAWAR', '=', "Y"],
            //     ])
            //     ->whereNotIn('carianProgram_STPM.UK_KOD_PROGRAM', function ($q) use ($sesiSemasa) {
            //         $q->select('UDK_KOD_PROGRAM')->from('emas.upu_daftar_kod')
            //             ->where('UDK_JENPROG', session()->get('carianKategori'))
            //             ->where('UDK_SESI', $sesiSemasa)
            //             ->where('UDK_STATUS', '1');
            //     })
            //     ->join('emas.upu_ipta2', 'emas.upu_ipta2.IPTA_KOD', '=', 'carianProgram_STPM.UK_KOD_IPTA')
            //     ->join('emas.upu_syarat', function ($join) {
            //         $join->on('carianProgram_STPM.UK_KOD_PROGRAM', '=', 'emas.upu_syarat.US_KOD_PROGRAM')
            //             ->on('carianProgram_STPM.UK_SESI', '=', 'emas.upu_syarat.US_SESI')
            //             ->on('carianProgram_STPM.UK_JENPROG', '=', 'emas.upu_syarat.US_JENPROG');
            //     })
            //     ->leftJoin('upucodeset.refijzdip_jensetaraf', function ($join) {
            //         $join->on('emas.upu_syarat.US_JENSETARAF', '=', 'upucodeset.refijzdip_jensetaraf.kodjensetaraf');
            //     })
            //     ->leftJoin('upucodeset.refemas_katag', function ($join) {
            //         $join->on('emas.upu_syarat.US_KAT', '=', 'upucodeset.refemas_katag.kodkatag')
            //             ->where('upucodeset.refemas_katag.jenprog', '=', 'stpm');
            //     })
            //     ->groupBy('carianProgram_STPM.UK_KOD_PROGRAM')
            //     ->orderBy('carianProgram_STPM.UK_PROGRAM', 'ASC')
            //     ->paginate(10);





            // $all_kod = DB::connection('emas')
            //     ->table('upu_kod')
            //     ->select('UK_KOD_PROGRAM As kodProgram', 'US_KAT As KATEGORI', 'US_JENSETARAF As JENSETARAF')
            //     ->where(function ($query) use ($kategoriTerbuka) {
            //         $query->where('emas.upu_syarat.US_KAT', 'like', '%' . $kategoriTerbuka . '%')
            //               ->orWhere('emas.upu_syarat.US_JENSETARAF', 'like', '%' . $kategoriTerbuka . '%');
            //     })
            //     ->join('emas.upu_syarat', function ($join) {
            //         $join->on('UK_KOD_PROGRAM', '=', 'emas.upu_syarat.US_KOD_PROGRAM')
            //             ->on('UK_SESI', '=', 'emas.upu_syarat.US_SESI')
            //             ->on('UK_JENPROG', '=', 'emas.upu_syarat.US_JENPROG');
            //     })
            //     ->where('UK_SESI', $sesiSemasa)
            //     ->where('UK_STATUS_TAWAR', 'Y')
            //     ->where('UK_JENPROG', session()->get('carianKategori'))
            //     ->get();

            $JENIS_KATEGORI1 = DB::connection('upu_codeset')
                ->table('refall_katag')
                ->select('kodkatag', 'ketkatag', 'sorting')
                ->where('jenprog', 'stpm')
                ->where('statuskatag', 'Y')
                ->whereNotIn('kodkatag', ['E', 'F', 'G']);

            $JENIS_KATEGORI2 = DB::connection('upu_codeset')
                ->table('refijzdip_jensetaraf')
                ->where('statuslyksetaraf', 'Y')
                ->select('kodjensetaraf AS kodkatag', 'ketjensetaraf AS ketkatag', 'sorting');

            $codeset_katag = $JENIS_KATEGORI1->unionAll($JENIS_KATEGORI2)->orderby('kodkatag', 'ASC')->orderby('sorting', 'ASC')->get();

        } else {
            $CARIAN_KATEGORI_PROGRAM == '';

        }

        if (session()->get('carianKategori') == 'spm') {
            return view('carianNamaProgram.index', compact('CARIAN_KATEGORI_PROGRAM', 'all_kod', 'codeset_katag'));


        } elseif (session()->get('carianKategori') == 'stpm') {
            return view('carianNamaProgram.index', compact('CARIAN_KATEGORI_PROGRAM', 'all_kod', 'codeset_katag'));

        } else {
            return view('carianNamaProgram.index', compact('CARIAN_KATEGORI_PROGRAM'));

        }
    }

    public function paparProgramRawak(Request $request, $kodipta, $kodprogram, $katag, $jenprog)
    {
        $request->session()->put(['jIPTA3' => $kodipta, 'fuzzySearch' => $kodprogram, 'kodkatag' => $katag, 'jenprog' => $jenprog]);

        return redirect('ProgramPengajian/kategoriCalon/' . session()->get('kodkatag'));
    }

    public function paparEFGProgramRawak(Request $request, $kodipta, $kodprogram, $jensetaraf, $jenprog)
    {
        $request->session()->put(['jIPTA3' => $kodipta, 'fuzzySearch' => $kodprogram, 'kodkatag' => $jensetaraf, 'jenprog' => $jenprog]);

        return redirect('ProgramPengajian/kategoriCalon/' . session()->get('kodkatag'));
    }

    public function carianProgramRawak(Request $request)
    {
        if ($request->has('carian_TawaranProgram')) {
            $lepasanProgram = $request->input('carianKategori');
            $kategoriTerbuka = $request->input('kategoriProgram');
            $namaProgram = $request->input('namaProgram');
            $jenisIPTA = $request->input('carianIPTA');

            $request->session()->put(['carianKategori' => $lepasanProgram, 'namaProgram' => $namaProgram, 'kategoriProgram' => $kategoriTerbuka, 'carianIPTA' => $jenisIPTA]);
        }

        return redirect('carianNamaProgram?page=1')->withInput();
    }
}
