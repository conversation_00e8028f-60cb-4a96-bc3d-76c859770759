
{{-- @if(!isset($syaratkhas_sk))
    <p>No data found for syaratkhas_sk</p>
@endif --}}
{{-- {{ dd(count($syaratkhas_sk), count($syaratkhas_g1), count($syaratkhas_ga)) }} --}}




@foreach ($syaratkhas_sk as $syarat_khas_sk_0)
    @if ($loop->first)
        <li style="padding-left: .3em; margin-bottom:8px;">
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_khas_sk_0->MINGRED}}</b> dalam <b>{{$syarat_khas_sk_0->KET_JUMLAH_MIN_SUBJEK}} ({{$syarat_khas_sk_0->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran <b>



                DAN</b> Gred <b>{{$syarat_khas_sk_0->MINGRED_2}}</b> dalam <b>{{$syarat_khas_sk_0->KET_JUMLAH_MIN_SUBJEK_2}} ({{$syarat_khas_sk_0->JUMLAH_MIN_SUBJEK_2}})</b> mata pelajaran di peringkat

            <b>
                @if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') STPM
                @elseif($PROGRAM->kategori_Pengajian=='T') STAM
                @elseif($PROGRAM->kategori_Pengajian=='N') Matrikulasi / Asasi
                @elseif($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='J') Matrikulasi
                @else Asasi
                @endif
            </b> :
            <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
                <table cellpadding="2" width="100%">
                    @foreach ($syaratkhas_sk as $syaratkhas_sk0)

                        @if(substr($syaratkhas_sk0->PROGRAMKOD,-3,1)!='1' && substr($syaratkhas_sk0->PROGRAMKOD,-3,1)!='2' && substr($syaratkhas_sk0->PROGRAMKOD,-3,1)!='3')
                            <tr>
                                <td style="vertical-align:top;">&#8226;</td>
                                <td style="vertical-align:top; width:95%">{{ ucwords(strtolower($syaratkhas_sk0->KODSUBJEK_2)) }}</td>
                            </tr>
                        @endif
                    @endforeach
                </table>
            </div>
        </li>
    @endif
@endforeach




