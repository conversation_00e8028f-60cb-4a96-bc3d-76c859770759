<?php

namespace App\Http\Controllers;

use App\Models\listmaklumatAgensi;

class SenaraiAgensiController extends Controller
{
    public function index()
    {
        session()->forget(['jIPTA', 'jIPTA2', 'jIPTA3', 'jBIDANG', 'jTEMUDUGA', 'fuzzySearch', 'namaProgram', 'carianKategori', 'meritProgram', 'jTVET', 'jMODpengajian', 'jPERINGKATpengajian', 'jDOUBLE_DEGREE',]);

        $MRUN = listmaklumatAgensi::whereIn('IPTA_KOD', ['UM', 'US', 'UK', 'UP', 'UT'])
            ->orderByRaw("FIELD(IPTA_KOD, 'UM', 'US', 'UK', 'UP', 'UT')")
            ->get();

        $MCUN = listmaklumatAgensi::whereIn('IPTA_KOD', ['UY', 'UW', 'UH', 'UQ', 'UE', 'UD'])
            ->orderByRaw("FIELD(IPTA_KOD, 'UY', 'UW', 'UH', 'UQ', 'UE', 'UD')")
            ->get();

        $MFUN = listmaklumatAgensi::whereIn('IPTA_KOD', ['UU', 'UA', 'UG', 'UL', 'UZ'])
            ->orderByRaw("FIELD(IPTA_KOD, 'UU', 'UA', 'UG', 'UL', 'UZ')")
            ->get();

        $MTUN = listmaklumatAgensi::whereIn('IPTA_KOD', ['UB', 'UC', 'UR', 'UJ'])
            ->orderByRaw("FIELD(IPTA_KOD, 'UB', 'UC', 'UR', 'UJ')")
            ->get();

        $ILKA = listmaklumatAgensi::whereIn('IPTA_KOD', ['OP'])
            ->orderByRaw("FIELD(IPTA_KOD, 'OP')")
            ->get();

        $JPPKK = listmaklumatAgensi::whereIn('IPTA_KOD', ['FB', 'FC'])
            ->orderByRaw("FIELD(IPTA_KOD, 'FB', 'FC')")
            ->get();

        return view('senaraiAgensi.index', compact('MRUN', 'MCUN', 'MFUN', 'MTUN', 'JPPKK', 'ILKA'));
    }
}
