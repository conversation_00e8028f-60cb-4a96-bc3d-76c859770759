// ----- COUNTER ----- //
var a = 0;
$(window).scroll(function() {
    var oTop = $('#counter').offset().top - window.innerHeight;
    if (a == 0 && $(window).scrollTop() > oTop) {
        $('.counter-value').each(function() {
            var $this = $(this),
                countTo = $this.attr('data-count');
            $({
                countNum: $this.text()
            }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function(now) {
                    const countValue = Math.floor(now);
                    if (countValue >= 1000) {
                        $this.text(countValue.toLocaleString());
                    } else {
                        $this.text(countValue);
                    }
                },
                complete: function() {
                    const finalCountValue = Math.floor(countTo);
                    if (finalCountValue >= 1000) {
                        $this.text(finalCountValue.toLocaleString());
                    } else {
                        $this.text(finalCountValue);
                    }
                }
            });
        });
        a = 1;
    }
});
