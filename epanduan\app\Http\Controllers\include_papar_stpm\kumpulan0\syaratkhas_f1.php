<?php
$syaratkhas_f1  = DB::connection('emas')->select("SELECT * FROM 
(   
    SELECT
    a.PROGRAMKOD AS PROGRAMKOD,
    CASE WHEN SUBSTR(PROGRAMKOD, -3, 1) IN ('1', '2', '3', '4') THEN SUBSTR(PROGRAMKOD, -3, 1) ELSE 'X' END AS ALIRAN,
    a.<PERSON><PERSON> AS GKUMPULAN,
    b.<PERSON> AS KODSUBJEK_1,
    d.<PERSON>TAP<PERSON><PERSON><PERSON>ARAN AS KODSUBJEK_2,
    a.MINGRED AS MINGRED,
    a.<PERSON> AS KUMPULAN,
    a.SUB_KUMPULAN AS SUB_KUMPULAN,
    CASE
        WHEN a.JUMLAH_MIN_SUBJEK = '1'
        THEN 'SATU'
        WHEN a.JUMLAH_MIN_SUBJEK = '2'
        THEN 'DUA'
        WHEN a.JUMLAH_MIN_SUBJEK = '3'
        THEN 'TIGA'
        WHEN a.JUMLAH_MIN_SUBJEK = '4'
        THEN 'EMPAT'
        WHEN a.JUMLAH_MIN_SUBJEK = '5'
        THEN 'LIMA'
        WHEN a.JUMLAH_MIN_SUBJEK = '6'
        THEN 'ENAM'
        WHEN a.JUMLAH_MIN_SUBJEK = '7'
        THEN 'TUJUH'
        WHEN a.JUMLAH_MIN_SUBJEK = '8'
        THEN 'LAPAN'
        ELSE 'SEMBILAN'
    END AS KET_JUMLAH_MIN_SUBJEK,
    a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
    a.SESI AS SESI,
    a.ORDERID AS ORDERID,
    b.ORDERID AS ORDERID2
    FROM syarat_khas_stpm a
    LEFT JOIN syarat_xsub_kumpulan_subjek_stpm b ON (a.KODSUBJEK = b.KUMPULAN AND a.SESI = b.SESI)
    LEFT JOIN upuplus_all_subjek d ON (b.KODSUBJEK = d.KOD)
    WHERE a.KUMPULAN = 'F' AND a.SESI = '$sessionSesi'
) AS temp 
    WHERE PROGRAMKOD LIKE '$PROGRAM->kod_Program%' 
    AND PROGRAMKOD LIKE '%$PROGRAM->kategori_Pengajian)' 
    AND SUBSTR(PROGRAMKOD, -3, 1) NOT IN ('1','2','3','4')
    ORDER BY ORDERID2 ASC");
