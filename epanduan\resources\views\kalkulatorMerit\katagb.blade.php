<style>
    select.form-control:focus { box-shadow: none!important; }
    input:focus { box-shadow: none!important; }
	
	.tbody-dark {
		color: #fff;
		background-color: #5a5c69;
		border-color: #6c6e7e;
	}
</style>
    
    
    <div class="row" style="font-size:.875rem;">

        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">

            <div class="row form-group{{ $errors->has('ALIRANB') ? ' has-error' : '' }} mt-3">                    
                    <div class="col-xl-3">
                        {!! Html::decode(Form::label('ALIRANB', 'SILA PILIH JENIS ALIRANB : ',['class'=>'form-label form label-sm', 'style'=>'white-space: nowrap;'])) !!}
                    </div>                     
                    <div class="col-xl-7">                         
                        <div class="input-group input-group-sm">
                            <select name="ALIRANB" id="ALIRANB" class="form-control form-control-sm mt-n1">
                                {{-- <option value=""selected="selected">-- Sila pilih  --</option> --}}
                                <option value="SAINS" selected="selected">SAINS</option>
                                <option value="SASTERA">SASTERA</option>
                            </select>                         
                        </div>                       
                    </div>                 
                </div>


            <table class="table table-bordered table-sm" width="100%" cellspacing="0">           
                <thead class="thead-dark">
                    <tr>
                        <th>Mata Pelajaran Utama Mengikut Aliran</th>
                        <th width="130px" rowspan="2" style="vertical-align: middle;">Gred</th>
                    </tr>
                </thead>
            
                <tbody style="color: #000;">
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="SUBJEKB01" id="SUBJEKB01" class="form-control form-control-sm" style="border: none; font-weight:bold;">
                                @foreach($subjek_spm_01 as $spm01)
                                    <option value="{{$spm01->kodsubjekspm}}" aliran-type="{{$spm01->aliran}}">{{$spm01->ketsubjekspm}}</option>
                                @endforeach
                            </select>  
                          </td>
                        <td>
                            <select name="GRDGREDB01" id="GRDGREDB01" class="form-control form-control-sm" style="border: none;">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                            {{-- <small class="text-danger" id="grd01" style="margin-left: 0.8rem!important;"></small> --}}
                        </td>
                    </tr>
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="SUBJEKB02" id="SUBJEKB02" class="form-control form-control-sm" style="border: none; font-weight:bold;">
                                @foreach($subjek_spm_02 as $spm02)
                                    <option value="{{$spm02->kodsubjekspm}}" aliran-type="{{$spm02->aliran}}">{{$spm02->ketsubjekspm}}</option>
                                @endforeach
                            </select>   
                         </td>
                        <td>
                            <select name="GRDGREDB02" id="GRDGREDB02" class="form-control form-control-sm" style="border: none;">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>
                    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="SUBJEKB03" id="SUBJEKB03" class="form-control form-control-sm" style="border: none; font-weight:bold;">
                                @foreach($subjek_spm_03 as $spm03)
                                    <option value="{{$spm03->kodsubjekspm}}" aliran-type="{{$spm03->aliran}}">{{$spm03->ketsubjekspm}}</option>
                                @endforeach
                            </select>  
                        </td>
                        <td>
                            <select name="GRDGREDB03" id="GRDGREDB03" class="form-control form-control-sm" style="border: none;">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="SUBJEKB04" id="SUBJEKB04" class="form-control form-control-sm" style="border: none; font-weight:bold;">
                                @foreach($subjek_spm_04 as $spm04)
                                    <option value="{{$spm04->kodsubjekspm}}" aliran-type="{{$spm04->aliran}}">{{$spm04->ketsubjekspm}}</option>
                                @endforeach
                            </select> 
                        </td>
                        <td>
                            <select name="GRDGREDB04" id="GRDGREDB04" class="form-control form-control-sm" style="border: none;">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>

                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="SUBJEKB05" id="SUBJEKB05" class="form-control form-control-sm progControlSelect2" style="border: none; font-weight:bold;" onchange="hidespmB1()">
                                <option value="" aliran-type="info" selected>SILA PILIH SUBJEK</option>   
                                    @foreach($subjek_spm_05 as $spm05)
                                        <option value="{{$spm05->kodsubjekspm}}" aliran-type="{{$spm05->aliran}}">{{$spm05->ketsubjekspm}}</option>
                                    @endforeach
                            </select> 
                            <small class="text-danger" id="spmb01" style="margin-left: 0.5rem!important;"></small>
                        </td>
                        <td>
                            <select name="GRDGREDB05" id="GRDGREDB05" class="form-control form-control-sm" style="border: none;" onchange="hidespmB1()">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>

                </tbody>
            
            </table>

    
            {{-- ************************************************************************************************************************************** --}}

            
            <table class="table table-bordered table-sm" width="100%" cellspacing="0">
               
                <thead class="thead-dark">
                    <tr>
                        <th>Mata Pelajaran Terbaik (Selain Subjek Di Atas)</th>
                        <th width="130px">Gred</th>
                    </tr>
                </thead>
            
                <tbody style="color: #000;">
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="MPTL01" id="MPTL01" class="form-control form-control-sm progControlSelect2" onchange="hidespmB2()">
                                <option value="" selected>SILA PILIH SUBJEK</option>  
                                    @foreach($subjek_spm as $subjekspm)
                                        <option value="{{$subjekspm->kodsubjekspm}}" aliran-type="{{$subjekspm->aliran}}">{{$subjekspm->ketsubjekspm}}</option>
                                    @endforeach
                            </select>
                            <small class="text-danger" id="spmb02" style="margin-left: 0.5rem!important;"></small>
                        </td>
    
                        <td>
                            <select name="GRDGREDB06" id="GRDGREDB06" class="form-control form-control-sm" style="border: none;" onchange="hidespmB2()">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="MPTL02" id="MPTL02" class="form-control form-control-sm progControlSelect2" onchange="hidespmB3()">
                                <option value="" selected>SILA PILIH SUBJEK</option>  
                                    @foreach($subjek_spm as $subjekspm)
                                        <option value="{{$subjekspm->kodsubjekspm}}" aliran-type="{{$subjekspm->aliran}}">{{$subjekspm->ketsubjekspm}}</option>
                                    @endforeach
                            </select>
                            <small class="text-danger" id="spmb03" style="margin-left: 0.5rem!important;"></small>
                        </td>
    
                        <td>
                            <select name="GRDGREDB07" id="GRDGREDB07" class="form-control form-control-sm" style="border: none;" onchange="hidespmB3()">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>

                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">
                            <select name="MPTL03" id="MPTL03" class="form-control form-control-sm progControlSelect2" onchange="hidespmB4()">
                                <option value="" >SILA PILIH SUBJEK</option>  
                                    @foreach($subjek_spm as $subjekspm)
                                        <option value="{{$subjekspm->kodsubjekspm}}" aliran-type="{{$subjekspm->aliran}}">{{$subjekspm->ketsubjekspm}}</option>
                                    @endforeach
                            </select>
                            <small class="text-danger" id="spmb04" style="margin-left: 0.5rem!important;"></small>
                        </td>
    
                        <td>
                            <select name="GRDGREDB08" id="GRDGREDB08" class="form-control form-control-sm" style="border: none;" onchange="hidespmB4()">
                                <option value="" selected>SILA PILIH</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
                    </tr>

                </tbody>
            
            </table>
    
            {{-- ************************************************************************************************************************************** --}}

            <table class="table table-bordered table-sm" width="100%" cellspacing="0">        
                <tbody style="color: #000;">
                    <tr>
                        <td class="tbody-dark" style="vertical-align: middle; font-weight:bold;">Markah Ko-Kurikulum (10%)</td>
                        <td width="130px">
                            <select name="MRKKOKOB" id="MRKKOKOB" class="form-control form-control-sm subjekspm">
                                @for ($i = 10.00; $i >= 0; $i -= 0.01)
                                    <option value="{{ number_format($i,2)}}">{{ number_format($i,2) }}</option>
                                @endfor
                            </select>
                        </td>
                    </tr>
                </tbody>
            </table>
    
            <div id="header_meritB" class="mb-2 alert-success" align="center" style="border:1px solid #e3e6f0; border-radius:5px;">
                <div class="mt-2 mb-1" style="font-size:16px; font-weight:bold;">Markah Merit Anda :</div>
                <div id="meritB" style="font-size:36px; font-weight:bold;"></div>
                <div id="pakejB" class="mb-2" style="font-size:16px; font-weight:bold;"></div>
            </div>   


        </div>



        
    </div>
              
    <script>

        $("select#ALIRANB").on("change", function(){ 
            var aliran_katag=$(this).val();
            
            if(aliran_katag=='SAINS'){
                
                $('#spmb01').hide();
                $('#spmb02').hide();
                $('#spmb03').hide();
                $('#spmb04').hide();

                document.getElementById("SUBJEKB01").selectedIndex='1';
                document.getElementById("SUBJEKB02").selectedIndex='1';
                document.getElementById("SUBJEKB03").selectedIndex='0';
                document.getElementById("SUBJEKB04").selectedIndex='0';
                $('#SUBJEKB05').val('').select2();
                $('#MPTL01').val('').select2();
                $('#MPTL02').val('').select2();
                $('#MPTL03').val('').select2();

                document.getElementById("GRDGREDB01").value='';
                document.getElementById("GRDGREDB02").value='';
                document.getElementById("GRDGREDB03").value='';
                document.getElementById("GRDGREDB04").value='';
                document.getElementById("GRDGREDB05").value='';
                document.getElementById("GRDGREDB06").value='';
                document.getElementById("GRDGREDB07").value='';
                document.getElementById("GRDGREDB08").value='';

                $('#MRKKOKOB').val('0.00').select2();;
                $('#header_meritB').hide();

                $("select[name='SUBJEKB01']").children().removeAttr("disabled").show().not("option[aliran-type='3']").attr("disabled","disabled").hide();
                $("select[name='SUBJEKB02']").children().removeAttr("disabled").show().not("option[aliran-type='1']").attr("disabled","disabled").hide();
                $("select[name='SUBJEKB03']").children().removeAttr("disabled").show().not("option[aliran-type='1']").attr("disabled","disabled").hide();
                $("select[name='SUBJEKB04']").children().removeAttr("disabled").show().not("option[aliran-type='1']").attr("disabled","disabled").hide();
                $("select[name='SUBJEKB05']").children().removeAttr("disabled").show().not("option[aliran-type='1'], option[aliran-type='info']").attr("disabled","disabled").hide();
            
            }
            else if(aliran_katag=='SASTERA'){

                $('#spmb01').hide();
                $('#spmb02').hide();
                $('#spmb03').hide();
                $('#spmb04').hide();

                document.getElementById("SUBJEKB01").selectedIndex='0';
                document.getElementById("SUBJEKB02").selectedIndex='0';
                document.getElementById("SUBJEKB03").selectedIndex='1';
                document.getElementById("SUBJEKB04").selectedIndex='1';
                $('#SUBJEKB05').val('').select2();
                $('#MPTL01').val('').select2();
                $('#MPTL02').val('').select2();
                $('#MPTL03').val('').select2();

                document.getElementById("GRDGREDB01").value='';
                document.getElementById("GRDGREDB02").value='';
                document.getElementById("GRDGREDB03").value='';
                document.getElementById("GRDGREDB04").value='';
                document.getElementById("GRDGREDB05").value='';
                document.getElementById("GRDGREDB06").value='';
                document.getElementById("GRDGREDB07").value='';
                document.getElementById("GRDGREDB08").value='';

                $('#MRKKOKOB').val('0.00').select2();;
                $('#header_meritB').hide();

                $("select[name='SUBJEKB01']").children().removeAttr("disabled").show().not("option[aliran-type='2']").attr("disabled","disabled").hide();
                $("select[name='SUBJEKB02']").children().removeAttr("disabled").show().not("option[aliran-type='3']").attr("disabled","disabled").hide();
                $("select[name='SUBJEKB03']").children().removeAttr("disabled").show().not("option[aliran-type='2']").attr("disabled","disabled").hide();
                $("select[name='SUBJEKB04']").children().removeAttr("disabled").show().not("option[aliran-type='2']").attr("disabled","disabled").hide();
                $("select[name='SUBJEKB05']").children().removeAttr("disabled").show().not("option[aliran-type='2'], option[aliran-type='info']").attr("disabled","disabled").hide(); 
            }
        });


        $(document).ready(function() 
        {	

            $('#spmb01').hide();
            $('#spmb02').hide();
            $('#spmb03').hide();
            $('#spmb04').hide();

            document.getElementById("SUBJEKB01").selectedIndex='1';
            document.getElementById("SUBJEKB02").selectedIndex='1';
            document.getElementById("SUBJEKB03").selectedIndex='0';
            document.getElementById("SUBJEKB04").selectedIndex='0';
            $('#SUBJEKB05').val('').select2();
            $('#header_meritB').hide();
            $('#MRKKOKOB').val('0.00').select2();
            $("select[name='SUBJEKB01']").children().removeAttr("disabled").show().not("option[aliran-type='3']").attr("disabled","disabled").hide();
            $("select[name='SUBJEKB02']").children().removeAttr("disabled").show().not("option[aliran-type='1']").attr("disabled","disabled").hide();
            $("select[name='SUBJEKB03']").children().removeAttr("disabled").show().not("option[aliran-type='1']").attr("disabled","disabled").hide();
            $("select[name='SUBJEKB04']").children().removeAttr("disabled").show().not("option[aliran-type='1']").attr("disabled","disabled").hide();
            $("select[name='SUBJEKB05']").children().removeAttr("disabled").show().not("option[aliran-type='1'], option[aliran-type='info']").attr("disabled","disabled").hide();
        });

        function hidespmB1()
        {
            $('#spmb01').hide();
        }

        function hidespmB2()
        {
            $('#spmb02').hide();
        }
        function hidespmB3()
        {
            $('#spmb03').hide();
        }
        function hidespmB4()
        {
            $('#spmb04').hide();
        }


        function kiraSPMB()
        {

            var vSUBJEKB1 = document.getElementById("SUBJEKB05").selectedIndex;
            var vSUBJEKB2 = document.getElementById("MPTL01").selectedIndex;
            var vSUBJEKB3 = document.getElementById("MPTL02").selectedIndex;
            var vSUBJEKB4 = document.getElementById("MPTL03").selectedIndex;

            var vGREDB1 = document.getElementById("GRDGREDB01").selectedIndex;
            var vGREDB2 = document.getElementById("GRDGREDB02").selectedIndex;
            var vGREDB3 = document.getElementById("GRDGREDB03").selectedIndex;
            var vGREDB4 = document.getElementById("GRDGREDB04").selectedIndex;
            var vGREDB5 = document.getElementById("GRDGREDB05").selectedIndex;
            var vGREDB6 = document.getElementById("GRDGREDB06").selectedIndex;
            var vGREDB7 = document.getElementById("GRDGREDB07").selectedIndex;
            var vGREDB8 = document.getElementById("GRDGREDB08").selectedIndex;
            var vKOKOB = document.getElementById("MRKKOKOB").value;

            var aliran_katag = document.getElementById("ALIRANB").value;

            if((vSUBJEKB1!='' && vGREDB5=='') || (vSUBJEKB1=='' && vGREDB5!=''))
            {
                $('#spmb01').show();
                document.getElementById("spmb01").innerHTML='Ruangan Subjek dan Gred perlu diisi sekiranya mengisi salah satu daripada maklumat tersebut.'; 
                
            }
            else if((vSUBJEKB2!='' && vGREDB6=='') || (vSUBJEKB2=='' && vGREDB6!=''))
            {       
                $('#spmb02').show();
                document.getElementById("spmb02").innerHTML='Ruangan Subjek dan Gred perlu diisi sekiranya mengisi salah satu daripada maklumat tersebut.'; 
            }
            else if((vSUBJEKB3!='' && vGREDB7=='') || (vSUBJEKB3=='' && vGREDB7!=''))
            {
                $('#spmb03').show();
                document.getElementById("spmb03").innerHTML='Ruangan Subjek dan Gred perlu diisi sekiranya mengisi salah satu daripada maklumat tersebut.'; 
            }
            else if((vSUBJEKB4!='' && vGREDB8=='') || (vSUBJEKB4=='' && vGREDB8!=''))
            {            
                $('#spmb04').show();
                document.getElementById("spmb04").innerHTML='Ruangan Subjek dan Gred perlu diisi sekiranya mengisi salah satu daripada maklumat tersebut.'; 
            }

            else if(vSUBJEKB2!='' && vSUBJEKB2==vSUBJEKB1)
            {
                $('#spmb02').show();
                document.getElementById("spmb02").innerHTML='Sila pilih subjek yang berbeza.'; 
            }

            else if(vSUBJEKB3!='' && (vSUBJEKB3==vSUBJEKB2 || vSUBJEKB3==vSUBJEKB1))
            {
                $('#spmb03').show();
                document.getElementById("spmb03").innerHTML='Sila pilih subjek yang berbeza.'; 
            }

            else if(vSUBJEKB4!='' && (vSUBJEKB4==vSUBJEKB3 || vSUBJEKB4==vSUBJEKB2 || vSUBJEKB4==vSUBJEKB1))
            {
                $('#spmb04').show();
                document.getElementById("spmb04").innerHTML='Sila pilih subjek yang berbeza.'; 
            }
            else
            {
				$('#spmb01').hide();
				$('#spmb02').hide();
				$('#spmb03').hide();
				$('#spmb04').hide();

                if(vGREDB1==1){MPUB01 = 18;}
                else if(vGREDB1==2){MPUB01 = 16;}
                else if(vGREDB1==3){MPUB01 = 14;}
                else if(vGREDB1==4){MPUB01 = 12;}
                else if(vGREDB1==5){MPUB01 = 10;}	
                else if(vGREDB1==6){MPUB01 = 8;}
                else if(vGREDB1==7){MPUB01 = 6;}	
                else if(vGREDB1==8){MPUB01 = 4;}
                else if(vGREDB1==9){MPUB01 = 2;}
                else {MPUB01 = 0;}
                
                if(vGREDB2==1){MPUB02= 18;}
                else if(vGREDB2==2){MPUB02= 16;}
                else if(vGREDB2==3){MPUB02= 14;}
                else if(vGREDB2==4){MPUB02= 12;}
                else if(vGREDB2==5){MPUB02= 10;}	
                else if(vGREDB2==6){MPUB02= 8;}
                else if(vGREDB2==7){MPUB02= 6;}	
                else if(vGREDB2==8){MPUB02= 4;}
                else if(vGREDB2==9){MPUB02= 2;}
                else {MPUB02= 0;}
                
                if(vGREDB3==1){MPUB03 = 18;}
                else if(vGREDB3==2){MPUB03 = 16;}
                else if(vGREDB3==3){MPUB03 = 14;}
                else if(vGREDB3==4){MPUB03 = 12;}
                else if(vGREDB3==5){MPUB03 = 10;}	
                else if(vGREDB3==6){MPUB03 = 8;}
                else if(vGREDB3==7){MPUB03 = 6;}	
                else if(vGREDB3==8){MPUB03 = 4;}
                else if(vGREDB3==9){MPUB03 = 2;}
                else {MPUB03 = 0;}
                
                if(vGREDB4==1){MPUB04 = 18;}
                else if(vGREDB4==2){MPUB04 = 16;}
                else if(vGREDB4==3){MPUB04 = 14;}
                else if(vGREDB4==4){MPUB04 = 12;}
                else if(vGREDB4==5){MPUB04 = 10;}	
                else if(vGREDB4==6){MPUB04 = 8;}
                else if(vGREDB4==7){MPUB04 = 6;}	
                else if(vGREDB4==8){MPUB04 = 4;}
                else if(vGREDB4==9){MPUB04 = 2;}
                else {MPUB04 = 0;}

                if(vGREDB5==1){MPUB05 = 18;}
                else if(vGREDB5==2){MPUB05 = 16;}
                else if(vGREDB5==3){MPUB05 = 14;}
                else if(vGREDB5==4){MPUB05 = 12;}
                else if(vGREDB5==5){MPUB05 = 10;}	
                else if(vGREDB5==6){MPUB05 = 8;}
                else if(vGREDB5==7){MPUB05 = 6;}	
                else if(vGREDB5==8){MPUB05 = 4;}
                else if(vGREDB5==9){MPUB05 = 2;}
                else {MPUB05 = 0;}
                
                if(vGREDB6==1){MPTB01 = 18;}
                else if(vGREDB6==2){MPTB01 = 16;}
                else if(vGREDB6==3){MPTB01 = 14;}
                else if(vGREDB6==4){MPTB01 = 12;}
                else if(vGREDB6==5){MPTB01 = 10;}	
                else if(vGREDB6==6){MPTB01 = 8;}
                else if(vGREDB6==7){MPTB01 = 6;}	
                else if(vGREDB6==8){MPTB01 = 4;}
                else if(vGREDB6==9){MPTB01 = 2;}
                else {MPTB01 = 0;}

                if(vGREDB7==1){MPTB02 = 18;}
                else if(vGREDB7==2){MPTB02 = 16;}
                else if(vGREDB7==3){MPTB02 = 14;}
                else if(vGREDB7==4){MPTB02 = 12;}
                else if(vGREDB7==5){MPTB02 = 10;}	
                else if(vGREDB7==6){MPTB02 = 8;}
                else if(vGREDB7==7){MPTB02 = 6;}	
                else if(vGREDB7==8){MPTB02 = 4;}
                else if(vGREDB7==9){MPTB02 = 2;}
                else {MPTB02 = 0;}
                
                if(vGREDB8==1){MPTB03 = 18;}
                else if(vGREDB8==2){MPTB03 = 16;}
                else if(vGREDB8==3){MPTB03 = 14;}
                else if(vGREDB8==4){MPTB03 = 12;}
                else if(vGREDB8==5){MPTB03 = 10;}	
                else if(vGREDB8==6){MPTB03 = 8;}
                else if(vGREDB8==7){MPTB03 = 6;}	
                else if(vGREDB8==8){MPTB03 = 4;}
                else if(vGREDB8==9){MPTB03 = 2;}
                else {MPTB03 = 0;}  

                var MPUB_TOTAL = parseFloat(MPUB01) + parseFloat(MPUB02) + parseFloat(MPUB03) + parseFloat(MPUB04) + parseFloat(MPUB05);
                var MPUB_PERCENT = (MPUB_TOTAL/90)*90;

                var MPTB_TOTAL = parseFloat(MPTB01) + parseFloat(MPTB02) + parseFloat(MPTB03);
                var MPTB_PERCENT = (MPTB_TOTAL/54)*30;

                var MPUB_MPTB_TOTAL = parseFloat(MPUB_PERCENT) + parseFloat(MPTB_PERCENT);
                var MPUB_MPTB_PERCENT = (MPUB_MPTB_TOTAL/120)*90;

                var MERIT=parseFloat(MPUB_MPTB_PERCENT) + parseFloat(vKOKOB);


                if(aliran_katag=='SAINS'){
                    document.getElementById("pakejB").innerHTML='Aliran : SAINS'; 
                }
                else if(aliran_katag=='SASTERA'){
                    document.getElementById("pakejB").innerHTML='Aliran : SASTERA'; 
                }
                document.getElementById("meritB").innerHTML=parseFloat(Number(MERIT)).toFixed(2) + ' %'; 
                $('#header_meritB').show();
            }
            
        }

        function resetSPMB()
        {
            $('#SUBJEKB05').val('').select2(); 
            $('#MPTL01').val('').select2(); 
            $('#MPTL02').val('').select2(); 
            $('#MPTL03').val('').select2(); 

            document.getElementById("GRDGREDB01").value='';
            document.getElementById("GRDGREDB02").value='';
            document.getElementById("GRDGREDB03").value='';
            document.getElementById("GRDGREDB04").value='';
            document.getElementById("GRDGREDB05").value='';
            document.getElementById("GRDGREDB06").value='';
            document.getElementById("GRDGREDB07").value='';
            document.getElementById("GRDGREDB08").value='';
            $('#MRKKOKOB').val('0.00').select2();
            $('#header_meritB').hide();

            $('#spmb01').hide();
            $('#spmb02').hide();
            $('#spmb03').hide();
            $('#spmb04').hide();

        }


    </script>
    
    <script type="text/javascript">
        $(".maxmin").each(function () {

        var thisJ = $(this);
        var max = thisJ.attr("max") * 1;
        var min = thisJ.attr("min") * 1;
        var intOnly = String(thisJ.attr("intOnly")).toLowerCase() == "true";

        var test = function (str) {
            return str == "" || /* (!intOnly && str == ".") || */
                ($.isNumeric(str) && str * 1 <= max && str * 1 >= min &&
                (!intOnly || str.indexOf(".") == -1) && str.match(/^0\d/) == null);
                // commented out code would allow entries like ".7"
        };

        thisJ.keydown(function () {
            var str = thisJ.val();
            if (test(str)) thisJ.data("dwnval", str);
        });

        thisJ.keyup(function () {
            var str = thisJ.val();
            if (!test(str)) thisJ.val(thisJ.data("dwnval"));
        })
        });
    </script>
    
        