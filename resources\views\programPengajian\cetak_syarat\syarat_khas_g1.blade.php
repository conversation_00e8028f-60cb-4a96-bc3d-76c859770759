@if(count($syaratkhas_g1) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_g1 as $syarat_khas_g1)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_g1->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>  
</li>
@endif

@if(count($syaratkhas_g2) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_g2 as $syarat_khas_g2)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_g2->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>  
</li>
@endif

{{-- ############################################################################################### --}}

@if(count($syaratkhas_k1_g1) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k1_g1 as $syarat_khas_k1_g1)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k1_g1->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</li>
@endif

@if((count($syaratkhas_k1_g1) > 0 && count($syaratkhas_k1_g2) > 0) && (empty($syaratkhas_k2_g1) && empty($syaratkhas_k2_g2)) && (empty($syaratkhas_k3_g2) && empty($syaratkhas_k3_g2)))
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%" id="mytable">
            @foreach ($syaratkhas_k1_g2 as $syarat_khas_k1_g2)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k1_g2->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</li>
@endif

@if((count($syaratkhas_k1_g1) > 0 && count($syaratkhas_k1_g2) > 0) && (count($syaratkhas_k2_g1) > 0 && empty($syaratkhas_k2_g2)) && (empty($syaratkhas_k3_g2) && empty($syaratkhas_k3_g2)))
<div style="padding-left: .3em; margin-top:10px;""> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%" id="mytable">
            @foreach ($syaratkhas_k1_g2 as $syarat_khas_k1_g2)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k1_g2->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</div>
@endif



@if((count($syaratkhas_k1_g2) > 0 && count($syaratkhas_k2_g2) > 0) && (($syaratkhas_k1_g2[0]->MINGRED != $syaratkhas_k2_g2[0]->MINGRED) || $syaratkhas_k1_g2[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_k2_g2[0]->KET_JUMLAH_MIN_SUBJEK))
<div style="padding-left: .3em; margin-top:10px;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k1_g2 as $syarat_khas_k1_g2)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k1_g2->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</div>
@endif

@if((count($syaratkhas_k1_g3) > 0 && count($syaratkhas_k2_g3) > 0) && (($syaratkhas_k1_g3[0]->MINGRED != $syaratkhas_k2_g3[0]->MINGRED) || $syaratkhas_k1_g3[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_k2_g3[0]->KET_JUMLAH_MIN_SUBJEK))
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k1_g3 as $syarat_khas_k1_g3)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k1_g3->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</li>
@endif

@if(count($syaratkhas_k1_g3) > 0 && empty($syaratkhas_k2_g3) && empty($syaratkhas_k3_g3))
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%" id="mytable">
            @foreach ($syaratkhas_k1_g3 as $syarat_khas_k1_g3)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k1_g3->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</li>
@endif


{{-- ############################################################################################### --}}

@if((count($syaratkhas_k2_g1) > 0 || count($syaratkhas_k2_g2) > 0) && (count($syaratkhas_k1_g1) != 0 || count($syaratkhas_k1_g2) != 0))
<br>
<p style="text-align:center;"><b>ATAU</b></p>
<div style="padding-left: .3em; margin-top:10px;"> 
	@if(count($syaratkhas_k2_g1) > 0)
	Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k2_g1 as $syarat_khas_k2_g1)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k2_g1->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div> 
	@endif
</div>
@else

    @if(count($syaratkhas_k2_g1) > 0)
        <li>Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
        <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
            <table cellpadding="2" width="100%">
                @foreach ($syaratkhas_k2_g1 as $syarat_khas_k2_g1)
                    <tr>
                        <td style="vertical-align:top;">&#8226;</td>
                        <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k2_g1->KODSUBJEK_2)) }}</td>
                    </tr>
                @endforeach
            </table>
        </div>
		</li>
    @endif
@endif

@if(empty($syaratkhas_k1_g2) && count($syaratkhas_k2_g2) > 0 && empty($syaratkhas_k3_g2))
<div style="padding-left: .3em; margin-top:10px;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k2_g2 as $syarat_khas_k2_g2)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k2_g2->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</div>
@endif

@if((count($syaratkhas_k1_g2) > 0 && count($syaratkhas_k2_g2) > 0) && (($syaratkhas_k1_g2[0]->MINGRED == $syaratkhas_k2_g2[0]->MINGRED) && $syaratkhas_k1_g2[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_k2_g2[0]->KET_JUMLAH_MIN_SUBJEK))
<li style="padding-left: .3em; margin-top:10px;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k2_g2 as $syarat_khas_k2_g2)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k2_g2->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</li>
@endif

@if((count($syaratkhas_k1_g2) > 0 && count($syaratkhas_k2_g2) > 0) && (($syaratkhas_k1_g2[0]->MINGRED != $syaratkhas_k2_g2[0]->MINGRED) || $syaratkhas_k1_g2[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_k2_g2[0]->KET_JUMLAH_MIN_SUBJEK))
<div style="padding-left: .3em; margin-top:10px;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k2_g2 as $syarat_khas_k2_g2)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k2_g2->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</div>
@endif


@if((count($syaratkhas_k1_g3) > 0 && count($syaratkhas_k2_g3) > 0) && (($syaratkhas_k1_g3[0]->MINGRED == $syaratkhas_k2_g3[0]->MINGRED) && $syaratkhas_k1_g3[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_k2_g3[0]->KET_JUMLAH_MIN_SUBJEK))
<li style="padding-left: .3em; margin-top:10px;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k2_g3 as $syarat_khas_k2_g3)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k2_g3->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
</li>
@endif

@if(count($syaratkhas_k2_g3) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k2_g3 as $syarat_khas_k2_g3)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k2_g3->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
@endif



{{-- ############################################################################################### --}}

@if(count($syaratkhas_k3_g1) > 0 || count($syaratkhas_k3_g2) > 0)
<br>
<p style="text-align:center;"><b>ATAU</b></p>
@endif


@if(count($syaratkhas_k3_g1) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k3_g1 as $syarat_khas_k3_g1)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k3_g1->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
@endif

@if(count($syaratkhas_k3_g2) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k3_g2 as $syarat_khas_k3_g2)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k3_g2->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
@endif

@if(count($syaratkhas_k3_g3) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
        <table cellpadding="2" width="100%">
            @foreach ($syaratkhas_k3_g3 as $syarat_khas_k3_g3)
                <tr>
                    <td style="vertical-align:top;">&#8226;</td>
                    <td style="vertical-align:top; width:98%">{{ ucwords(strtolower($syarat_khas_k3_g3->KODSUBJEK_2)) }}</td>
                </tr>
            @endforeach
        </table>
    </div>
@endif
