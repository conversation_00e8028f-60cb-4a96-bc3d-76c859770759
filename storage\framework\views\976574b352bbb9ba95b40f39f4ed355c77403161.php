<?php $__env->startSection('content'); ?>
    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h2 class="title"> KATEGORI CALON </h2>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><PERSON><PERSON></a></li>
                                    <li class="breadcrumb-item"><a href="#">Kategor<PERSON> Calon</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container">

            <?php if(session()->get('jenprog') == 'spm'): ?>
                <div class="container">
                    <div class="row-list">
                        <?php $__currentLoopData = $JENIS_KATEGORI->whereIn('kodkatag', ['A', 'B']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $KATEGORI_CALON_SPM): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="<?php echo e(url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_SPM->kodkatag)); ?>">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="<?php echo e(asset('/assets/images/_backgroundBidang/awam.jpg')); ?>"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a
                                                class=" text-white d-block font-weight-bold"><?php echo e($KATEGORI_CALON_SPM->kodkatag); ?> : <?php echo e($KATEGORI_CALON_SPM->ketkatag); ?></a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if(session()->get('jenprog') == 'stpm'): ?>
                <div class="container">
                    <div class="row-list">
                        <div class="col-12 mt-4 pt-2">
                            <div class="section-title">
                                <h2 class="titleHeader" style="text-align: center; color:#1818189c">LEPASAN STPM
                                </h2>
                            </div>
                        </div>
                        <?php $__currentLoopData = $JENIS_KATEGORI->whereIn('kodkatag', ['A', 'S']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $KATEGORI_CALON_STPM): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="<?php echo e(url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_STPM->kodkatag)); ?>">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="<?php echo e(asset('/assets/images/_backgroundBidang/awam.jpg')); ?>"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a
                                                class=" text-white d-block font-weight-bold"><?php echo e($KATEGORI_CALON_STPM->kodkatag); ?> : <?php echo e($KATEGORI_CALON_STPM->ketkatag); ?></a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <div class="container" style="padding-top: 7rem">
                    <div class="row-list">
                        <div class="col-12 mt-4 pt-2">
                            <div class="section-title">
                                <h2 class="titleHeader" style="text-align: center; color:#1818189c">LEPASAN MATRIKULASI / ASASI</h2>
                            </div>
                        </div>
                        <?php $__currentLoopData = $JENIS_KATEGORI->whereIn('kodkatag', ['P', 'L', 'U', 'N', 'K', 'J','M','V']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $KATEGORI_CALON_MATRIK): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="<?php echo e(url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_MATRIK->kodkatag)); ?>">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="<?php echo e(asset('/assets/images/_backgroundBidang/awam.jpg')); ?>"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a class=" text-white d-block font-weight-bold"><?php echo e($KATEGORI_CALON_MATRIK->kodkatag); ?> : <?php echo e($KATEGORI_CALON_MATRIK->ketkatag); ?></a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <div class="container" style="padding-top: 7rem">
                    <div class="row-list">
                        <div class="col-12 mt-4 pt-2">
                            <div class="section-title">
                                <h2 class="titleHeader" style="text-align: center; color:#1818189c">LEPASAN STAM
                                </h2>
                            </div>
                        </div>
                        <?php $__currentLoopData = $JENIS_KATEGORI->whereIn('kodkatag', ['T']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $KATEGORI_CALON_STAM): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="<?php echo e(url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_STAM->kodkatag)); ?>">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="<?php echo e(asset('/assets/images/_backgroundBidang/awam.jpg')); ?>"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a
                                                class=" text-white d-block font-weight-bold"><?php echo e($KATEGORI_CALON_STAM->kodkatag); ?> : <?php echo e($KATEGORI_CALON_STAM->ketkatag); ?></a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <div class="container" style="padding-top: 7rem">
                    <div class="row-list">
                        <div class="col-12 mt-4 pt-2">
                            <div class="section-title">
                                <h2 class="titleHeader" style="text-align: center; color:#1818189c">KELAYAKAN SETARAF
                                </h2>
                            </div>
                        </div>
                        <?php $__currentLoopData = $JENIS_KATEGORI->whereNotIn('kodkatag', ['P', 'L', 'U', 'N', 'K', 'J','M','V', 'A', 'S', 'T']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $KATEGORI_CALON_DIPLOMA): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="<?php echo e(url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_DIPLOMA->kodkatag)); ?>">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="<?php echo e(asset('/assets/images/_backgroundBidang/awam.jpg')); ?>"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a
                                                class=" text-white d-block font-weight-bold"><?php echo e($KATEGORI_CALON_DIPLOMA->kodkatag); ?> : <?php echo e($KATEGORI_CALON_DIPLOMA->ketkatag); ?></a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/kategoriCalon/index.blade.php ENDPATH**/ ?>