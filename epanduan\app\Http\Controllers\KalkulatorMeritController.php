<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class KalkulatorMeritController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

     
    public function calcSPM()
    {
		$subjek_stem  = \DB::connection('upu_codeset')->select(\DB::raw("SELECT kodsubjekspm,ketsubjekspm,pakej FROM refspm_subjek WHERE pakej IN ('PA','PB','PC','KB','KP','KS','PK') ORDER BY ketsubjekspm ASC"));
		$subjek_spm_kssm  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm,ketsubjekspm,pakej FROM refspm_subjek WHERE kodsubjekspm NOT IN ('1103','1119','1449','1249') AND pakej IN ('ST','PA','PB','PC','KB','KP','KS','PK') ORDER BY ketsubjekspm ASC"));

		$subjek_spm  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm,ketsubjekspm,aliran FROM refspm_subjek ORDER BY ketsubjekspm ASC"));
		$subjek_BM  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm,ketsubjekspm FROM refspm_subjek WHERE kodsubjekspm IN ('1103') ORDER BY ketsubjekspm ASC"));
		$subjek_BI  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm,ketsubjekspm FROM refspm_subjek WHERE kodsubjekspm IN ('1119') ORDER BY ketsubjekspm ASC"));
		$subjek_MATE  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm,ketsubjekspm FROM refspm_subjek WHERE kodsubjekspm IN ('1449') ORDER BY ketsubjekspm ASC"));
		$subjek_SEJ  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm,ketsubjekspm FROM refspm_subjek WHERE kodsubjekspm IN ('1249') ORDER BY ketsubjekspm ASC"));

		$subjek_spm_01  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm, ketsubjekspm, aliran FROM refspm_subjek WHERE kodsubjekspm IN ('1103','1449') ORDER BY ketsubjekspm ASC"));
		$subjek_spm_02  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm, ketsubjekspm, aliran FROM refspm_subjek WHERE kodsubjekspm IN ('3472','1449') ORDER BY ketsubjekspm ASC"));
		$subjek_spm_03  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm, ketsubjekspm, aliran FROM refspm_subjek WHERE kodsubjekspm IN ('4531','1511') ORDER BY ketsubjekspm ASC"));
		$subjek_spm_04  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm, ketsubjekspm, aliran FROM refspm_subjek WHERE kodsubjekspm IN ('4541','1249') ORDER BY ketsubjekspm ASC"));
		$subjek_spm_05  = DB::connection('upu_codeset')->select(DB::raw("SELECT kodsubjekspm, ketsubjekspm, aliran FROM refspm_subjek WHERE aliran IN ('1','2') AND kodsubjekspm NOT IN ('1449','1103','3472','4531','1511','4541','1249') ORDER BY ketsubjekspm ASC"));
        $merit_gred_spm  = DB::connection('upu_codeset')->select(DB::raw("SELECT * FROM refspm_gred WHERE statusspmgred='Y' AND kodspmgred NOT IN ('R','T','Z') ORDER BY kodspmgred ASC"));



        return view('kalkulatorMerit.index', compact('merit_gred_spm','subjek_spm','subjek_spm_kssm','subjek_stem','subjek_BM','subjek_BI','subjek_MATE','subjek_SEJ','subjek_spm_01','subjek_spm_02','subjek_spm_03','subjek_spm_04','subjek_spm_05'));
    }
    
    public function calcSTPM()
    {
        $stam = DB::connection('upu_codeset')->table('refstam_thp')
        ->select('kodthpstam', 'ketthpstam')
        ->whereNotIn('kodthpstam', ['T'])
        ->where('statusthpstam','Y')
        ->get();

        return view('kalkulatorMerit.index', compact('stam'));

	}    

    public function calcDIPLOMA()
    {
    	return view('kalkulatorMerit.index');
    }    
    
    public function calcSTAM()
    {
    	return view('kalkulatorMerit.index');
	}    


}
