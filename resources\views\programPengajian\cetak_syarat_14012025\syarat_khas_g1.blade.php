@if(count($syaratkhas_g1) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g1 as $syarat_khas_g1)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_g1->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_g1->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
</li>
@endif

@if(count($syaratkhas_g2) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g2 as $syarat_khas_g2)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_g2->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_g2->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
</li>
@endif


{{-- @if(count($syaratkhas_g3) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g3 as $syarat_khas_g3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif --}}

{{-- ############################################################################################### --}}


@if(count($syaratkhas_k1_g1) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k1_g1 as $syarat_khas_k1_g1)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k1_g1->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k1_g1->KODSUBJEK_2)) }}</span>
                </div>
            @endforeach
        </div>
    </div>
</li>
@endif


@if(count($syaratkhas_k1_g2) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k1_g2 as $syarat_khas_k1_g2)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k1_g2->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k1_g2->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
</li>
@endif

{{-- @if(count($syaratkhas_k1_g3) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k1_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k1_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k1_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k1_g3 as $syarat_khas_k1_g3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_k1_g3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif --}}

@if((count($syaratkhas_k2_g1) > 0 || count($syaratkhas_k2_g2) > 0) && (count($syaratkhas_k1_g1) != 0 || count($syaratkhas_k1_g2) != 0))
<br>
<p style="text-align:center;"><b>ATAU</b></p>

	@if(count($syaratkhas_k2_g1) > 0)
		<!-- li -->
	Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
		<div class="card bg-light text-dark">
			<div class="card-body p-2">
				@foreach ($syaratkhas_k2_g1 as $syarat_khas_k2_g1)
					{{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
					{{ ucwords(strtolower($syarat_khas_k2_g1->KODSUBJEK_2)) }} <br> --}}

					<div class="col-md-12">
						<span  style="display:table-cell;">&#9679;</span>
						<span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k2_g1->KODSUBJEK_2)) }}</span>
					</div>

				@endforeach
			</div>
		</div>
		<!--/li -->
	@endif


@else

{{-- @if($syaratkhas_k1_g1[0]->KODSUBJEK_1<>$syaratkhas_k2_g1[0]->KODSUBJEK_1) --}}
    @if(count($syaratkhas_k2_g1) > 0)
        <li>Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
        <div class="card bg-light text-dark">
            <div class="card-body p-2">
                @foreach ($syaratkhas_k2_g1 as $syarat_khas_k2_g1)
                    {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_k2_g1->KODSUBJEK_2)) }} <br> --}}

                    <div class="col-md-12">
                        <span  style="display:table-cell;">&#9679;</span>
                        <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k2_g1->KODSUBJEK_2)) }}</span>
                    </div>

                @endforeach
            </div>
        </div>
		</li>
    @endif
{{-- @endif --}}
@endif

@if(count($syaratkhas_k2_g2) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k2_g2 as $syarat_khas_k2_g2)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k2_g2->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k2_g2->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
@endif

{{-- @if(count($syaratkhas_k2_g3) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k2_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k2_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k2_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k2_g3 as $syarat_khas_k2_g3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_k2_g3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif --}}


@if(count($syaratkhas_k3_g1) > 0 || count($syaratkhas_k3_g2) > 0)
<br>
<p style="text-align:center;"><b>ATAU</b></p>
@endif


@if(count($syaratkhas_k3_g1) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_g1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_g1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_g1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k3_g1 as $syarat_khas_k3_g1)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k3_g1->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k3_g1->KODSUBJEK_2)) }}</span>
                </div>

            @endforeach
        </div>
    </div>
@endif

@if(count($syaratkhas_k3_g2) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_g2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_g2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_g2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k3_g2 as $syarat_khas_k3_g2)
                {{-- <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                {{ ucwords(strtolower($syarat_khas_k3_g2->KODSUBJEK_2)) }} <br> --}}

                <div class="col-md-12">
                    <span  style="display:table-cell;">&#9679;</span>
                    <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_k3_g2->KODSUBJEK_2)) }}</span>
                </div>
            @endforeach
        </div>
    </div>
@endif

{{-- @if(count($syaratkhas_k3_g3) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_k3_g3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_k3_g3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_k3_g3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_k3_g3 as $syarat_khas_k3_g3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_k3_g3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif --}}

{{-- ############################################################################################### --}}