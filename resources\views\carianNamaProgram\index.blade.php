@extends('layout.layout')
@section('content')
    <!-- Hero Start -->
    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h4 class="title"> CARIAN PROGRAM </h4>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="{{ url('/') }}"><PERSON><PERSON></a></li>
                                    <li class="breadcrumb-item active" aria-current="page">Carian Program</li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
                <!--end col-->
            </div>
            <!--end row-->
        </div>
        <!--end container-->
    </section>
    <!--end section-->
    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container">
            <form action="{{ url('carianNamaProgram/carianProgramRawak') }}" method="post">
                @csrf
                <div class="row">
                    <!--Lepasan Calon-->
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Lepasan calon <span class="text-danger">*</span></label>
                            <div class="position-relative">
                                <select class="form-control custom-select" id="carianKategori" name="carianKategori"
                                    required>
                                    <option value="">Pilih lepasan</option>
                                    <option value="spm" {{ old('carianKategori') == 'spm' ? 'selected' : '' }}>SPM
                                    </option>
                                    <option value="stpm" {{ old('carianKategori') == 'stpm' ? 'selected' : '' }}>STPM / Setaraf</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!--Kategori Calon-->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Kategori calon</label>
                            <div class="position-relative">
                                <select class="form-control custom-select" id="kategoriProgram" name="kategoriProgram">
                                    <option value="" data-value="">Pilih kategori</option>
                                    <option value="A" data-value="spm" {{ old('kategoriProgram') == 'A' ? 'selected' : '' }}>SPM TAHUN SEMASA</option>
                                    <option value="B" data-value="spm" {{ old('kategoriProgram') == 'B' ? 'selected' : '' }}>SPM BUKAN TAHUN SEMASA</option>
                                    <option value="S" data-value="stpm" {{ old('kategoriProgram') == 'S' ? 'selected' : '' }}>STPM TAHUN SEMASA (SAINS)</option>
                                    <option value="A" data-value="stpm" {{ old('kategoriProgram') == 'A' ? 'selected' : '' }}>STPM TAHUN SEMASA (SASTERA)</option>
                                    <option value="P" data-value="stpm" {{ old('kategoriProgram') == 'P' ? 'selected' : '' }}>MATRIKULASI (PERAKAUNAN)</option>
                                    <option value="L" data-value="stpm" {{ old('kategoriProgram') == 'L' ? 'selected' : '' }}>ASASI (TESL)</option>
                                    <option value="U" data-value="stpm" {{ old('kategoriProgram') == 'U' ? 'selected' : '' }}>ASASI (UNDANG-UNDANG)</option>
                                    <option value="N" data-value="stpm" {{ old('kategoriProgram') == 'N' ? 'selected' : '' }}>MATRIKULASI / ASASI (SAINS)</option>
                                    <option value="K" data-value="stpm" {{ old('kategoriProgram') == 'K' ? 'selected' : '' }}>ASASI (KEJURUTERAAN)</option>
                                    <option value="J" data-value="stpm" {{ old('kategoriProgram') == 'J' ? 'selected' : '' }}>MATRIKULASI (KEJURUTERAAN)</option>
                                    <option value="M" data-value="stpm" {{ old('kategoriProgram') == 'M' ? 'selected' : '' }}>ASASI (SAINS SOSIAL)</option>
                                    <option value="V" data-value="stpm" {{ old('kategoriProgram') == 'V' ? 'selected' : '' }}>ASASI (TVET POLITEKNIK)</option>
                                    <option value="T" data-value="stpm" {{ old('kategoriProgram') == 'T' ? 'selected' : '' }}>STAM TAHUN SEMASA</option>
                                    <option value="G1" data-value="stpm" {{ old('kategoriProgram') == 'G1' ? 'selected' : '' }}>DIPLOMA UNIVERSITI AWAM</option>
                                    <option value="G2" data-value="stpm" {{ old('kategoriProgram') == 'G2' ? 'selected' : '' }}>DIPLOMA POLITEKNIK / KOLEJ KOMUNITI</option>
                                    <option value="E1" data-value="stpm" {{ old('kategoriProgram') == 'E1' ? 'selected' : '' }}>DKM / DLKM</option>
                                    <option value="E2" data-value="stpm" {{ old('kategoriProgram') == 'E2' ? 'selected' : '' }}>DIPLOMA VOKASIONAL MALAYSIA (DVM) (LEPASAN KOLEJ VOKASIONAL)</option>
                                    <option value="E3" data-value="stpm" {{ old('kategoriProgram') == 'E3' ? 'selected' : '' }}>DIPLOMA ILKA / IPTS / LUAR NEGARA / LAIN-LAIN DIPLOMA</option>
                                    <option value="F1" data-value="stpm" {{ old('kategoriProgram') == 'F1' ? 'selected' : '' }}>A-LEVEL</option>
                                    <option value="F2" data-value="stpm" {{ old('kategoriProgram') == 'F2' ? 'selected' : '' }}>INTERNATIONAL BACCALAUREATE DIPLOMA (IBD)</option>
                                    <option value="F3" data-value="stpm" {{ old('kategoriProgram') == 'F3' ? 'selected' : '' }}>SEKOLAH SUKAN</option>
                                    <option value="F4" data-value="stpm" {{ old('kategoriProgram') == 'F4' ? 'selected' : '' }}>STPM BUKAN TAHUN SEMASA</option>
                                    <option value="F5" data-value="stpm" {{ old('kategoriProgram') == 'F5' ? 'selected' : '' }}>MATRIKULASI / ASASI BUKAN TAHUN SEMASA</option>
                                    <option value="F6" data-value="stpm" {{ old('kategoriProgram') == 'F6' ? 'selected' : '' }}>STAM BUKAN TAHUN 2022 / 2021</option>
                                    <option value="F7" data-value="stpm" {{ old('kategoriProgram') == 'F7' ? 'selected' : '' }}>AUSMAT / SAM / CPU</option>
                                    <option value="F8" data-value="stpm" {{ old('kategoriProgram') == 'F8' ? 'selected' : '' }}>FOUNDATION IPTS</option>

                                </select>
                            </div>
                        </div>
                    </div>
                    <!--Nama IPTA-->
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>IPTA / ILKA <span class="text-danger">*</span></label>
                            <div class="position-relative">
                                <select class="form-control custom-select" id="carianIPTA" name="carianIPTA">
                                    <option value="" data-value="" data-value2="">-- Papar Semua --</option>
                                    <option value="UA" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UA") {{ 'selected' }} @endif>UPSI</option>
                                    <option value="UB" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UB") {{ 'selected' }} @endif>UTHM</option>
                                    <option value="UC" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UC") {{ 'selected' }} @endif>UTeM</option>
                                    <option value="UD" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UD") {{ 'selected' }} @endif>UniSZA</option>
                                    <option value="UE" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UE") {{ 'selected' }} @endif>UiTM</option>
                                    <option value="UG" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UG") {{ 'selected' }} @endif>UMT</option>
                                    <option value="UH" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UH") {{ 'selected' }} @endif>UMS</option>
                                    <option value="UJ" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UJ") {{ 'selected' }} @endif>UMPSA</option>
                                    <option value="UL" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UL") {{ 'selected' }} @endif>UMK</option>
                                    <option value="UK" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UK") {{ 'selected' }} @endif>UKM</option>
                                    <option value="UM" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UM") {{ 'selected' }} @endif>UM</option>
                                    <option value="UP" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UP") {{ 'selected' }} @endif>UPM</option>
                                    <option value="UQ" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UQ") {{ 'selected' }} @endif>USIM</option>
                                    <option value="UR" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UR") {{ 'selected' }} @endif>UniMAP</option>
                                    <option value="US" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "US") {{ 'selected' }} @endif>USM</option>
                                    <option value="UT" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UT") {{ 'selected' }} @endif>UTM</option>
                                    <option value="UU" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UU") {{ 'selected' }} @endif>UUM</option>
                                    <option value="UW" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UW") {{ 'selected' }} @endif>UNIMAS</option>
                                    <option value="UY" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UY") {{ 'selected' }} @endif>UIAM</option>
                                    <option value="UZ" data-value="spm" data-value2="stpm" @if (old('carianIPTA') == "UZ") {{ 'selected' }} @endif>UPNM</option>
                                    <option value="OP" data-value="spm" @if (old('carianIPTA') == "OP") {{ 'selected' }} @endif>KOLEJ PROFESIONAL MARA</option>
                                    <option value="OM" data-value="spm" @if (old('carianIPTA') == "OM") {{ 'selected' }} @endif>KOLEJ MARA</option>
                                    <option value="FB" data-value="spm" @if (old('carianIPTA') == "FB") {{ 'selected' }} @endif>POLITEKNIK </option>
                                    <option value="FC" data-value="spm" @if (old('carianIPTA') == "FC") {{ 'selected' }} @endif>KOLEJ KOMUNITI</option>
                                    {{-- <option value="IA" data-value="spm" @if (old('carianIPTA') == "IA") {{ 'selected' }} @endif>ADTEC</option> --}}
                                    {{-- <option value="IJ" data-value="spm" @if (old('carianIPTA') == "IJ") {{ 'selected' }} @endif>JMTI</option> --}}
                                    {{-- <option value="IK" data-value="spm" @if (old('carianIPTA') == "IK") {{ 'selected' }} @endif>ILP</option> --}}
                                    {{-- <option value="NT" data-value="spm" @if (old('carianIPTA') == "NT") {{ 'selected' }} @endif>BPKLP</option> --}}
                                    {{-- <option value="OI" data-value="spm" @if (old('carianIPTA') == "OI") {{ 'selected' }} @endif>IKM</option> --}}
                                    {{-- <option value="OK" data-value="spm" @if (old('carianIPTA') == "OK") {{ 'selected' }} @endif>KKTM</option> --}}
                                    {{-- <option value="XT" data-value="spm" @if (old('carianIPTA') == "XT") {{ 'selected' }} @endif>IKBN</option> --}}
                                </select>
                            </div>
                        </div>
                    </div>
                    <!--Carian Nama Program-->
                    <div class="col-md-5">
                        <div class="form-group">
                            <label>Carian nama program pengajian <span class="text-danger"></span></label>
                            <div class="form-group mb-0 position-relative">
                                <div class="input-group">
                                    <input id="namaProgram" name="namaProgram" class="form-control"
                                        placeholder="Carian Nama Progam" value="{{ old('namaProgram') }}">
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-primary" id="carian_TawaranProgram"
                                            name="carian_TawaranProgram">Cari</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--Paparan Maklumat Carian Program-->
                    <div class="col">
                        <div class="table-responsive bg-white shadow rounded mt-4">
                            <table class="table mb-0 table-center">
                                <thead class="bg-light">
                                    <tr>
                                        <th scope="col" class="text-center" style="max-width: 100px;">IPTA</th>
                                        <th scope="col" class="text-center" style="width: 100px;">Kod Program</th>
                                        <th scope="col" class="text-center" style="min-width: 300px;">Nama Program
                                            pengajian</th>
                                        <th scope="col" class="text-center"style="min-width: 300px;">Terbuka kepada
                                            kategori</th>
                                    </tr>
                                </thead>
                                <tbody class="loadPagination">
                                    @foreach ($CARIAN_KATEGORI_PROGRAM as $index => $programCarian)
                                        <tr>
                                            <td class="text-center small h6 font-weight-bold">
                                                {{ $programCarian->namaSingkatan_IPTA }}</td>
                                            <td class="text-center small h6" id="programKod" name="programKod">
                                                {{ $programCarian->kod_Program }}</td>
                                            <td class="text-left small h6">{{ $programCarian->nama_Program }}
                                                @if ($programCarian->program_Temuduga == 'Y')
                                                    <b>#</b>
                                                @endif
                                            </td>
                                            <td class="small">
                                                <div class="faq-content">
                                                    <div class="accordion" id="accordionExample{{ $index }}">
                                                        <div class="card border-0 rounded mb-2"
                                                            style="background-color: transparent">
                                                            <a data-toggle="collapse" href="#collapseone{{ $index }}"
                                                                class="faq position-relative" aria-expanded="true"
                                                                aria-controls="collapseone{{ $index }}">
                                                                <div class="border-0 p-3 pr-5" id="headingfifone">
                                                                    <h6 class="title mb-0">Kategori :</h6>
                                                                </div>
                                                            </a>
                                                            <div id="collapseone{{ $index }}" class="collapse"
                                                                aria-labelledby="headingfifone"
                                                                data-parent="#accordionExample{{ $index }}">
                                                                <div class="card-body px-2 py-4">
                                                                    @foreach ($all_kod as $kodprogram)
                                                                        @if (session()->get('carianKategori') == 'spm')
                                                                            @if ($programCarian->kod_Program == $kodprogram->kodProgram)
                                                                                <a href="{{ url('carianNamaProgram/' . $programCarian->kod_IPTA . '/' . $programCarian->kod_Program . '/' . $kodprogram->KATEGORI . '/' . session()->get('carianKategori')) }}"
                                                                                    class="text-muted mb-0 faq-ans"
                                                                                    data-toggle="tooltip"
                                                                                    data-placement="top"
                                                                                    title="Syarat Kemasukan">
                                                                                    @foreach ($codeset_katag as $codeset)
                                                                                        @if ($kodprogram->KATEGORI == $codeset->kodkatag)
                                                                                            {{ $codeset->kodkatag }} :
                                                                                            {{ $codeset->ketkatag }} <br>
                                                                                        @endif
                                                                                    @endforeach
                                                                                </a>
                                                                            @endif
                                                                        @endif
                                                                        @if (session()->get('carianKategori') == 'stpm')
                                                                            @if ($programCarian->kod_Program == $kodprogram->kodProgram)
                                                                                <a href="{{ url('carianNamaProgram/' . $programCarian->kod_IPTA . '/' . $programCarian->kod_Program . '/' . $kodprogram->KATEGORI . '/' . session()->get('carianKategori')) }}"
                                                                                    class="text-muted mb-0 faq-ans"
                                                                                    data-toggle="tooltip"
                                                                                    data-placement="top"
                                                                                    title="Syarat Kemasukan">
                                                                                    @foreach ($codeset_katag as $codeset)
                                                                                        @if ($kodprogram->KATEGORI == $codeset->kodkatag)
                                                                                            {{ $codeset->kodkatag }} :
                                                                                            {{ $codeset->ketkatag }} <br>
                                                                                        @endif
                                                                                    @endforeach
                                                                                </a>
                                                                            @endif
                                                                            @if ($programCarian->kod_Program == $kodprogram->kodProgram)
                                                                                <a href="{{ url('carianNamaProgram/' . $programCarian->kod_IPTA . '/' . $programCarian->kod_Program . '/' . $kodprogram->JENSETARAF . '/' . session()->get('carianKategori')) }}"
                                                                                    class="text-muted mb-0 faq-ans">
                                                                                    @foreach ($codeset_katag as $codeset)
                                                                                        @if ($kodprogram->JENSETARAF == $codeset->kodkatag)
                                                                                            {{ $codeset->kodkatag }} :
                                                                                            {{ $codeset->ketkatag }}<br>
                                                                                        @endif
                                                                                    @endforeach
                                                                                </a>
                                                                            @endif
                                                                        @endif
                                                                    @endforeach
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- PAGINATION START -->
                        <div class="col-12 mt-4 pt-2">
                            @if ($CARIAN_KATEGORI_PROGRAM instanceof Illuminate\Pagination\LengthAwarePaginator)
                                {!! $CARIAN_KATEGORI_PROGRAM->links('carianNamaProgram.carianList-Paginator') !!}
                            @endif
                        </div>
                    </div>
                </div>
            </form>
    </section>
    {{-- <script src="{{ asset('/assets/js/dropdownKategori.js') }}"></script> --}}
@endsection
