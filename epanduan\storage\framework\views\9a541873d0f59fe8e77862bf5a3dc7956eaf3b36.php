<?php $__env->startSection('content'); ?>

<style>
/* Modern Filter Dropdown Styles - Compact Version */
.modern-filter-container {
    background: #ffffff;
    border-radius: 6px;
    padding: 0.5rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.modern-filter-group {
    margin-bottom: 0.4rem;
}

.modern-dropdown {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.modern-dropdown:hover {
    border-color: #007bff;
    box-shadow: 0 1px 6px rgba(0, 123, 255, 0.12);
}

.modern-dropdown-toggle {
    width: 100%;
    background: none;
    border: none;
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 38px;
}

.modern-dropdown-toggle:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.dropdown-header {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    flex: 1;
    min-width: 0;
}

.dropdown-icon {
    color: #007bff;
    font-size: 0.8rem;
    width: 14px;
    text-align: center;
    flex-shrink: 0;
}

.dropdown-title {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.8rem;
    flex: 1;
    line-height: 1.2;
}

.dropdown-arrow {
    color: #6c757d;
    transition: transform 0.3s ease;
    font-size: 0.7rem;
    flex-shrink: 0;
}

.modern-dropdown-toggle[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
}

.modern-dropdown-content {
    border-top: 1px solid #e9ecef;
    background: #fafbfc;
}

.dropdown-search-container {
    padding: 0.5rem;
    position: relative;
}

.dropdown-search {
    width: 100%;
    padding: 0.4rem 0.6rem 0.4rem 1.8rem;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 0.75rem;
    transition: all 0.3s ease;
    background: white;
}

.dropdown-search:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 0.7rem;
}

.dropdown-options {
    max-height: 200px;
    overflow-y: auto;
    padding: 0.4rem;
}

.modern-checkbox-item {
    padding: 0.3rem 0.4rem;
    border-radius: 3px;
    transition: all 0.2s ease;
    margin-bottom: 0.1rem;
}

.modern-checkbox-item:hover {
    background: rgba(0, 123, 255, 0.08);
}

.modern-checkbox-item.hidden {
    display: none;
}

.modern-checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 0;
    font-size: 0.75rem;
    color: #495057;
    font-weight: 500;
    line-height: 1.2;
}

.modern-checkbox {
    display: none;
}

.checkbox-custom {
    width: 14px;
    height: 14px;
    border: 1px solid #dee2e6;
    border-radius: 2px;
    margin-right: 0.4rem;
    position: relative;
    transition: all 0.3s ease;
    background: white;
    flex-shrink: 0;
}

.modern-checkbox:checked + .modern-checkbox-label .checkbox-custom {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
}

.modern-checkbox:checked + .modern-checkbox-label .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.checkbox-text {
    line-height: 1.3;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Merit Range Specific Styles - Vertical Layout */
.merit-range-container {
    padding: 0.75rem;
}

.merit-header {
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.merit-title-section {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.merit-main-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.merit-title {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.8rem;
}

.merit-measurement {
    margin-left: 1.5rem;
}

.merit-measurement small {
    font-size: 0.7rem;
    color: #6c757d;
    font-style: italic;
}

.merit-inputs {
    margin-top: 0.5rem;
}

.input-column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-row {
    display: flex;
    align-items: end;
    gap: 1rem;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.input-label {
    display: block;
    font-size: 0.75rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.3rem;
}

.modern-input {
    width: 100%;
    padding: 0.6rem 0.8rem;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    min-height: 40px;
}

.modern-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Carian Program Search Input Styling */
.filter-search-input {
    padding: 0.7rem 1rem !important;
    font-size: 0.9rem !important;
    border: 1px solid #e9ecef !important;
    border-radius: 6px 0 0 6px !important;
    min-height: 45px !important;
    width: 100% !important;
}

.filter-search-input:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    outline: none !important;
}

.filter-search-input::placeholder {
    color: #6c757d;
    font-size: 0.85rem;
}

.input-separator {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* Responsive Design - Ultra Compact */
@media (max-width: 768px) {
    .modern-filter-container {
        padding: 0.4rem;
    }

    .modern-filter-group {
        margin-bottom: 0.3rem;
    }

    .modern-dropdown-toggle {
        padding: 0.4rem 0.6rem;
        min-height: 32px;
    }

    .dropdown-title {
        font-size: 0.7rem;
    }

    .dropdown-icon {
        font-size: 0.7rem;
        width: 12px;
    }

    .dropdown-arrow {
        font-size: 0.65rem;
    }

    .dropdown-search-container {
        padding: 0.4rem;
    }

    .dropdown-search {
        padding: 0.35rem 0.5rem 0.35rem 1.6rem;
        font-size: 0.7rem;
    }

    .search-icon {
        left: 0.9rem;
        font-size: 0.65rem;
    }

    .dropdown-options {
        max-height: 150px;
        padding: 0.3rem;
    }

    .modern-checkbox-item {
        padding: 0.25rem 0.3rem;
        margin-bottom: 0.05rem;
    }

    .modern-checkbox-label {
        font-size: 0.7rem;
    }

    .checkbox-custom {
        width: 12px;
        height: 12px;
        margin-right: 0.3rem;
    }

    .modern-checkbox:checked + .modern-checkbox-label .checkbox-custom::after {
        font-size: 8px;
    }

    .compact-btn {
        padding: 0.3rem 0.6rem !important;
        font-size: 0.75rem !important;
    }

    .compact-btn i {
        font-size: 0.7rem;
    }

    .filter-tags-container.compact {
        margin-top: 0.4rem;
        padding: 0.3rem;
        min-height: 40px;
    }

    .filter-tags-title {
        font-size: 0.7rem;
    }

    .filter-tag {
        font-size: 0.65rem;
        padding: 0.2rem 0.35rem;
        gap: 0.25rem;
    }

    .filter-tag-remove {
        width: 14px;
        height: 14px;
        font-size: 9px;
    }

    .clear-all-filters {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
    }

    .merit-range-container {
        padding: 0.5rem;
    }

    .merit-title {
        font-size: 0.75rem;
    }

    .merit-measurement small {
        font-size: 0.65rem;
    }

    .input-column {
        gap: 0.4rem;
    }

    .input-group {
        gap: 0.25rem;
    }

    .input-label {
        font-size: 0.7rem;
        margin-bottom: 0.2rem;
    }

    .modern-input {
        padding: 0.5rem 0.6rem;
        font-size: 0.8rem;
        min-height: 36px;
    }

    .filter-search-input {
        padding: 0.6rem 0.8rem !important;
        font-size: 0.85rem !important;
        min-height: 42px !important;
    }

    .input-row {
        flex-direction: column;
        gap: 0.4rem;
    }

    .input-separator {
        margin-bottom: 0;
        transform: rotate(90deg);
    }
}

/* Animation for collapsing */
.collapsing {
    transition: height 0.35s ease;
}

/* Scrollbar styling for dropdown options */
.dropdown-options::-webkit-scrollbar {
    width: 6px;
}

.dropdown-options::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Compact Button Styling */
.compact-btn {
    padding: 0.4rem 0.8rem !important;
    font-size: 0.8rem !important;
    line-height: 1.2 !important;
    border-radius: 4px !important;
}

.compact-btn i {
    font-size: 0.75rem;
}

/* Filter Tags System - Compact */
.filter-tags-container {
    margin-top: 0.75rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    min-height: 50px;
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.filter-tags-container.compact {
    margin-top: 0.5rem;
    padding: 0.4rem;
    min-height: 45px;
}

.filter-tags-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.3rem;
}

.filter-tags-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.4rem;
}

.filter-tags-title i {
    font-size: 0.7rem;
}

.clear-all-filters {
    background: none;
    border: none;
    color: #dc3545;
    font-size: 0.7rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.clear-all-filters:hover {
    background: #dc3545;
    color: white;
}

.filter-tags-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
    min-height: 25px;
    align-items: flex-start;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 0.25rem 0.4rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    gap: 0.3rem;
    animation: tagSlideIn 0.3s ease-out;
    box-shadow: 0 1px 3px rgba(0, 123, 255, 0.2);
}

.filter-tag.bidang { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); }
.filter-tag.ipta { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
.filter-tag.peringkat { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; }
.filter-tag.tvet { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
.filter-tag.mod { background: linear-gradient(135deg, #6f42c1 0%, #59359a 100%); }
.filter-tag.double { background: linear-gradient(135deg, #fd7e14 0%, #e8650e 100%); }
.filter-tag.temuduga { background: linear-gradient(135deg, #e83e8c 0%, #d91a72 100%); }
.filter-tag.merit { background: linear-gradient(135deg, #6c757d 0%, #545b62 100%); }

.filter-tag-category {
    font-weight: 600;
    opacity: 0.9;
}

.filter-tag-value {
    font-weight: 400;
}

.filter-tag-remove {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: inherit;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 10px;
    font-weight: bold;
    transition: all 0.2s ease;
    margin-left: 0.15rem;
}

.filter-tag-remove:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.no-filters-message {
    color: #6c757d;
    font-size: 0.7rem;
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 25px;
}

@keyframes  tagSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive adjustments for filter tags */
@media (max-width: 768px) {
    .filter-tags-container {
        padding: 0.5rem;
        margin-top: 0.75rem;
    }

    .filter-tag {
        font-size: 0.7rem;
        padding: 0.25rem 0.4rem;
        gap: 0.3rem;
    }

    .filter-tag-remove {
        width: 16px;
        height: 16px;
        font-size: 10px;
    }

    .filter-tags-title {
        font-size: 0.75rem;
    }

    .clear-all-filters {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}
</style>
    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h3 class="title"> PROGRAM PENGAJIAN</h3>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>">Laman Utama</a></li>
                                    <li class="breadcrumb-item"><a
                                            href="<?php echo e(url('kategoriCalon', [session('jenprog')])); ?>">Kategori
                                            <?php echo e(Request::route('kodkatag')); ?></a></li>
                                    <li class="breadcrumb-item"><a href="#">Program Pengajian</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container">
            <form action="<?php echo e(url('ProgramPengajian/kategoriCalon/' . Request::route('kodkatag'))); ?>" method="post">
                <?php echo csrf_field(); ?>
                <div class="row">
                    <div class="col-lg-3 col-md-4 col-12">
                        <div class="card border-0 sidebar sticky-bar">
                            <div class="card-body p-0">
                                <!-- FILTER CARD -->
                                <div class="card border-0 shadow-sm">
                                    <!-- Header with Magnifier -->
                                    <div class="card-header bg-gradient-primary filter-card-header text-white py-3">
                                        <h5 class="mb-0 text-white d-flex align-items-center">
                                            <i class="fas fa-search me-2"></i>Tapisan Carian
                                        </h5>
                                    </div>

                                    <div class="card-body p-3">
                                        <!-- SEARCH INPUT -->
                                        <div class="mb-3">
                                            <div class="input-group">
                                                <input type="text" class="form-control filter-search-input" id="fuzzySearch"
                                                    name="fuzzySearch" placeholder="Carian Program..."
                                                    value="<?php echo e(old('fuzzySearch')); ?>">
                                                
                                            </div>
                                        </div>

                                        <!-- ACTION BUTTONS -->
                                        <div class="d-flex gap-2 mb-2">
                                            <button type="submit" class="btn btn-sm executive-action-btn btn-outline-primary flex-fill compact-btn"
                                                    id="searching" name="searching">
                                                <i class="fas fa-search me-1"></i> Cari
                                            </button>
                                            <button type="submit" class="btn btn-sm executive-action-btn btn-outline-danger flex-fill compact-btn"
                                                    id="clearFiltering" name="clearFiltering">
                                                <i class="fas fa-trash-alt me-1"></i> Clear
                                            </button>
                                        </div>

                                        <!-- FILTER TAGS DISPLAY -->
                                        <div class="filter-tags-container compact" id="filterTagsContainer">
                                            <div class="filter-tags-header">
                                                <div class="filter-tags-title">
                                                    <i class="fas fa-tags"></i>
                                                    <span>Filter Aktif</span>
                                                </div>
                                                <button type="button" class="clear-all-filters" onclick="clearAllFilters()" style="display: none;">
                                                    <i class="fas fa-times"></i> Hapus Semua
                                                </button>
                                            </div>
                                            <div class="filter-tags-wrapper" id="filterTagsWrapper">
                                                <div class="no-filters-message">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Tiada filter dipilih
                                                </div>
                                            </div>
                                        </div>

                                    <!-- Modern Filter Dropdowns Container -->
                                    <div class="modern-filter-container mt-4">

                                        <!-- Bidang Dropdown -->
                                        <div class="modern-filter-group mb-3">
                                            <div class="modern-dropdown">
                                                <button class="modern-dropdown-toggle" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#bidangCollapse" aria-expanded="false" aria-controls="bidangCollapse">
                                                    <div class="dropdown-header">
                                                        <i class="fas fa-th-list dropdown-icon"></i>
                                                        <span class="dropdown-title">Bidang</span>
                                                    </div>
                                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                                </button>

                                                <div class="collapse modern-dropdown-content" id="bidangCollapse">
                                                    <div class="dropdown-search-container">
                                                        <input type="text" class="dropdown-search" placeholder="Cari bidang..."
                                                               onkeyup="filterDropdownOptions('bidangCollapse', this.value)">
                                                        <i class="fas fa-search search-icon"></i>
                                                    </div>

                                                    <div class="dropdown-options">
                                                        <div class="modern-checkbox-item" data-search="program dan kelayakan generik">
                                                            <input type="checkbox" name="pBidang[]" value="00"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('00', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang00"
                                                                onchange="updateFilterTags()">
                                                            <label class="modern-checkbox-label" for="bidang00">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Program Dan Kelayakan Generik</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="pendidikan">
                                                            <input type="checkbox" name="pBidang[]" value="01"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('01', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang01"
                                                                onchange="updateFilterTags()">
                                                            <label class="modern-checkbox-label" for="bidang01">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Pendidikan</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="sastera dan kemanusiaan">
                                                            <input type="checkbox" name="pBidang[]" value="02"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('02', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang02"
                                                                onchange="updateFilterTags()">
                                                            <label class="modern-checkbox-label" for="bidang02">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Sastera Dan Kemanusiaan</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="sains sosial kewartawanan dan maklumat">
                                                            <input type="checkbox" name="pBidang[]" value="03"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('03', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang03"
                                                                >
                                                            <label class="modern-checkbox-label" for="bidang03">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Sains Sosial, Kewartawanan Dan Maklumat</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="perniagaan pentadbiran dan perundangan">
                                                            <input type="checkbox" name="pBidang[]" value="04"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('04', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang04"
                                                                onchange="updateSelectedCount('bidangCollapse', 'bidangCount')">
                                                            <label class="modern-checkbox-label" for="bidang04">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Perniagaan, Pentadbiran Dan Perundangan</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="sains semulajadi matematik dan statistik">
                                                            <input type="checkbox" name="pBidang[]" value="05"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('05', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang05"
                                                                onchange="updateSelectedCount('bidangCollapse', 'bidangCount')">
                                                            <label class="modern-checkbox-label" for="bidang05">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Sains Semulajadi, Matematik Dan Statistik</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="teknologi maklumat dan komunikasi">
                                                            <input type="checkbox" name="pBidang[]" value="06"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('06', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang06"
                                                                onchange="updateSelectedCount('bidangCollapse', 'bidangCount')">
                                                            <label class="modern-checkbox-label" for="bidang06">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Teknologi Maklumat Dan Komunikasi</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="kejuruteraan pembuatan dan pembinaan">
                                                            <input type="checkbox" name="pBidang[]" value="07"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('07', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang07"
                                                                onchange="updateSelectedCount('bidangCollapse', 'bidangCount')">
                                                            <label class="modern-checkbox-label" for="bidang07">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Kejuruteraan, Pembuatan Dan Pembinaan</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="pertanian perhutanan perikanan dan vaterinar">
                                                            <input type="checkbox" name="pBidang[]" value="08"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('08', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang08"
                                                                onchange="updateSelectedCount('bidangCollapse', 'bidangCount')">
                                                            <label class="modern-checkbox-label" for="bidang08">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Pertanian, Perhutanan, Perikanan Dan Vaterinar</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="kesihatan dan kebajikan">
                                                            <input type="checkbox" name="pBidang[]" value="09"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('09', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang09"
                                                                onchange="updateSelectedCount('bidangCollapse', 'bidangCount')">
                                                            <label class="modern-checkbox-label" for="bidang09">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Kesihatan Dan Kebajikan</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="perkhidmatan">
                                                            <input type="checkbox" name="pBidang[]" value="10"
                                                                <?php if(!empty(session()->get('jBIDANG')) && in_array('10', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="bidang10"
                                                                onchange="updateSelectedCount('bidangCollapse', 'bidangCount')">
                                                            <label class="modern-checkbox-label" for="bidang10">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Perkhidmatan</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- IPTA Dropdown -->
                                        <div class="modern-filter-group mb-3">
                                            <div class="modern-dropdown">
                                                <button class="modern-dropdown-toggle" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#iptaCollapse" aria-expanded="false" aria-controls="iptaCollapse">
                                                    <div class="dropdown-header">
                                                        <i class="fas fa-university dropdown-icon"></i>
                                                        <span class="dropdown-title">IPTA</span>
                                                    </div>
                                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                                </button>

                                                <div class="collapse modern-dropdown-content" id="iptaCollapse">
                                                    <div class="dropdown-search-container">
                                                        <input type="text" class="dropdown-search" placeholder="Cari IPTA..."
                                                               onkeyup="filterDropdownOptions('iptaCollapse', this.value)">
                                                        <i class="fas fa-search search-icon"></i>
                                                    </div>

                                                    <div class="dropdown-options">
                                                        <?php if(session()->get('jenprog') == 'spm'): ?>
                                                            <div class="modern-checkbox-item" data-search="kolej mara">
                                                                <input type="checkbox" name="carianIPTA[]" value="OM"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('OM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="modern-checkbox checkbox-filter" id="ipta_OM"
                                                                    onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                                <label class="modern-checkbox-label" for="ipta_OM">
                                                                    <span class="checkbox-custom"></span>
                                                                    <span class="checkbox-text">Kolej Mara</span>
                                                                </label>
                                                            </div>

                                                            <div class="modern-checkbox-item" data-search="kolej komuniti">
                                                                <input type="checkbox" name="carianIPTA[]" value="FC"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('FC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="modern-checkbox checkbox-filter" id="ipta_FC"
                                                                    onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                                <label class="modern-checkbox-label" for="ipta_FC">
                                                                    <span class="checkbox-custom"></span>
                                                                    <span class="checkbox-text">Kolej Komuniti</span>
                                                                </label>
                                                            </div>

                                                            <div class="modern-checkbox-item" data-search="kpm">
                                                                <input type="checkbox" name="carianIPTA[]" value="OP"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('OP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="modern-checkbox checkbox-filter" id="ipta_OP"
                                                                    onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                                <label class="modern-checkbox-label" for="ipta_OP">
                                                                    <span class="checkbox-custom"></span>
                                                                    <span class="checkbox-text">KPM</span>
                                                                </label>
                                                            </div>

                                                            <div class="modern-checkbox-item" data-search="politeknik">
                                                                <input type="checkbox" name="carianIPTA[]" value="FB"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('FB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="modern-checkbox checkbox-filter" id="ipta_FB"
                                                                    onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                                <label class="modern-checkbox-label" for="ipta_FB">
                                                                    <span class="checkbox-custom"></span>
                                                                    <span class="checkbox-text">Politeknik</span>
                                                                </label>
                                                            </div>
                                                        <?php endif; ?>

                                                        <div class="modern-checkbox-item" data-search="uiam">
                                                            <input type="checkbox" name="carianIPTA[]" value="UY"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UY', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UY"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UY">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UIAM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="uitm">
                                                            <input type="checkbox" name="carianIPTA[]" value="UE"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UE', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UE"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UE">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UITM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="ukm">
                                                            <input type="checkbox" name="carianIPTA[]" value="UK"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UK', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UK"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UK">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UKM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="um">
                                                            <input type="checkbox" name="carianIPTA[]" value="UM"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UM"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UM">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="umpsa">
                                                            <input type="checkbox" name="carianIPTA[]" value="UJ"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UJ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UJ"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UJ">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UMPSA</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="umk">
                                                            <input type="checkbox" name="carianIPTA[]" value="UL"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UL', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UL"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UL">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UMK</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="ums">
                                                            <input type="checkbox" name="carianIPTA[]" value="UH"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UH', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UH"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UH">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UMS</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="umt">
                                                            <input type="checkbox" name="carianIPTA[]" value="UG"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UG', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UG"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UG">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UMT</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="unimap">
                                                            <input type="checkbox" name="carianIPTA[]" value="UR"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UR', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UR"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UR">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UNIMAP</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="unimas">
                                                            <input type="checkbox" name="carianIPTA[]" value="UW"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UW', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UW"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UW">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UNIMAS</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="unisza">
                                                            <input type="checkbox" name="carianIPTA[]" value="UD"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UD', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UD"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UD">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UNISZA</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="upm">
                                                            <input type="checkbox" name="carianIPTA[]" value="UP"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UP"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UP">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UPM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="upnm">
                                                            <input type="checkbox" name="carianIPTA[]" value="UZ"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UZ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UZ"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UZ">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UPNM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="upsi">
                                                            <input type="checkbox" name="carianIPTA[]" value="UA"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UA', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UA"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UA">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UPSI</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="usim">
                                                            <input type="checkbox" name="carianIPTA[]" value="UQ"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UQ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UQ"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UQ">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">USIM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="usm">
                                                            <input type="checkbox" name="carianIPTA[]" value="US"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('US', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_US"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_US">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">USM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="utm">
                                                            <input type="checkbox" name="carianIPTA[]" value="UT"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UT', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UT"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UT">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UTM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="utem">
                                                            <input type="checkbox" name="carianIPTA[]" value="UC"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UC"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UC">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UTeM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="uthm">
                                                            <input type="checkbox" name="carianIPTA[]" value="UB"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UB"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UB">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UTHM</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="uum">
                                                            <input type="checkbox" name="carianIPTA[]" value="UU"
                                                                <?php if(!empty(session()->get('jIPTA')) && in_array('UU', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="ipta_UU"
                                                                onchange="updateSelectedCount('iptaCollapse', 'iptaCount')">
                                                            <label class="modern-checkbox-label" for="ipta_UU">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">UUM</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <?php if(session()->get('jenprog') == 'spm'): ?>
                                        <!-- Peringkat Pengajian Dropdown -->
                                        <div class="modern-filter-group mb-3">
                                            <div class="modern-dropdown">
                                                <button class="modern-dropdown-toggle" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#peringkatCollapse" aria-expanded="false" aria-controls="peringkatCollapse">
                                                    <div class="dropdown-header">
                                                        <i class="fas fa-code-fork dropdown-icon"></i>
                                                        <span class="dropdown-title">Peringkat Pengajian</span>

                                                    </div>
                                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                                </button>

                                                <div class="collapse modern-dropdown-content" id="peringkatCollapse">
                                                    <div class="dropdown-search-container">
                                                        <input type="text" class="dropdown-search" placeholder="Cari peringkat..."
                                                               onkeyup="filterDropdownOptions('peringkatCollapse', this.value)">
                                                        <i class="fas fa-search search-icon"></i>
                                                    </div>

                                                    <div class="dropdown-options">
                                                        <div class="modern-checkbox-item" data-search="asasi matrikulasi">
                                                            <input type="checkbox" name="peringkatPengajian[]" value="0"
                                                                <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('0', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="peringkat0"
                                                                onchange="updateSelectedCount('peringkatCollapse', 'peringkatCount')">
                                                            <label class="modern-checkbox-label" for="peringkat0">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Asasi/Matrikulasi</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="sijil kredit graduan min 15">
                                                            <input type="checkbox" name="peringkatPengajian[]" value="1"
                                                                <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('1', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="peringkat1"
                                                                onchange="updateSelectedCount('peringkatCollapse', 'peringkatCount')">
                                                            <label class="modern-checkbox-label" for="peringkat1">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Sijil-Kredit Graduan Min 15</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="sijil kredit graduan min 30">
                                                            <input type="checkbox" name="peringkatPengajian[]" value="2"
                                                                <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('2', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="peringkat2"
                                                                onchange="updateSelectedCount('peringkatCollapse', 'peringkatCount')">
                                                            <label class="modern-checkbox-label" for="peringkat2">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Sijil-Kredit Graduan Min 30</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="sijil kredit graduan min 60">
                                                            <input type="checkbox" name="peringkatPengajian[]" value="3"
                                                                <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('3', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="peringkat3"
                                                                onchange="updateSelectedCount('peringkatCollapse', 'peringkatCount')">
                                                            <label class="modern-checkbox-label" for="peringkat3">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Sijil-Kredit Graduan Min 60</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="diploma">
                                                            <input type="checkbox" name="peringkatPengajian[]" value="4"
                                                                <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('4', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="peringkat4"
                                                                onchange="updateSelectedCount('peringkatCollapse', 'peringkatCount')">
                                                            <label class="modern-checkbox-label" for="peringkat4">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Diploma</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="diploma lanjutan">
                                                            <input type="checkbox" name="peringkatPengajian[]" value="5"
                                                                <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('5', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="peringkat5"
                                                                onchange="updateSelectedCount('peringkatCollapse', 'peringkatCount')">
                                                            <label class="modern-checkbox-label" for="peringkat5">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Diploma Lanjutan</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="sarjana muda">
                                                            <input type="checkbox" name="peringkatPengajian[]" value="6"
                                                                <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('6', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="peringkat6"
                                                                onchange="updateSelectedCount('peringkatCollapse', 'peringkatCount')">
                                                            <label class="modern-checkbox-label" for="peringkat6">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Sarjana Muda</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                        

                                        <!-- Program TVET Dropdown -->
                                        <div class="modern-filter-group mb-3">
                                            <div class="modern-dropdown">
                                                <button class="modern-dropdown-toggle" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#tvetCollapse" aria-expanded="false" aria-controls="tvetCollapse">
                                                    <div class="dropdown-header">
                                                        <i class="fas fa-cogs dropdown-icon"></i>
                                                        <span class="dropdown-title">Program TVET</span>

                                                    </div>
                                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                                </button>

                                                <div class="collapse modern-dropdown-content" id="tvetCollapse">
                                                    <div class="dropdown-options">
                                                        <div class="modern-checkbox-item" data-search="ya">
                                                            <input type="checkbox" name="pTVET[]" value="Y"
                                                                <?php if(!empty(session()->get('jTVET')) && in_array('Y', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="tvet_Y"
                                                                onchange="updateSelectedCount('tvetCollapse', 'tvetCount')">
                                                            <label class="modern-checkbox-label" for="tvet_Y">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Ya</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="tidak">
                                                            <input type="checkbox" name="pTVET[]" value="T"
                                                                <?php if(!empty(session()->get('jTVET')) && in_array('T', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="tvet_T"
                                                                onchange="updateSelectedCount('tvetCollapse', 'tvetCount')">
                                                            <label class="modern-checkbox-label" for="tvet_T">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Tidak</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Mod Pengajian Dropdown -->
                                        <div class="modern-filter-group mb-3">
                                            <div class="modern-dropdown">
                                                <button class="modern-dropdown-toggle" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#modCollapse" aria-expanded="false" aria-controls="modCollapse">
                                                    <div class="dropdown-header">
                                                        <i class="fas fa-pie-chart dropdown-icon"></i>
                                                        <span class="dropdown-title">Mod Pengajian</span>

                                                    </div>
                                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                                </button>

                                                <div class="collapse modern-dropdown-content" id="modCollapse">
                                                    <div class="dropdown-options">
                                                        <div class="modern-checkbox-item" data-search="2u2i 3u1i">
                                                            <input type="checkbox" name="ModPengajian[]" value="Y"
                                                                <?php if(!empty(session()->get('jMODpengajian')) && in_array('Y', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="mod_Y"
                                                                onchange="updateSelectedCount('modCollapse', 'modCount')">
                                                            <label class="modern-checkbox-label" for="mod_Y">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">2U2I/3U1I</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="konvensional">
                                                            <input type="checkbox" name="ModPengajian[]" value="T"
                                                                <?php if(!empty(session()->get('jMODpengajian')) && in_array('T', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="mod_T"
                                                                onchange="updateSelectedCount('modCollapse', 'modCount')">
                                                            <label class="modern-checkbox-label" for="mod_T">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Konvensional</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <?php if(session()->get('jenprog') == 'stpm'): ?>
                                        <!-- Double Degree Dropdown -->
                                        <div class="modern-filter-group mb-3">
                                            <div class="modern-dropdown">
                                                <button class="modern-dropdown-toggle" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#doubleCollapse" aria-expanded="false" aria-controls="doubleCollapse">
                                                    <div class="dropdown-header">
                                                        <i class="fas fa-certificate dropdown-icon"></i>
                                                        <span class="dropdown-title">Joint/Dual/Double Degree</span>

                                                    </div>
                                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                                </button>

                                                <div class="collapse modern-dropdown-content" id="doubleCollapse">
                                                    <div class="dropdown-options">
                                                        <div class="modern-checkbox-item" data-search="ya">
                                                            <input type="checkbox" name="pDoubleDegree[]" value="Y"
                                                                <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('Y', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="double_Y"
                                                                onchange="updateSelectedCount('doubleCollapse', 'doubleCount')">
                                                            <label class="modern-checkbox-label" for="double_Y">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Ya</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="tidak">
                                                            <input type="checkbox" name="pDoubleDegree[]" value="T"
                                                                <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('T', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="double_T"
                                                                onchange="updateSelectedCount('doubleCollapse', 'doubleCount')">
                                                            <label class="modern-checkbox-label" for="double_T">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Tidak</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Bertemu duga Dropdown -->
                                        <div class="modern-filter-group mb-3">
                                            <div class="modern-dropdown">
                                                <button class="modern-dropdown-toggle" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#temudugaCollapse" aria-expanded="false" aria-controls="temudugaCollapse">
                                                    <div class="dropdown-header">
                                                        <i class="fas fa-hashtag dropdown-icon"></i>
                                                        <span class="dropdown-title">Bertemu duga / Ujian</span>

                                                    </div>
                                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                                </button>

                                                <div class="collapse modern-dropdown-content" id="temudugaCollapse">
                                                    <div class="dropdown-options">
                                                        <div class="modern-checkbox-item" data-search="ya">
                                                            <input type="checkbox" name="pTemuduga[]" value="Y"
                                                                <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('Y', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="temuduga_Y"
                                                                onchange="updateSelectedCount('temudugaCollapse', 'temudugaCount')">
                                                            <label class="modern-checkbox-label" for="temuduga_Y">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Ya</span>
                                                            </label>
                                                        </div>

                                                        <div class="modern-checkbox-item" data-search="tidak">
                                                            <input type="checkbox" name="pTemuduga[]" value="T"
                                                                <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('T', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                class="modern-checkbox checkbox-filter" id="temuduga_T"
                                                                onchange="updateSelectedCount('temudugaCollapse', 'temudugaCount')">
                                                            <label class="modern-checkbox-label" for="temuduga_T">
                                                                <span class="checkbox-custom"></span>
                                                                <span class="checkbox-text">Tidak</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                        <!-- Merit Range Dropdown -->
                                        <div class="modern-filter-group mb-3">
                                            <div class="modern-dropdown">
                                                <button class="modern-dropdown-toggle" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#meritCollapse" aria-expanded="false" aria-controls="meritCollapse">
                                                    <div class="dropdown-header">
                                                        <i class="fas fa-line-chart dropdown-icon"></i>
                                                        <span class="dropdown-title">Purata Merit</span>
                                                    </div>
                                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                                </button>

                                                <div class="collapse modern-dropdown-content" id="meritCollapse">
                                                    <div class="merit-range-container">
                                                        <div class="merit-header">
                                                            <div class="merit-title-section">
                                                                <div class="merit-main-title">
                                                                    <i class="fas fa-percentage text-primary"></i>
                                                                    <span class="merit-title">Purata Merit</span>
                                                                </div>
                                                                <div class="merit-measurement">
                                                                    <small class="text-muted">Peratus (%)</small>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="merit-inputs">
                                                            <div class="input-column">
                                                                <div class="input-group mb-3">
                                                                    <label class="input-label">Minimum</label>
                                                                    <input type="number" class="form-control modern-input"
                                                                        id="meritMin" name="meritProgramMin"
                                                                        min="0" max="100" step="0.01"
                                                                        value="<?php echo e(old('meritProgramMin', session('meritProgramMin', 0))); ?>"
                                                                        placeholder="0">
                                                                </div>
                                                                <div class="input-group">
                                                                    <label class="input-label">Maksimum</label>
                                                                    <input type="number" class="form-control modern-input"
                                                                        id="meritMax" name="meritProgramMax"
                                                                        min="0" max="100" step="0.01"
                                                                        value="<?php echo e(old('meritProgramMax', session('meritProgramMax', 100))); ?>"
                                                                        placeholder="100">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                    </div>
                                    <!-- End Modern Filter Container -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-9 col-md-8 col-12 mt-5 pt-2 mt-sm-0 pt-sm-0">
                        <div class="row align-items-center">
                            <div class="col-lg-8 col-md-7">
                                <div class="section-title">
                                    <h5 class="mb-0">Paparan
                                        <?php echo e($SENARAI_PROGRAM->firstItem()); ?> - <?php echo e($SENARAI_PROGRAM->lastItem()); ?> daripada
                                        <?php echo e($SENARAI_PROGRAM->total()); ?> carian</h5>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <?php if(count($SENARAI_PROGRAM) != '0'): ?>
                                <?php $__currentLoopData = $SENARAI_PROGRAM; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $PROGRAM): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-12 mt-4 pt-2">
                                        <div class="card shop-list border-0 shadow position-relative focus-card h-100">
                                            <div class="row align-items-center no-gutters">


                                                <div class="flex-grow-1 d-flex flex-column justify-content-between">
                                                    <div class="card-body content p-4 pb-2">

                                                        <div class="d-flex align-items-center gap-3 mt-2 mb-3">
                                                                            <div class="col-lg-4 col-md-6">
                                                                                <?php echo $__env->make('programPengajian.logoUA-ILKA', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                            </div>

                                                                            <div>
                                                                                <a href="#" class="text-decoration-none text-dark h5 fw-bold">
                                                                                    <?php echo e($PROGRAM->nama_Program); ?>

                                                                                    <?php if($PROGRAM->program_Temuduga == 'Y'): ?>
                                                                                        <span class="badge bg-warning text-dark ms-2">#</span>
                                                                                    <?php endif; ?>
                                                                                </a>

                                                                                <p class="text-muted small mb-0" style="margin-top: -0.5rem;">
                                                                                    <?php echo e(Str::title($PROGRAM->nama_IPTA)); ?>

                                                                                </p>
                                                                            </div>
                                                          </div>
                                                            <div class="executive-badges-container mb-3">
                                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                                                <?php if($PROGRAM->program_FEEDER == 'Y'): ?>
                                                                                    <span class="badge executive-badge-feeder rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program pembekal kepada program Ijazah Sarjana Muda di Universiti Awam berkenaan sahaja.">
                                                                                        <i class="fas fa-seedling me-1"></i>Program Perintis (Feeder)
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                <?php if($PROGRAM->program_STEM == 'Y'): ?>
                                                                                    <span class="badge executive-badge-stem rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program dalam bidang Sains, Teknologi, Kejuruteraan dan Matematik.">
                                                                                        <i class="fas fa-atom me-1"></i>STEM
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                <?php if($PROGRAM->program_TVET == 'Y'): ?>
                                                                                    <span class="badge executive-badge-tvet rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah.">
                                                                                        <i class="fas fa-tools me-1"></i>TVET
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                <?php if($PROGRAM->program_BUMI == 'Y'): ?>
                                                                                    <span class="badge executive-badge-competitive rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Berketurunan Melayu, Anak Negeri Sabah, Anak Negeri Sarawak dan Orang Asli sahaja.">
                                                                                        <i class="fas fa-leaf me-1"> </i>Bumiputera
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                            <?php endif; ?>

                                                                            <?php if(session()->get('jenprog') == 'stpm'): ?>
                                                                                <?php if($PROGRAM->program_KOMPETITIF == 'Y'): ?>
                                                                                    <span class="badge executive-badge-competitive rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program pengajian popular dengan persaingan yang tinggi untuk kemasukan.">
                                                                                        <i class="fas fa-trophy me-1"></i>Kompetitif
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                <?php if($PROGRAM->program_BTECH == 'Y'): ?>
                                                                                    <span class="badge executive-badge-btech rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program Ijazah Sarjana Muda Teknologi Kejuruteraan yang diiktiraf oleh Lembaga Teknologis Malaysia (MBOT).">
                                                                                        <i class="fas fa-cogs me-1"></i>BTECH
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                <?php if($PROGRAM->program_TVET == 'Y'): ?>
                                                                                    <span class="badge executive-badge-tvet rounded-pill me-2 mb-1 css-tooltip"
                                                                                        data-tooltip="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah.">
                                                                                        <i class="fas fa-tools me-1"></i>TVET
                                                                                    </span>
                                                                                <?php endif; ?>
                                                                                  
                                                                            <?php endif; ?>
                                                                        </div>

                                                        
                                                        

                                                         <!-- Executive Data Labels Section -->
                                                        <div class="executive-data-section mb-4">
                                                            <div class="row g-3">
                                                                <!-- Kod Program -->
                                                                <div class="col-md-4">
                                                                    <div class="executive-data-card">
                                                                        <div class="executive-data-icon">
                                                                            <i class="fas fa-tag"></i>
                                                                        </div>
                                                                        <div class="executive-data-content">
                                                                            <span class="executive-data-label">KOD PROGRAM</span>
                                                                            <span class="executive-data-value"><?php echo e($PROGRAM->kod_Program); ?></span>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <!-- Tahun -->
                                                                <div class="col-md-4">
                                                                    <div class="executive-data-card">
                                                                        <div class="executive-data-icon">
                                                                            <i class="far fa-calendar-alt"></i>
                                                                        </div>
                                                                        <div class="executive-data-content">
                                                                            <span class="executive-data-label">TAHUN</span>
                                                                            <span class="executive-data-value"><?php echo e(session()->get('tahun_semasa')); ?></span>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <!-- Merit -->
                                                                <?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                                                <div class="col-md-4">
                                                                    <div class="executive-data-card">
                                                                        <div class="executive-data-icon">
                                                                            <i class="fas fa-chart-line"></i>
                                                                        </div>
                                                                        <div class="executive-data-content">
                                                                            <span class="executive-data-label">PURATA MARKAH MERIT</span>
                                                                            <span class="executive-data-value">
                                                                                <?php $__currentLoopData = $MAKLUMAT_PENGAJIAN; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $maklumat_Pengajian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                    <?php if($maklumat_Pengajian->kod_Program == $PROGRAM->kod_Program): ?>
                                                                                        <?php if($maklumat_Pengajian->merit_Program==''): ?>
                                                                                            Tiada
                                                                                        <?php else: ?>
                                                                                            <?php echo $maklumat_Pengajian->merit_Program; ?>%
                                                                                            <i class="fas fa-info-circle text-primary ms-1"
                                                                                               data-bs-toggle="tooltip"
                                                                                               data-bs-placement="top"
                                                                                               title="Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata."></i>
                                                                                        <?php endif; ?>
                                                                                    <?php endif; ?>
                                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>

                                                        <!-- Action Buttons Row -->
                                                        <div class="d-flex flex-wrap justify-content-center mb-3" style="gap: 6px;">
                                                            <!-- Syarat Program Button -->
                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                                <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                   id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                   class="btn btn-outline-primary btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                    <i class="fas fa-book mr-1"></i> Syarat
                                                                </a>
                                                            <?php elseif(session()->get('jenprog') == 'stpm'): ?>
                                                                <?php if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F'): ?>
                                                                    <a href="<?php echo e(url('ProgramPengajian/modalSyaratdiploma/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->jensetaraf . '/' . $PROGRAM->kod_Program)); ?>"
                                                                       id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                       class="btn btn-outline-primary btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                        <i class="fas fa-book mr-1"></i> Syarat
                                                                    </a>
                                                                <?php else: ?>
                                                                    <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                       id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                       class="btn btn-outline-primary btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                        <i class="fas fa-book mr-1"></i> Syarat
                                                                    </a>
                                                                <?php endif; ?>
                                                            <?php endif; ?>

                                                            <!-- Yuran Pengajian Button -->
                                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#yuran-pengajian"
                                                               class="btn btn-outline-success btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                <i class="fas fa-dollar-sign mr-1"></i> Yuran
                                                            </a>

                                                            <!-- Kampus Button -->
                                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#kampus__<?php echo e($PROGRAM->kod_Program); ?>"
                                                               class="btn btn-outline-info btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                <i class="fas fa-map-marked-alt mr-1"></i> Kampus
                                                            </a>

                                                            <!-- Laluan Kerjaya Button -->
                                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#laluan-kerjaya_<?php echo e($PROGRAM->kod_Program); ?>"
                                                               class="btn btn-outline-warning btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                <i class="fas fa-briefcase mr-1"></i> Kerjaya
                                                            </a>

                                                            <!-- Bidang NEC Button (conditional) -->
                                                            <?php if(substr(Request::route('kodkatag'),0,1)=='G'): ?>
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#bidangNEC_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                   class="btn btn-outline-secondary btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                    <i class="fas fa-bullseye mr-1"></i> NEC
                                                                </a>
                                                            <?php endif; ?>

                                                            <!-- Maklumat Program Button -->
                                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#maklumatProgram__<?php echo e($PROGRAM->kod_Program); ?>"
                                                               class="btn btn-outline-dark btn-sm flex-fill" style="font-size: 1rem; max-width: 140px;">
                                                                <i class="fas fa-info-circle mr-1"></i> Info
                                                            </a>
                                                        </div>








                                                        <ul class="list-unstyled mb-0 flex-wrap">
                                                           
                                                           
                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                              
                                                                <div class="modal fade kelayakanMinimum_Modal"
                                                                    id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    tabindex="-1" role="dialog"
                                                                    aria-labelledby="syarat-program-title"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                        role="document">
                                                                        <div
                                                                            class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                            <button type="button"
                                                                                class="close float-right mr-2"
                                                                                data-dismiss="modal" aria-label="Close"
                                                                                style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                                <span aria-hidden="true">&times;</span>
                                                                            </button>
                                                                            <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                                <div class="text-left">
                                                                                    <h4 class="text-center"><b>Syarat
                                                                                            Program</b>
                                                                                    </h4>
                                                                                    <div class="container mt-100 mt-60">
                                                                                        <div class="row">
                                                                                            <div class="col-12">
                                                                                                <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                    id="pills-tab"
                                                                                                    role="tablist">
                                                                                                    <!--Syarat Am Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 active rounded"
                                                                                                            id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-am"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Am</h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>

                                                                                                    <!--Syarat Khas Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 rounded"
                                                                                                            id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-Khas"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Khas
                                                                                                                </h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>
                                                                                                </ul>

                                                                                                <div class="tab-content"
                                                                                                    id="pills-tabContent"
                                                                                                    style="padding-top: 2rem!important">
                                                                                                    <!--Paparan Syarat Am Tab-->
                                                                                                    <div class="card border-0 tab-pane fade show active"
                                                                                                        id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                        <?php echo $__env->make('programPengajian.syarat_am_spm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                        

                                                                                                    </div>

                                                                                                    <!--Paparan Syarat khas Tab-->
                                                                                                    <div class="card border-0 tab-pane fade"
                                                                                                        id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                        <div class="text-muted contentLoad"
                                                                                                            style="font-weight: bold">
                                                                                                            <div
                                                                                                                align="center">
                                                                                                                <div
                                                                                                                    class="loader-spinner text-center">
                                                                                                                </div>
                                                                                                                <h4>Sila
                                                                                                                    tunggu
                                                                                                                    sebentar...
                                                                                                                </h4>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>



                                                            <?php elseif(session()->get('jenprog') == 'stpm'): ?>



                                                           

                                                            <div class="modal fade kelayakanMinimum_Modal"
                                                                id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                tabindex="-1" role="dialog"
                                                                aria-labelledby="syarat-program-title"
                                                                aria-hidden="true">
                                                                <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                    role="document" >
                                                                    <div
                                                                        class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                        <button type="button"
                                                                            class="close float-right mr-2"
                                                                            data-dismiss="modal" aria-label="Close"
                                                                            style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                            <span aria-hidden="true">&times;</span>
                                                                        </button>
                                                                        <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                            <div class="text-left">
                                                                                <h4 class="text-center"><b>Syarat
                                                                                        Program</b>
                                                                                </h4>
                                                                                <div class="container mt-100 mt-60">
                                                                                    <div class="row">
                                                                                        <div class="col-12">
                                                                                            <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                id="pills-tab"
                                                                                                role="tablist">
                                                                                                <!--Syarat Am Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 active rounded"
                                                                                                        id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-am"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Am</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <li <?php echo e($PROGRAM->kod_Program == 'UM6143001' ? 'hidden' : null); ?>

                                                                                                    class="nav-item col-4">
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-pendidikan"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Pendidikan</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                                <?php endif; ?>


                                                                                                <!--Syarat Khas Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-Khas"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Khas
                                                                                                            </h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                            </ul>

                                                                                            <div class="tab-content"
                                                                                                id="pills-tabContent"
                                                                                                style="padding-top: 2rem!important">
                                                                                                <!--Paparan Syarat Am Tab-->
                                                                                                <div class="card border-0 tab-pane fade show active"
                                                                                                    id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php if($PROGRAM->kategori_Pengajian=='G'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_g', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='E'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_e', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='F'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_f', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php else: ?>
																										<?php echo $__env->make('programPengajian.syarat_am_stpm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php endif; ?>

                                                                                                </div>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php echo $__env->make('programPengajian.syarat_am_pendidikan', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                </div>
                                                                                                <?php endif; ?>


                                                                                                <!--Paparan Syarat khas Tab-->
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                    <div class="text-muted contentLoad"
                                                                                                        style="font-weight: bold">
                                                                                                        <div
                                                                                                            align="center">
                                                                                                            <div
                                                                                                                class="loader-spinner text-center">
                                                                                                            </div>
                                                                                                            <h4>Sila
                                                                                                                tunggu
                                                                                                                sebentar...
                                                                                                            </h4>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>





                                                                
                                                            <?php endif; ?>
                                                            

                                                            
                                                            


                                                        </ul>
                                                    </div>
                                                </div>

                                                <!--end col-->
                                            </div>
                                            <!--end row-->
                                        </div>
                                        <!--end blog post-->
                                    </div>
                                    <?php echo $__env->make('programPengajian.modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <?php echo $__env->make('pageLock.tiadaMaklumat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <?php endif; ?>

                            <!-- PAGINATION START -->
                            <div class="col-12 mt-4 pt-2">
                                <?php echo $SENARAI_PROGRAM->links('programPengajian.list-paginator'); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
    <style>
        .merit-filter-mobile {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            margin: 0;
            width: 100%;
            max-width: 100%;
        }

        .merit-filter-mobile:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }

        .merit-header-mobile {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 1px solid #e9ecef;
        }

        .filter-title-mobile {
            font-size: 0.8rem;
            font-weight: 600;
            color: #495057;
        }

        .merit-inputs-mobile {
            margin: 0;
        }

        .input-row-mobile {
            display: flex;
            align-items: end;
            gap: 8px;
            width: 100%;
        }

        .input-group-mobile {
            flex: 1;
            min-width: 0;
        }

        .input-label-mobile {
            display: block;
            font-size: 0.7rem;
            font-weight: 500;
            color: #6c757d;
            margin-bottom: 3px;
            text-align: center;
        }

        .mobile-input {
            width: 100% !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 4px !important;
            padding: 6px 8px !important;
            font-size: 0.8rem !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
            background: #ffffff !important;
            box-sizing: border-box !important;
            text-align: center !important;
        }

        .mobile-input:focus {
            border-color: #007bff !important;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1) !important;
            outline: none !important;
        }

        .mobile-input:hover {
            border-color: #007bff !important;
        }

        .mobile-input::placeholder {
            color: #adb5bd;
            font-size: 0.75rem;
        }

        .input-separator-mobile {
            display: flex;
            align-items: center;
            justify-content: center;
            padding-bottom: 4px;
            font-weight: 600;
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* Mobile-first responsive design */
        @media (max-width: 576px) {
            .merit-filter-mobile {
                padding: 6px;
            }

            .input-row-mobile {
                gap: 6px;
            }

            .mobile-input {
                padding: 5px 6px !important;
                font-size: 0.75rem !important;
            }

            .input-label-mobile {
                font-size: 0.65rem;
                margin-bottom: 2px;
            }

            .filter-title-mobile {
                font-size: 0.75rem;
            }
        }

        /* Tablet and up */
        @media (min-width: 577px) and (max-width: 768px) {
            .merit-filter-mobile {
                padding: 10px;
            }

            .input-row-mobile {
                gap: 10px;
            }

            .mobile-input {
                padding: 8px 10px !important;
                font-size: 0.85rem !important;
            }
        }

        /* Desktop */
        @media (min-width: 769px) {
            .input-row-mobile {
                gap: 12px;
            }

            .mobile-input {
                padding: 8px 12px !important;
                font-size: 0.9rem !important;
            }
        }

        /* Override Bootstrap styles */
        .mobile-input.form-control {
            display: block !important;
            width: 100% !important;
            line-height: 1.4 !important;
            color: #495057 !important;
            background-color: #fff !important;
            background-clip: padding-box !important;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
        }

        /* Ensure proper box model */
        .merit-filter-mobile * {
            box-sizing: border-box;
        }

        /* Touch-friendly inputs for mobile */
        @media (pointer: coarse) {
            .mobile-input {
                min-height: 44px !important;
                padding: 8px 12px !important;
            }
        }

        .btn-modern-square {
    border-radius: 6px !important;
    padding: 0.5rem 0.75rem !important;
    min-width: 36px;
    min-height: 36px;
    font-size: 0.95rem;
    font-weight: 500;
    letter-spacing: 0.01em;
    box-shadow: 0 1px 4px rgba(40,167,69,0.07);
    transition: background 0.18s, box-shadow 0.18s, transform 0.09s;
    background: #fff;
    border: 1px solid #e9ecef;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.4em;
    line-height: 1.1;
}
.btn-modern-square.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #43e97b 100%);
    color: #fff;
    border: none;
}
.btn-modern-square.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #ff758c 100%);
    color: #fff;
    border: none;
}
.btn-modern-square:hover, .btn-modern-square:focus {
    box-shadow: 0 2px 8px rgba(40,167,69,0.13);
    transform: translateY(-1px) scale(1.025);
    opacity: 0.98;
}
.btn-modern-square i {
    font-size: 1em;
    margin-right: 0.3em;
}
    </style>

    <script src="<?php echo e(asset('/assets/js/range-slider.js')); ?>"></script>

    <script>
    // Modern Dropdown Functionality with Filter Tags
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize filter tags on page load
        updateFilterTags();

        // Add Bootstrap 5 collapse event listeners
        document.querySelectorAll('.modern-dropdown-toggle').forEach(function(toggle) {
            toggle.addEventListener('click', function() {
                const target = this.getAttribute('data-bs-target');
                const collapse = document.querySelector(target);
                const isExpanded = this.getAttribute('aria-expanded') === 'true';

                // Toggle aria-expanded
                this.setAttribute('aria-expanded', !isExpanded);

                // Toggle collapse
                if (isExpanded) {
                    collapse.classList.remove('show');
                } else {
                    collapse.classList.add('show');
                }
            });
        });

        // Update all onchange handlers to use updateFilterTags
        document.querySelectorAll('.modern-checkbox').forEach(function(checkbox) {
            checkbox.addEventListener('change', updateFilterTags);
        });

        // Add event listeners for merit range inputs
        const meritMin = document.getElementById('meritMin');
        const meritMax = document.getElementById('meritMax');
        if (meritMin) meritMin.addEventListener('input', updateFilterTags);
        if (meritMax) meritMax.addEventListener('input', updateFilterTags);

        // Remove all inline onchange handlers since we're using event listeners
        document.querySelectorAll('[onchange*="updateSelectedCount"]').forEach(function(element) {
            element.removeAttribute('onchange');
        });
    });

    // Function to filter dropdown options based on search input
    function filterDropdownOptions(collapseId, searchValue) {
        const collapse = document.getElementById(collapseId);
        const items = collapse.querySelectorAll('.modern-checkbox-item');
        const searchTerm = searchValue.toLowerCase().trim();

        items.forEach(function(item) {
            const searchData = item.getAttribute('data-search').toLowerCase();
            const shouldShow = searchData.includes(searchTerm);

            if (shouldShow) {
                item.classList.remove('hidden');
                item.style.display = '';
            } else {
                item.classList.add('hidden');
                item.style.display = 'none';
            }
        });
    }

    // Filter configuration with labels and colors
    const filterConfig = {
        'pBidang[]': {
            name: 'Bidang',
            class: 'bidang',
            options: {
                '00': 'Program Dan Kelayakan Generik',
                '01': 'Pendidikan',
                '02': 'Sastera Dan Kemanusiaan',
                '03': 'Sains Sosial, Kewartawanan Dan Maklumat',
                '04': 'Perniagaan, Pentadbiran Dan Perundangan',
                '05': 'Sains Semulajadi, Matematik Dan Statistik',
                '06': 'Teknologi Maklumat Dan Komunikasi',
                '07': 'Kejuruteraan, Pembuatan Dan Pembinaan',
                '08': 'Pertanian, Perhutanan, Perikanan Dan Vaterinar',
                '09': 'Kesihatan Dan Kebajikan',
                '10': 'Perkhidmatan'
            }
        },
        'carianIPTA[]': {
            name: 'IPTA',
            class: 'ipta',
            options: {
                'OM': 'Kolej Mara', 'FC': 'Kolej Komuniti', 'OP': 'KPM', 'FB': 'Politeknik',
                'UY': 'UIAM', 'UE': 'UITM', 'UK': 'UKM', 'UM': 'UM', 'UJ': 'UMPSA',
                'UL': 'UMK', 'UH': 'UMS', 'UG': 'UMT', 'UR': 'UNIMAP', 'UW': 'UNIMAS',
                'UD': 'UNISZA', 'UP': 'UPM', 'UZ': 'UPNM', 'UA': 'UPSI', 'UQ': 'USIM',
                'US': 'USM', 'UT': 'UTM', 'UC': 'UTeM', 'UB': 'UTHM', 'UU': 'UUM'
            }
        },
        'peringkatPengajian[]': {
            name: 'Peringkat',
            class: 'peringkat',
            options: {
                '0': 'Asasi/Matrikulasi',
                '1': 'Sijil-Kredit Graduan Min 15',
                '2': 'Sijil-Kredit Graduan Min 30',
                '3': 'Sijil-Kredit Graduan Min 60',
                '4': 'Diploma',
                '5': 'Diploma Lanjutan',
                '6': 'Sarjana Muda'
            }
        },
        'pTVET[]': {
            name: 'TVET',
            class: 'tvet',
            options: { 'Y': 'Ya', 'T': 'Tidak' }
        },
        'ModPengajian[]': {
            name: 'Mod',
            class: 'mod',
            options: { 'Y': '2U2I/3U1I', 'T': 'Konvensional' }
        },
        'pDoubleDegree[]': {
            name: 'Double Degree',
            class: 'double',
            options: { 'Y': 'Ya', 'T': 'Tidak' }
        },
        'pTemuduga[]': {
            name: 'Temuduga',
            class: 'temuduga',
            options: { 'Y': 'Ya', 'T': 'Tidak' }
        }
    };

    // Main function to update filter tags display
    function updateFilterTags() {
        const tagsWrapper = document.getElementById('filterTagsWrapper');
        const clearAllBtn = document.querySelector('.clear-all-filters');
        const noFiltersMsg = document.querySelector('.no-filters-message');

        // Clear existing tags
        tagsWrapper.innerHTML = '';

        let hasFilters = false;

        // Check each filter type
        Object.keys(filterConfig).forEach(function(filterName) {
            const checkboxes = document.querySelectorAll(`input[name="${filterName}"]:checked`);

            if (checkboxes.length > 0) {
                hasFilters = true;
                const config = filterConfig[filterName];

                checkboxes.forEach(function(checkbox) {
                    const value = checkbox.value;
                    const label = config.options[value] || value;

                    const tag = createFilterTag(config.name, label, config.class, filterName, value);
                    tagsWrapper.appendChild(tag);
                });
            }
        });

        // Handle merit range
        const meritMin = document.getElementById('meritMin');
        const meritMax = document.getElementById('meritMax');
        if (meritMin && meritMax) {
            const minVal = parseFloat(meritMin.value) || 0;
            const maxVal = parseFloat(meritMax.value) || 100;

            if (minVal > 0 || maxVal < 100) {
                hasFilters = true;
                const tag = createFilterTag('Merit', `${minVal}% - ${maxVal}%`, 'merit', 'merit', 'range');
                tagsWrapper.appendChild(tag);
            }
        }

        // Show/hide elements based on filter state
        if (hasFilters) {
            clearAllBtn.style.display = 'block';
            if (noFiltersMsg) noFiltersMsg.style.display = 'none';
        } else {
            clearAllBtn.style.display = 'none';
            if (!noFiltersMsg) {
                const msg = document.createElement('div');
                msg.className = 'no-filters-message';
                msg.innerHTML = '<i class="fas fa-info-circle me-2"></i>Tiada filter dipilih';
                tagsWrapper.appendChild(msg);
            } else {
                noFiltersMsg.style.display = 'flex';
            }
        }
    }

    // Function to create a filter tag element
    function createFilterTag(category, value, cssClass, filterName, filterValue) {
        const tag = document.createElement('div');
        tag.className = `filter-tag ${cssClass}`;
        tag.innerHTML = `
            <span class="filter-tag-category">${category}:</span>
            <span class="filter-tag-value">${value}</span>
            <button type="button" class="filter-tag-remove" onclick="removeFilter('${filterName}', '${filterValue}')">
                <i class="fas fa-times"></i>
            </button>
        `;
        return tag;
    }

    // Function to remove individual filter
    function removeFilter(filterName, filterValue) {
        if (filterName === 'merit') {
            // Reset merit range
            const meritMin = document.getElementById('meritMin');
            const meritMax = document.getElementById('meritMax');
            if (meritMin) meritMin.value = 0;
            if (meritMax) meritMax.value = 100;
        } else {
            // Uncheck the specific checkbox
            const checkbox = document.querySelector(`input[name="${filterName}"][value="${filterValue}"]`);
            if (checkbox) {
                checkbox.checked = false;
            }
        }

        updateFilterTags();
    }

    // Function to clear all filters
    function clearAllFilters() {
        // Uncheck all checkboxes
        document.querySelectorAll('.modern-checkbox:checked').forEach(function(checkbox) {
            checkbox.checked = false;
        });

        // Reset merit range
        const meritMin = document.getElementById('meritMin');
        const meritMax = document.getElementById('meritMax');
        if (meritMin) meritMin.value = 0;
        if (meritMax) meritMax.value = 100;

        updateFilterTags();
    }

    // Enhanced search functionality with debouncing
    let searchTimeout;
    function debouncedSearch(collapseId, searchValue) {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            filterDropdownOptions(collapseId, searchValue);
        }, 300);
    }

    // Add smooth animations for dropdown toggles
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggles = document.querySelectorAll('.modern-dropdown-toggle');

        dropdownToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function() {
                const targetId = this.getAttribute('data-bs-target');
                const targetElement = document.querySelector(targetId);

                // Add smooth transition
                if (targetElement) {
                    targetElement.style.transition = 'all 0.35s ease-in-out';
                }
            });
        });
    });

    // Keyboard navigation support
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close all open dropdowns on Escape key
            document.querySelectorAll('.modern-dropdown-content.show').forEach(function(dropdown) {
                dropdown.classList.remove('show');
                const toggle = document.querySelector('[data-bs-target="#' + dropdown.id + '"]');
                if (toggle) {
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });

    // Add click outside to close functionality
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.modern-dropdown')) {
            document.querySelectorAll('.modern-dropdown-content.show').forEach(function(dropdown) {
                dropdown.classList.remove('show');
                const toggle = document.querySelector('[data-bs-target="#' + dropdown.id + '"]');
                if (toggle) {
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });

    // Merit range validation
    document.addEventListener('DOMContentLoaded', function() {
        const meritMin = document.getElementById('meritMin');
        const meritMax = document.getElementById('meritMax');

        if (meritMin && meritMax) {
            function validateMeritRange() {
                const minVal = parseFloat(meritMin.value) || 0;
                const maxVal = parseFloat(meritMax.value) || 100;

                if (minVal > maxVal) {
                    meritMax.value = minVal;
                }

                // Update merit count display
                const meritCount = document.getElementById('meritCount');
                if (meritCount) {
                    meritCount.textContent = minVal + '% - ' + maxVal + '%';
                }
            }

            meritMin.addEventListener('input', validateMeritRange);
            meritMax.addEventListener('input', validateMeritRange);

            // Initialize on page load
            validateMeritRange();
        }
    });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/index.blade.php ENDPATH**/ ?>