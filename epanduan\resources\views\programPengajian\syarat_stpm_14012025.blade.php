@foreach ($<PERSON><PERSON>RAI_PROGRAM as $PROGRAM)
    @php

        $jum_aliran = DB::connection('emas')->table('syarat_khas_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kate<PERSON><PERSON>_<PERSON>jian)->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -3, 1)'))->get();


        // KUMPULAN 0
        $syaratkhas_nn_stpm = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->ka<PERSON><PERSON><PERSON>_<PERSON>)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_nn_spm = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_g1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_ga = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_sk = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->get();
        $syaratkhas_f4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f5 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syarat_muet = DB::connection('emas')->table('stpm_upuplus_papar_syarat_band_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('Syarat_MUET','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $syarat_1119 = DB::connection('emas')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(Programkod, -3, 1)'), ['1','2','3','4'])->where('Syarat_1119','1')->where('sesi',session()->get('sesi_semasa'))->get();


        // KUMPULAN 1
        $syaratkhas_nn_stpm_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_nn_spm_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_g1_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_ga_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g2_0_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g2_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g3_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_sk_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f1_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f2_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f3_0_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f3_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->get();
        $syaratkhas_f4_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f5_0_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f5_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syarat_muet_1 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_band_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('Syarat_MUET','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $syarat_1119_1 = DB::connection('emas')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(Programkod, -3, 1)'), '1')->where('Syarat_1119','1')->where('sesi',session()->get('sesi_semasa'))->get();


        // KUMPULAN 2
        $syaratkhas_nn_stpm_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_nn_spm_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_g1_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_ga_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g2_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g3_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_sk_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f1_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f2_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f3_0_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f3_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->get();
        $syaratkhas_f4_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f5_0_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f5_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syarat_muet_2 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_band_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('Syarat_MUET','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $syarat_1119_2 = DB::connection('emas')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(Programkod, -3, 1)'), '2')->where('Syarat_1119','1')->where('sesi',session()->get('sesi_semasa'))->get();


        // KUMPULAN 3
        $syaratkhas_nn_stpm_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_nn_spm_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_g1_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_ga_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g2_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g3_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_sk_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f1_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f2_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f3_0_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f3_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->get();
        $syaratkhas_f4_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f5_0_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f5_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syarat_muet_3 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_band_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('Syarat_MUET','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $syarat_1119_3 = DB::connection('emas')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(Programkod, -3, 1)'), '3')->where('Syarat_1119','1')->where('sesi',session()->get('sesi_semasa'))->get();

        // KUMPULAN 4
        $syaratkhas_nn_stpm_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_nn_spm_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->get();
        $syaratkhas_g1_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_ga_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g2_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_g3_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_sk_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_yn_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f1_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f2_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f3_0_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f3_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->get();
        $syaratkhas_f4_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f5_0_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syaratkhas_f5_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_stpm_f5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, 1, 9)'))->groupby(DB::raw('SUBSTR(PROGRAMKOD, -2, 1)'))->groupby('KODSUBJEK_1')->orderby('ORDERID','ASC')->orderby('ORDERID2','ASC')->get();
        $syarat_muet_4 = DB::connection('emas')->table('stpm_upuplus_papar_syarat_band_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('Syarat_MUET','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $syarat_1119_4 = DB::connection('emas')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(Programkod, -3, 1)'), '4')->where('Syarat_1119','1')->where('sesi',session()->get('sesi_semasa'))->get();

        $syarat_umur = DB::connection('emas')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('Syarat_Umur','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $syarat_kahwin = DB::connection('emas')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('Syarat_taraf_perkahwinan','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $program  = DB::connection('emas')->table('program_stpm')->where(DB::raw('substr(KODPROGRAM, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(KODPROGRAM, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(KODPROGRAM, -3, 1)'), '1')->where('STATUS_TAWAR','Y')->where('sesi',session()->get('sesi_semasa'))->groupby('KODPROGRAM_PAPAR')->get();

        $syarat_umur_0 = DB::connection('emas')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('Syarat_Umur','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $syarat_kahwin_0 = DB::connection('emas')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('Syarat_taraf_perkahwinan','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $program_0  = DB::connection('emas')->table('program_stpm')->where(DB::raw('substr(KODPROGRAM, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(KODPROGRAM, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(KODPROGRAM, -3, 1)'), ['1','2','3','4'])->where('STATUS_TAWAR','Y')->where('sesi',session()->get('sesi_semasa'))->groupby('KODPROGRAM_PAPAR')->get();



        $syarat_diploma  = DB::connection('emas')->table('syarat_khas_diploma')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where('KATEGORI',$PROGRAM->kategori_Pengajian)->where('JENSETARAF',$PROGRAM->jensetaraf)->where('sesi',session()->get('sesi_semasa'))->get();

        $syarat_stpm_matrik_0  = DB::connection('emas')->table('stpm_upuplus_papar_syarat_pngk_rstpm_matrik')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('Syarat_PNGK_STPM','1')->where('sesi',session()->get('sesi_semasa'))->get();



        $syarat_stpm_matrik  = DB::connection('emas')->table('stpm_upuplus_papar_syarat_pngk_rstpm_matrik')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('Syarat_PNGK_STPM','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $syarat_stam_0  = DB::connection('emas')->table('stpm_upuplus_papar_syarat_tahap_rstam')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('syarat_tahap_STAM','1')->where('sesi',session()->get('sesi_semasa'))->get();
        $syarat_stam  = DB::connection('emas')->table('stpm_upuplus_papar_syarat_tahap_rstam')->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('syarat_tahap_STAM','1')->where('sesi',session()->get('sesi_semasa'))->get();


        $operator100='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator101='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator102='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator103='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator104='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';

        $operator200='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator201='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator202='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator203='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator204='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator205='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator206='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator207='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator208='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator209='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator210='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator211='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator212='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator213='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator214='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator215='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator216='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator217='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator218='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';

        $operator300='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator301='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator302='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';

        $operator400='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator401='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator402='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';
        $operator403='<span style="text-align:center; padding-top:2px; padding-bottom:12px;"></span>';

    @endphp
@endforeach
@if($PROGRAM->kategori_Pengajian!='G' && $PROGRAM->kategori_Pengajian!='E' && $PROGRAM->kategori_Pengajian!='F')

<ol style="padding-left: 2em; line-height: 1.5rem;" style="list-style-type:decimal;">

    @include('programPengajian.cetak_syarat_stpm.syarat_pngk_0')
    @include('programPengajian.cetak_syarat_stpm.syarat_pngk')

    {{-- KUMPULAN 0 --}}
    {{-- ################################################################################################################ --}}


    @if(count($syaratkhas_nn_stpm) > 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_stpm_nn')
    @endif

    @if(count($syaratkhas_g1) > 0 && count($syaratkhas_ga) == 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_g1')
    @endif

    @if(count($syaratkhas_g1) == 0 && count($syaratkhas_ga) > 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_ga')
    @endif

    @if(count($syaratkhas_g1) > 0 && count($syaratkhas_ga) > 0 )
        @if(count($syaratkhas_sk) > 0)
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_sk1')
        @endif
    @endif

    @if(count($syaratkhas_f5) > 0)
        @if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5) > 0) && !empty($syaratkhas_f5[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f5')
        @endif
    @endif

    @if(count($syaratkhas_f2) > 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f2')
    @endif

    @if(count($syaratkhas_f4) > 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f4')
    @endif

    @if(count($syaratkhas_nn_spm) > 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_spm_nn')
    @endif


    @if(count($syaratkhas_g2) > 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_g2')
        {!! $operator302 !!}
    @endif

    @if(count($syaratkhas_g3) > 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_g3')
    @endif


    @if(count($syaratkhas_f1) > 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f1')
    @endif

    @if(count($syaratkhas_f3) == '1')
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_khas_f3')
    @endif

    @if(count($syarat_muet) > 0)
        @include('programPengajian.cetak_syarat_stpm.k0.syarat_muet')
    @endif


    {{-- KUMPULAN 1 --}}
    {{-- ################################################################################################################ --}}

    @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) &&  count($syaratkhas_nn_stpm_1) > 0 && $syaratkhas_g1_1[0]->KODSUBJEK_1 == $syaratkhas_g1_2[0]->KODSUBJEK_1)
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
    @endif


    @if(count($syaratkhas_nn_stpm_1) > 0)
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_stpm_nn')

        {!! $operator103 !!}
        {!! $operator401 !!}
        {!! $operator216 !!}
    @endif


    {{-- SYARAT FLESIBLE 2 STPM DI SUSUN KE ATAS SEKIRANYA HANYA TERDAPAT 1 KUMPULAN SHAJA --}}
    @if(count($syaratkhas_f5_1) > 0)
        @if((count($syaratkhas_f5_1) > 0 && count($syaratkhas_f5_2) > 0) && $syaratkhas_f5_1[0]->KODSUBJEK_1 == $syaratkhas_f5_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f5')
        @endif
    @endif


    @if(count($syaratkhas_g1_1) > 0 && count($syaratkhas_ga_1) == 0)



        @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) &&  count($syaratkhas_nn_stpm_1) == 0 && $syaratkhas_g1_1[0]->KODSUBJEK_1 == $syaratkhas_g1_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
        @endif



        @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && $syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1 && (count($syaratkhas_g1_1) == count($syaratkhas_g1_2)) && count($syaratkhas_g2_2) == 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1_0')
        @endif

        @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && $syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1 && (count($syaratkhas_g1_1) == count($syaratkhas_g1_2)) && count($syaratkhas_g2_2) > 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
        @endif

        @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && $syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1 && count($syaratkhas_g1_1) != count($syaratkhas_g1_2))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
        @endif

        @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) == 0) && !empty($syaratkhas_g1_1[0]->KODSUBJEK_1) && count($syaratkhas_nn_stpm_1) > 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1_0')
        @endif

        @if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) == 0) && !empty($syaratkhas_g1_1[0]->KODSUBJEK_1) && count($syaratkhas_nn_stpm_1) == 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1')
        @endif
        {{-- @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g1') --}}

        {!! $operator102 !!}
        {!! $operator104 !!}
    @endif

    @if(count($syaratkhas_g1_1) == 0 && count($syaratkhas_ga_1) > 0)
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_ga')
        {!! $operator102 !!}

        {!! $operator104 !!}
    @endif

    @if(count($syaratkhas_g1_1) > 0 && count($syaratkhas_ga_1) > 0 )
        @if(count($syaratkhas_sk_1) > 0)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_sk1')
        @endif
        {!! $operator102 !!}

        {!! $operator104 !!}
    @endif


    @if(count($syaratkhas_f2_1) > 0)

        @if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) > 0) && $syaratkhas_f2_1[0]->KUMPULAN == $syaratkhas_f2_2[0]->KUMPULAN)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f2')
        @endif

        @if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) == 0) &&  !empty($syaratkhas_f2_1[0]->KUMPULAN))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f2')
        @endif

    @endif

    @if(count($syaratkhas_f4_1) > 0)
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f4')
    @endif


    @if((count($jum_aliran)=='2' && count($syaratkhas_f5_1) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f5_1) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f5_1) =='4'))
        {{-- @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f5_0') --}}
    @endif


    @if(count($syaratkhas_nn_spm_1) > 0)

        @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && $syaratkhas_nn_spm_1[0]->KODSUBJEK_1 != $syaratkhas_nn_spm_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
        @endif

        @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) == 0) &&  !empty($syaratkhas_nn_spm_1[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
        @endif

        @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) == 0) &&  !empty($syaratkhas_nn_spm_2[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
        @endif

        @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && ($syaratkhas_nn_spm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_spm_2[0]->KODSUBJEK_1) && ($syaratkhas_nn_spm_1[0]->MINGRED != $syaratkhas_nn_spm_2[0]->MINGRED))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
        @endif

        {!! $operator100 !!}

    @endif


    @if(count($syaratkhas_g2_1) > 0)

        @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 != $syaratkhas_g2_2[0]->KODSUBJEK_1)
            {{-- @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2_0') --}}
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
        @endif

        @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 == $syaratkhas_g2_2[0]->KODSUBJEK_1 && $syaratkhas_g2_1[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_g2_2[0]->KET_JUMLAH_MIN_SUBJEK)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
        @endif

        @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) == 0 && count($syaratkhas_g1_1) == 0 && count($syaratkhas_g2_1) == 0) && !empty($syaratkhas_g2_1[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2_0')
        @endif

        @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) == 0 && count($syaratkhas_g1_1) == 0 && count($syaratkhas_g2_1) != 0) && !empty($syaratkhas_g2_1[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
        @endif

        @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) == 0 && count($syaratkhas_g1_1) > 0) && !empty($syaratkhas_g2_1[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
        @endif

        @if((count($syaratkhas_g2_1) == 0 && count($syaratkhas_g2_2) > 0) && !empty($syaratkhas_g2_2[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
        @endif

    @endif



    @if(count($syaratkhas_g3_1) > 0)
        @if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) > 0) && $syaratkhas_g3_1[0]->KODSUBJEK_1 != $syaratkhas_g3_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3')
        @endif

        @if((count($syaratkhas_g3_1) == 0 && count($syaratkhas_g3_2) > 0) && !empty($syaratkhas_g3_2[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3')
        @endif

        @if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) ==  0) && !empty($syaratkhas_g3_1[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3')
        @endif
    @endif


    {{-- @if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) > 0)
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f1')
    @endif
    --}}

    @if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) == 0)
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f1')
    @endif


    @if((count($jum_aliran)=='2' && count($syaratkhas_f3_1) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_1) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_1) =='4'))
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f3_0')
    @endif




    @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) && $syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND && $syarat_muet_1[0]->MUET2_Band != $syarat_muet_2[0]->MUET2_Band)


        @if((count($syarat_1119_1) > 0 && count($syarat_1119_2) > 0) &&  $syarat_1119_1[0]->gred_1119 != $syarat_1119_2[0]->gred_1119 )
            {!! $operator400 !!}
        @endif

        @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) &&  ($syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND) && (empty($syarat_1119_1[0]->gred_1119) && empty($syarat_1119_2[0]->gred_1119)))
            {!! $operator400 !!}
        @endif


        @include('programPengajian.cetak_syarat_stpm.k1.syarat_muet')


    @endif

    {{-- @if((count($syarat_muet_1) == 0 && count($syarat_muet_2) > 0))
        {!! $operator400 !!}
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_muet_0')
    @endif --}}


    {{-- @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) == 0))
        {!! $operator400 !!}
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_muet_0')
    @endif --}}



    {{-- KUMPULAN 2 --}}
    {{-- ################################################################################################################ --}}


    @if(count($jum_aliran) > 1)
        <p style="text-align:center; padding-top:12px;"><b>ATAU</b></p>
    @endif


    @if(count($syaratkhas_nn_stpm_2) > 0)

        @if((count($syaratkhas_nn_stpm_1) > 0 && count($syaratkhas_nn_stpm_2) > 0) && $syaratkhas_nn_stpm_1[0]->KODSUBJEK_1 != $syaratkhas_nn_stpm_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_stpm_nn')
        @endif

		@if((count($syaratkhas_nn_stpm_1) > 0 && count($syaratkhas_nn_stpm_2) > 0) && $syaratkhas_nn_stpm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_stpm_2[0]->KODSUBJEK_1 && $syaratkhas_nn_stpm_1[0]->MINGRED != $syaratkhas_nn_stpm_2[0]->MINGRED)
			@include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_stpm_nn')
		@endif

        @if((count($syaratkhas_nn_stpm_1) == 0 && count($syaratkhas_nn_stpm_2) > 0) &&  !empty($syaratkhas_nn_stpm_2[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_stpm_nn')
        @endif

        {!! $operator205!!}

    @endif


    @if(count($syaratkhas_g1_2) > 0 && count($syaratkhas_ga_2) == 0)

		@if((!empty($syaratkhas_g1_1[1]) && $syaratkhas_g1_1[1]->KODSUBJEK_1 != '') && (!empty($syaratkhas_g1_2[1]) && $syaratkhas_g1_2[1]->KODSUBJEK_1 != ''))

			@if(count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0)
				@include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
			@endif

		@else

			@if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && ($syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1) && ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK))
				@include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
			@endif

			@if(count($syaratkhas_g1_1) == 0 && count($syaratkhas_g1_2) > 0)
				@include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
			@endif


			@if((count($syaratkhas_g1_1) > 0 && count($syaratkhas_g1_2) > 0) && ($syaratkhas_g1_1[0]->KODSUBJEK_1 != $syaratkhas_g1_2[0]->KODSUBJEK_1) && ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK))
				@include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
			@endif


			@if(count($jum_aliran) == 2 && (count($syaratkhas_g1_1) > 0 && (count($syaratkhas_g1_2) > 0 && count($syaratkhas_g2_2) > 0))  && ($syaratkhas_g1_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g1_2[0]->KET_JUMLAH_MIN_SUBJEK) && (count($syaratkhas_g1_1)!=count($syaratkhas_g1_2)))
				@include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
			@endif

			@if((count($syaratkhas_g1_1) > 0 && (count($syaratkhas_g1_2) > 0 && count($syaratkhas_nn_spm_2) > 0)  && count($syaratkhas_nn_stpm_1) == 0 ))
				@include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g1')
			@endif
		@endif

        {!! $operator202!!}
        {!! $operator204!!}
        {!! $operator213!!}
        {!! $operator217!!}

    @endif

    @if(count($syaratkhas_g1_2) == 0 && count($syaratkhas_ga_2) > 0)
        @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_ga')

        {!! $operator202!!}
        {!! $operator204!!}
    @endif

    @if(count($syaratkhas_g1_2) > 0 && count($syaratkhas_ga_2) > 0 )
        @if(count($syaratkhas_sk_2) > 0)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_sk1')
        @endif

        {!! $operator202!!}
        {!! $operator204!!}
    @endif


    @if(count($syaratkhas_f2_2))
        @if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) > 0) && $syaratkhas_f2_1[0]->KUMPULAN != $syaratkhas_f2_2[0]->KUMPULAN)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2')
        @endif

        @if((count($syaratkhas_f2_1) > 0 && count($syaratkhas_f2_2) == 0) &&  !empty($syaratkhas_f2_2[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2')
        @endif


        @if(count($jum_aliran) == 2 && (count($syaratkhas_f2_1) == 0 && count($syaratkhas_f2_2) > 0) && count($syaratkhas_g2_2) > 0)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2')
        @endif

        @if((count($syaratkhas_f2_2) > 0 && count($syaratkhas_f2_3) > 0) && $syaratkhas_f2_2[0]->KUMPULAN == $syaratkhas_f2_3[0]->KUMPULAN)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f2')
        @endif

        {!! $operator209 !!}
        {!! $operator212 !!}

    @endif


    @if(count($syaratkhas_f5_2) > 0)
        @if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5_2) > 0) && !empty($syaratkhas_f5_2[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f5')
        @endif
    {!! $operator208 !!}
    @endif


    @if(count($syaratkhas_f4_2) > 0)
        @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f4')
    @endif


    @if(count($syaratkhas_nn_spm_2) > 0)

        @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && $syaratkhas_nn_spm_1[0]->KODSUBJEK_1 != $syaratkhas_nn_spm_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_spm_nn')
        @endif

        @if((count($syaratkhas_nn_spm_1) == 0 && count($syaratkhas_nn_spm_2) > 0) &&  !empty($syaratkhas_nn_spm_2[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_spm_nn')
        @endif

        @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && ($syaratkhas_nn_spm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_spm_2[0]->KODSUBJEK_1) && ($syaratkhas_nn_spm_1[0]->MINGRED != $syaratkhas_nn_spm_2[0]->MINGRED))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_spm_nn')
        @endif


        {!! $operator200 !!}
        {!! $operator206 !!}
        {!! $operator207 !!}
        {!! $operator215 !!}
    @endif


    {{-- PAPAR BILA KOMBINASI TUNGGAL BERBEZA : UG6314001  --}}
    @if(count($syaratkhas_g2_1) > 0)

        @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && ($syaratkhas_g2_1[0]->KODSUBJEK_1 == $syaratkhas_g2_2[0]->KODSUBJEK_1) && $syaratkhas_g2_1[0]->KET_JUMLAH_MIN_SUBJEK == $syaratkhas_g2_2[0]->KET_JUMLAH_MIN_SUBJEK)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g2')
        @endif

    @endif


    @if(count($syaratkhas_g2_2) > 0)

        @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 != $syaratkhas_g2_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g2')
        @endif

        @if((count($syaratkhas_g2_1) == 0 && count($syaratkhas_g2_2) > 0) && !empty($syaratkhas_g2_2[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g2')
        @endif

        @if((count($syaratkhas_g2_1) > 0 && count($syaratkhas_g2_2) > 0) && $syaratkhas_g2_1[0]->KODSUBJEK_1 == $syaratkhas_g2_2[0]->KODSUBJEK_1 && $syaratkhas_g2_1[0]->KET_JUMLAH_MIN_SUBJEK != $syaratkhas_g2_2[0]->KET_JUMLAH_MIN_SUBJEK)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g2')
        @endif

        {!! $operator201 !!}
        {!! $operator210 !!}
    @endif

    @if(count($syaratkhas_g3_2) > 0)
        @if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) > 0) && $syaratkhas_g3_1[0]->KODSUBJEK_1 == $syaratkhas_g3_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_g3')
        @endif

        @if((count($syaratkhas_g3_1) > 0 && count($syaratkhas_g3_2) > 0) && $syaratkhas_g3_1[0]->KODSUBJEK_1 != $syaratkhas_g3_2[0]->KODSUBJEK_1)
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g3')
        @endif

        @if((count($syaratkhas_g3_1) == 0 && count($syaratkhas_g3_2) > 0) && !empty($syaratkhas_g3_2[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_g3')
        @endif
    @endif

    @if(count($syaratkhas_nn_spm_1) > 0)
        @if((count($syaratkhas_nn_spm_1) > 0 && count($syaratkhas_nn_spm_2) > 0) && ($syaratkhas_nn_spm_1[0]->KODSUBJEK_1 == $syaratkhas_nn_spm_2[0]->KODSUBJEK_1) && ($syaratkhas_nn_spm_1[0]->MINGRED == $syaratkhas_nn_spm_2[0]->MINGRED))
            @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_spm_nn')
        @endif
    @endif

    @if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) > 0 && ($syaratkhas_f1_1[0]->MINGRED == $syaratkhas_f1_2[0]->MINGRED) && ($syaratkhas_f1_1[0]->JUMLAH_MIN_SUBJEK == $syaratkhas_f1_2[0]->JUMLAH_MIN_SUBJEK))
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f1')
    @endif

    @if(count($syaratkhas_f1_1) > 0 && count($syaratkhas_f1_2) > 0 && ($syaratkhas_f1_1[0]->MINGRED == $syaratkhas_f1_2[0]->MINGRED) && ($syaratkhas_f1_1[0]->JUMLAH_MIN_SUBJEK != $syaratkhas_f1_2[0]->JUMLAH_MIN_SUBJEK))
        @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f1')
    @endif

    @if((count($jum_aliran)=='2' && count($syaratkhas_f3_2) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_2) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_2) =='4'))
        @include('programPengajian.cetak_syarat_stpm.k2.syarat_khas_f3_0')
    @endif



    @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) && $syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND && $syarat_muet_1[0]->MUET2_Band != $syarat_muet_2[0]->MUET2_Band)

        @if((count($syarat_1119_1) > 0 && count($syarat_1119_2) > 0) &&  $syarat_1119_1[0]->gred_1119 != $syarat_1119_2[0]->gred_1119 )
            {!! $operator400 !!}
        @endif

        @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) > 0) &&  ($syarat_muet_1[0]->MUET1_BAND != $syarat_muet_2[0]->MUET1_BAND) && (empty($syarat_1119_1[0]->gred_1119) && empty($syarat_1119_2[0]->gred_1119)))
            {!! $operator400 !!}
        @endif

        @include('programPengajian.cetak_syarat_stpm.k2.syarat_muet')
    @endif



    {{-- KUMPULAN 3 --}}
    {{-- ################################################################################################################ --}}

    @if(count($jum_aliran) > 2)
        <p style="text-align:center; padding-top:12px;"><b>ATAU</b></p>
    @endif


    @if(count($syaratkhas_nn_stpm_3) > 0)
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_stpm_nn')
    @endif


    @if(count($syaratkhas_g1_3) > 0 && count($syaratkhas_ga_3) == 0)
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_g1')

        {!! $operator300 !!}
    @endif

    @if(count($syaratkhas_g1_3) == 0 && count($syaratkhas_ga_3) > 0)
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_ga')

        {!! $operator300 !!}
    @endif

    @if(count($syaratkhas_g1_3) > 0 && count($syaratkhas_ga_3) > 0 )
        @if(count($syaratkhas_sk_3) > 0)
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_sk1')
        @endif

        {!! $operator300 !!}
    @endif


    @if(count($syaratkhas_f5_3) > 0)
        @if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5_3) > 0) && !empty($syaratkhas_f5_3[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f5')
        @endif
        {!! $operator302 !!}
    @endif

    @if(count($syaratkhas_f2_3) > 0)
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f2')
        {!! $operator301 !!}
    @endif

    @if(count($syaratkhas_f4_3) > 0)
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f4')
    @endif


    @if(count($syaratkhas_nn_spm_3) > 0)
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_spm_nn')
    @endif


    @if(count($syaratkhas_g2_3) > 0)
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_g2')
        {!! $operator302 !!}
    @endif

    @if(count($syaratkhas_g3_3) > 0)
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_g3')
    @endif


    @if(count($syaratkhas_f1_3) > 0)
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f1')
    @endif

    @if((count($jum_aliran)=='2' && count($syaratkhas_f3_3) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_3) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_3) =='4'))
        @include('programPengajian.cetak_syarat_stpm.k3.syarat_khas_f3_0')
    @endif

    @if((count($jum_aliran) > 1 && count($syaratkhas_f3_1) == '1'))
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f3')
    @endif



    {{-- KUMPULAN 4 --}}
    {{-- ################################################################################################################ --}}

    @if(count($jum_aliran) > 3)
        <p style="text-align:center; padding-top:12px;"><b>ATAU</b></p>
    @endif


    @if(count($syaratkhas_nn_stpm_4) > 0)
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_stpm_nn')
    @endif

    @if(count($syaratkhas_g1_4) > 0 && count($syaratkhas_ga_4) == 0)
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_g1')

        {!! $operator300 !!}
    @endif

    @if(count($syaratkhas_g1_4) == 0 && count($syaratkhas_ga_4) > 0)
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_ga')

        {!! $operator300 !!}
    @endif

    @if(count($syaratkhas_g1_4) > 0 && count($syaratkhas_ga_4) > 0 )
        @if(count($syaratkhas_sk_4) > 0)
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_sk1')
        @endif

        {!! $operator300 !!}
    @endif


    @if(count($syaratkhas_f5_4) > 0)
        @if((count($syaratkhas_f5_1) == 0 && count($syaratkhas_f5_4) > 0) && !empty($syaratkhas_f5_4[0]->KODSUBJEK_1))
            @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f5')
        @endif
        {!! $operator302 !!}
    @endif



    @if(count($syaratkhas_f2_4) > 0)
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f2')


        {!! $operator301 !!}

    @endif

    @if(count($syaratkhas_f4_4) > 0)
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f4')
    @endif


    @if((count($jum_aliran)=='2' && count($syaratkhas_f5_4) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f5_4) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f5_4) =='4'))
        {{-- @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f5_0') --}}
    @endif


    @if(count($syaratkhas_nn_spm_4) > 0)
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_spm_nn')
    @endif


    @if(count($syaratkhas_g2_4) > 0)
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_g2')
        {!! $operator302 !!}
    @endif

    @if(count($syaratkhas_g3_4) > 0)
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_g3')
    @endif


    @if(count($syaratkhas_f1_4) > 0)
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f1')
    @endif

    @if((count($jum_aliran)=='2' && count($syaratkhas_f3_4) =='2') || (count($jum_aliran)=='3' && count($syaratkhas_f3_4) =='3') || (count($jum_aliran)=='4' && count($syaratkhas_f3_4) =='4'))
        @include('programPengajian.cetak_syarat_stpm.k4.syarat_khas_f3_0')
    @endif


    @if((count($jum_aliran) > 1 && count($syaratkhas_f4_1) == '1'))
        @include('programPengajian.cetak_syarat_stpm.k1.syarat_khas_f3')
    @endif


    @if(count($syarat_muet_1) > 0 || count($syarat_muet_2) > 0 || count($syarat_muet_3) > 0 || count($syarat_muet_4) > 0)

        @if((count($syarat_muet_1) > 0 && count($syarat_muet_2) == 0))

            @if((count($syarat_1119_1) > 0 && count($syarat_1119_2) == 0))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_muet')
            @endif

            @if((count($syarat_1119_1) == 0 && count($syarat_1119_2) > 0))
                @include('programPengajian.cetak_syarat_stpm.k2.syarat_muet')
            @endif

            @if((count($syarat_1119_1) == 0 && count($syarat_1119_2) == 0))
                @include('programPengajian.cetak_syarat_stpm.k1.syarat_muet')
            @endif

        @endif


        @if((count($syarat_muet_1) == 0 && count($syarat_muet_2) > 0))
            @include('programPengajian.cetak_syarat_stpm.k2.syarat_muet')
        @endif

    @endif

    @include('programPengajian.cetak_syarat_stpm.syarat_lain')
    @include('programPengajian.cetak_syarat_stpm.syarat_catatan')

    @include('programPengajian.cetak_syarat_stpm.syarat_lain_0')
    @include('programPengajian.cetak_syarat_stpm.syarat_catatan_0')

    </ol>

@endif


@if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F')
    @foreach ($syarat_diploma as $diploma)
        <span style="font-size:1rem !important; font-weight: normal; color: #000;">
            {!! $diploma->SYARAT !!}
        </span>
    @endforeach
@endif

