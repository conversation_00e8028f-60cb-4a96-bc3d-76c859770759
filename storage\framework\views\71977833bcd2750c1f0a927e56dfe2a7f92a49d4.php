<?php $__currentLoopData = $syaratkhas_nn; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $syarat_khas_nn): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php if(substr($syarat_khas_nn->KODSUBJEK_1,0,1)!='K'): ?>
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b><?php echo e($syarat_khas_nn->MINGRED); ?></b> dalam mata pelajaran 
            <b>
                <?php echo e(ucwords(strtolower($syarat_khas_nn->KODSUBJEK_2))); ?>.
            </b>
        </li>
    <?php endif; ?>

    <?php if(substr($syarat_khas_nn->KODSUBJEK_1,0,1)=='K'): ?>
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b><?php echo e($syarat_khas_nn->MINGRED); ?></b> dalam <b><?php echo e($syarat_khas_nn->KET_JUMLAH_MIN_SUBJEK); ?> (<?php echo e($syarat_khas_nn->JUMLAH_MIN_SUBJEK); ?>)</b> mata pelajaran berikut :
            <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
                <table cellpadding="2" width="100%">
                    <tr>
                        <td style="vertical-align:top;">&#8226;</td>
                        <td style="vertical-align:top; width:98%"><?php echo e(ucwords(strtolower($syarat_khas_nn->KODSUBJEK_2))); ?></td>
                    </tr>
                </table>
            </div>        
        </li>
    <?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/cetak_syarat/syarat_khas_nn.blade.php ENDPATH**/ ?>