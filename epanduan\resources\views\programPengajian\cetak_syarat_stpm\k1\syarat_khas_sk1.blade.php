
@foreach ($syaratkhas_sk_1 as $syarat_khas_sk_1)
    @if ($loop->first)
        <li style="padding-left: .3em; margin-bottom:8px;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_khas_sk_1->MINGRED_1}}</b> dalam <b>{{$syarat_khas_sk_1->KET_JUMLAH_MIN_SUBJEK_1}} ({{$syarat_khas_sk_1->JUMLAH_MIN_SUBJEK_1}})</b> mata pelajaran <b>DAN</b> Gred <b>{{$syarat_khas_sk_1->MINGRED_2}}</b> dalam <b>{{$syarat_khas_sk_1->KET_JUMLAH_MIN_SUBJEK_2}} ({{$syarat_khas_sk_1->JUMLAH_MIN_SUBJEK_2}})</b> mata pelajaran di peringkat 
            
            <b>
                @if($print->kategori=='A' || $print->kategori=='S') STPM
                @elseif($print->kategori=='T') STAM
                @elseif($print->kategori=='N') Matrikulasi / Asasi
                @elseif($print->kategori=='P' || $print->kategori=='J') Matrikulasi
                @else Asasi
                @endif
            </b> :
            <div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
                <table cellpadding="2" width="100%">
                    @foreach ($syaratkhas_sk_1 as $syaratkhas_sk1)
                        @if(substr($syaratkhas_sk1->PROGRAMKOD,-3,1)=='1')
                            <tr>
                                <td style="vertical-align:top;">&#8226;</td>
                                <td style="vertical-align:top; width:95%">{{ ucwords(strtolower($syaratkhas_sk1->KODSUBJEK_2)) }}</td>
                            </tr>
                        @endif
                    @endforeach
                </table>
            </div>
        </li>
    @endif
@endforeach




