
@foreach ($syaratkhas_nn_stpm_1 as $syarat_khas_nn_stpm_1)

    @if(substr($syarat_khas_nn_stpm_1->KODSUBJEK_1,0,1)!='K')
        <div style="padding-left: .3em; margin-bottom:8px;"> 
            Mendapat sekurang-kurangnya 
            
            @if($print->kategori=='T')
                @foreach ($codeset_tstam as $tahap_stam)
                    @if($syarat_khas_nn_stpm_1[0]->MINGRED == $tahap_stam->kodthpstam)
                        <b>{{$tahap_stam->ketthpstam}}</b>
                    @endif
                @endforeach
            @else
            Gred <b>{{$syarat_khas_nn_stpm_1->MINGRED}}</b>
            @endif
            
            dalam mata pelajaran
            
            <b>
                {{ucwords(strtolower($syarat_khas_nn_stpm_1->KODSUBJEK_2))}}
            </b>
            di peringkat 
            
            <b>
                @if($print->kategori=='A' || $print->kategori=='S') STPM
                @elseif($print->kategori=='T') STAM
                @elseif($print->kategori=='N') Matrikulasi / Asasi
                @elseif($print->kategori=='P' || $print->kategori=='J') Matrikulasi
                @else Asasi
                @endif
            </b>.
            
        </div>
    @endif

    @if(substr($syarat_khas_nn_stpm_1->KODSUBJEK_1,0,1)=='K')
        <div style="padding-left: .3em; margin-bottom:8px;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_khas_nn_stpm_1->MINGRED}}</b> dalam <b>{{$syarat_khas_nn_stpm_1->KET_JUMLAH_MIN_SUBJEK}} ({{$syarat_khas_nn_stpm_1->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran di peringkat
            
            <b>
                @if($print->kategori=='A' || $print->kategori=='S') STPM
                @elseif($print->kategori=='T') STAM
                @elseif($print->kategori=='N') Matrikulasi / Asasi
                @elseif($print->kategori=='P' || $print->kategori=='J') Matrikulasi
                @else Asasi
                @endif
            </b> :
            <div class="card bg-light text-dark">
                <div class="card-body p-2">
                    <div class="col-md-12">
                        <span  style="display:table-cell;">&#9679;</span>
                        <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_nn_stpm_1->KODSUBJEK_2)) }}</span>
                    </div>
                </div>
            </div>
        </div>
    @endif

@endforeach
