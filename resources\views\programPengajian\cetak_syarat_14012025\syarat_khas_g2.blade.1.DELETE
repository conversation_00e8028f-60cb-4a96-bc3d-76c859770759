

@if(count($syarat_khas_yn_g2) == 1)

    @foreach ($syarat_khas_yn_g2 as  $syarat_g2)
        @php

            $syarat_sub_kumpulan = DB::connection('kpt')->table('upuplus_sub_kumpulan_subjek')->where('kodprogram',$syarat_g2->kodprogram)->where('kategori',$syarat_g2->kategori)->where('kod_group',$syarat_g2->kod_group)->orderby('orderid','ASC')->get();

            if($syarat_g2->jum_subjek=='1') { $read='SATU'; }
            elseif($syarat_g2->jum_subjek=='2') { $read='DUA'; }
            elseif($syarat_g2->jum_subjek=='3') { $read='TIGA'; }
            elseif($syarat_g2->jum_subjek=='4') { $read='EMPAT'; }
            elseif($syarat_g2->jum_subjek=='5') { $read='LIMA'; }
            elseif($syarat_g2->jum_subjek=='6') { $read='ENAM'; }
            elseif($syarat_g2->jum_subjek=='7') { $read='TUJUH'; }
            elseif($syarat_g2->jum_subjek=='8') { $read='LAPAN'; }
            elseif($syarat_g2->jum_subjek=='9') { $read='SEMBILAN'; }
        @endphp


        @if($syarat_g2->kumpulan=='Y' && $syarat_g2->sub_kumpulan=='N')
        <li style="padding-left: .3em;">
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_g2->mingred}}</b> dalam <b>{{$read}} ({{$syarat_g2->jum_subjek}})</b> mata pelajaran berikut :
            <div class="card bg-light text-dark">
                <div class="card-body p-2">

                    @foreach ($syarat_sub_kumpulan as $bil_no => $syarat_kumpulan)

                        @if(substr($syarat_kumpulan->kodsubjek,0,1)!='K')
                            @if($syarat_g2->kodprogram==$syarat_kumpulan->kodprogram)

                                @foreach($sksubjek as  $sk_subjek)
                                    @if($syarat_kumpulan->kodsubjek==$sk_subjek->kodsubjekspm)

                                    <ul style="list-style-type:disc" class="ml-n3">
                                        <li  style="margin-left: -1rem !important;">{{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }}</li>
                                    </ul>
                                    @endif
                                @endforeach     

                            @endif                        
                        @endif

                        @if(substr($syarat_kumpulan->kodsubjek,0,1)=='K')
                            @php
                                $syarat_kesetaraan = DB::connection('kpt')->table('syarat_kesetaraan')->where('PARENT_KUMPULAN_KOD',$syarat_kumpulan->kodsubjek)->orderby('orderid','ASC')->get();
                            @endphp

                            <div style="margin-left:.35rem;">
                                <span style="margin-right: 6px;font-size: 12px;">&#9679;</span>
                                @foreach ($syarat_kesetaraan as $bil_no => $syarat_setara)
                                    @if($syarat_kumpulan->kodsubjek==$syarat_setara->PARENT_KUMPULAN_KOD)                                                                                                                    
                                        @foreach($sksubjek as $sk_subjek)
                                            @if($syarat_setara->KOD==$sk_subjek->kodsubjekspm)
                                            {{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }} @if($bil_no+1 != count($syarat_kesetaraan)) / @endif 
                                            @endif
                                        @endforeach                                                                                              
                                    @endif
                                @endforeach    
                            </div>                                                         
                        @endif 
                    
                    @endforeach
                </div>
            </div>
        </li>
        @endif
    @endforeach

@endif



@if(count($syarat_khas_yn_g2) > 1)

@foreach ($syarat_khas_yn_g2 as  $syarat_g2)
@if ($loop->even)
    @php

        $syarat_sub_kumpulan = DB::connection('kpt')->table('upuplus_sub_kumpulan_subjek')->where('kodprogram',$syarat_g2->kodprogram)->where('kategori',$syarat_g2->kategori)->where('kod_group',$syarat_g2->kod_group)->orderby('orderid','ASC')->get();
        $syarat_khas_g2 = DB::connection('kpt')->table('upuplus_syarat_khas_g2')->where('kodprogram',$syarat_g2->kodprogram)->where('kategori',$syarat_g2->kategori)->where('kod_group','G2')->get();
        $syarat_khas_gb = DB::connection('kpt')->table('upuplus_syarat_khas_g2')->where('kodprogram',$syarat_g2->kodprogram)->where('kategori',$syarat_g2->kategori)->where('kod_group',"GB")->get();

        if($syarat_khas_g2[0]->jum_subjek=='1') { $read_g2='SATU'; }
        elseif($syarat_khas_g2[0]->jum_subjek=='2') { $read_g2='DUA'; }
        elseif($syarat_khas_g2[0]->jum_subjek=='3') { $read_g2='TIGA'; }
        elseif($syarat_khas_g2[0]->jum_subjek=='4') { $read_g2='EMPAT'; }
        elseif($syarat_khas_g2[0]->jum_subjek=='5') { $read_g2='LIMA'; }
        elseif($syarat_khas_g2[0]->jum_subjek=='6') { $read_g2='ENAM'; }
        elseif($syarat_khas_g2[0]->jum_subjek=='7') { $read_g2='TUJUH'; }
        elseif($syarat_khas_g2[0]->jum_subjek=='8') { $read_g2='LAPAN'; }
        elseif($syarat_khas_g2[0]->jum_subjek=='9') { $read_g2='SEMBILAN'; }

        if($syarat_khas_gb[0]->jum_subjek=='1') { $read_gb='SATU'; }
        elseif($syarat_khas_gb[0]->jum_subjek=='2') { $read_gb='DUA'; }
        elseif($syarat_khas_gb[0]->jum_subjek=='3') { $read_gb='TIGA'; }
        elseif($syarat_khas_gb[0]->jum_subjek=='4') { $read_gb='EMPAT'; }
        elseif($syarat_khas_gb[0]->jum_subjek=='5') { $read_gb='LIMA'; }
        elseif($syarat_khas_gb[0]->jum_subjek=='6') { $read_gb='ENAM'; }
        elseif($syarat_khas_gb[0]->jum_subjek=='7') { $read_gb='TUJUH'; }
        elseif($syarat_khas_gb[0]->jum_subjek=='8') { $read_gb='LAPAN'; }
        elseif($syarat_khas_gb[0]->jum_subjek=='9') { $read_gb='SEMBILAN'; }

    @endphp


    @if($syarat_g2->kumpulan=='Y' && $syarat_g2->sub_kumpulan=='N')

        <li style="padding-left: .3em;">

            Mendapat sekurang-kurangnya <b>{{$read_gb}} ({{$syarat_khas_gb[0]->jum_subjek}})</b> Gred <b>{{$syarat_khas_gb[0]->mingred}}</b> <b>dan</b> <b>{{$read_g2}} ({{$syarat_khas_g2[0]->jum_subjek}})</b> Gred <b>{{$syarat_khas_g2[0]->mingred}}</b> dalam mata pelajaran berikut :
            <div class="card bg-light text-dark">
                <div class="card-body p-2">
                    @foreach ($syarat_sub_kumpulan as $bil_no => $syarat_kumpulan)
                        @if(substr($syarat_kumpulan->kodsubjek,0,1)!='K')
                            @if($syarat_g2->kodprogram==$syarat_kumpulan->kodprogram)
                                @foreach($sksubjek as  $sk_subjek)
                                    @if($syarat_kumpulan->kodsubjek==$sk_subjek->kodsubjekspm)
                                    <ul style="list-style-type:disc">
                                        <li style="margin-left: -1rem !important;">{{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }}</li>
                                    </ul>
                                    @endif
                                @endforeach     
                            @endif                        
                        @endif

                        @if(substr($syarat_kumpulan->kodsubjek,0,1)=='K')
                            @php
                                $syarat_kesetaraan = DB::connection('kpt')->table('syarat_kesetaraan')->where('PARENT_KUMPULAN_KOD',$syarat_kumpulan->kodsubjek)->orderby('ORDERID','ASC')->get();
                            @endphp

                            <div style="margin-left: .35rem;">
                                <span style="margin-right: 6px;font-size: 12px;">&#9679;</span>
                                @foreach ($syarat_kesetaraan as $bil_no => $syarat_setara)
                                    @if($syarat_kumpulan->kodsubjek==$syarat_setara->PARENT_KUMPULAN_KOD)                                                                                                                    
                                        @foreach($sksubjek as $sk_subjek)
                                            @if($syarat_setara->KOD==$sk_subjek->kodsubjekspm)
                                            {{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }} @if($bil_no+1 != count($syarat_kesetaraan)) / @endif 
                                            @endif
                                        @endforeach                                                                                              
                                    @endif
                                @endforeach    
                            </div>                                                         
                        @endif 
                    
                    @endforeach
                </div>
            </div>
        </li>


    @endif

@endif
@endforeach



@endif


