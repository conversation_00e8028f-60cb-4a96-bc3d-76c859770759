@foreach ($<PERSON><PERSON>RAI_PROGRAM as $PROGRAM)
    @php
        $syaratkhas_nn = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_nn')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kate<PERSON><PERSON>_Pen<PERSON>jian)
            ->groupby('KODSUBJEK_1')
            ->groupby('JUMLAH_MIN_SUBJEK')
            ->orderby('ORDERID', 'ASC')
            ->get();

        $syaratkhas_valid_g1 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g1')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->ka<PERSON><PERSON><PERSON>_<PERSON>an)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_valid_ga = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_ga')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();

        $syaratkhas_valid_g2 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g1')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_valid_gb = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_ga')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();

        $syaratkhas_valid_g3 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g3')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();

        // JIKA TIADA KUMPULAN
        $syaratkhas_g1 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g1')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1', '2', '3'])
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_g2 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g2')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1', '2', '3'])
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_ga = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_ga')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1', '2', '3'])
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_gb = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_gb')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1', '2', '3'])
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_g3 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g3')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1', '2', '3'])
            ->orderby('ORDERID2', 'ASC')
            ->get();

        // JIKA KUMPULAN 1
        $syaratkhas_k1_g1 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g1')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k1_g2 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g2')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k1_ga = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_ga')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k1_gb = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_gb')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k1_g3 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g3')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();

        // JIKA KUMPULAN 2
        $syaratkhas_k2_g1 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g1')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k2_g2 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g2')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k2_ga = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_ga')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k2_gb = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_gb')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k2_g3 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g3')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();

        // JIKA KUMPULAN 3
        $syaratkhas_k3_g1 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g1')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k3_g2 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g2')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k3_ga = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_ga')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k3_gb = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_gb')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_k3_g3 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_g3')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();

        $syaratkhas_sk1 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_sk1')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_sk2 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_yn_sk2')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();

        $syaratkhas_f1 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_f1')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();

        $syaratkhas_f2 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_f2')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        $syaratkhas_f3 = DB::connection('emas')
            ->table('spm_upuplus_papar_syarat_f3')
            ->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->orderby('ORDERID2', 'ASC')
            ->get();
        // dd($syaratkhas_f1);

        $program = DB::connection('emas')
            ->table('program')
            ->where(DB::raw('substr(KODPROGRAM, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(KODPROGRAM, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->where('STATUS_TAWAR', 'Y')
            ->where('sesi', session()->get('sesiSemasa'))
            ->groupby('KODPROGRAM_PAPAR')
            ->get();
        $syarat_umur = DB::connection('emas')
            ->table('syarat_lain')
            ->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->where('Syarat_Umur', '1')
            ->where('sesi', session()->get('sesiSemasa'))
            ->groupBy(DB::raw('substr(Programkod, 1, 9)'))
            ->get();
        $syarat_kahwin = DB::connection('emas')
            ->table('syarat_lain')
            ->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->where('Syarat_taraf_perkahwinan', '1')
            ->where('sesi', session()->get('sesiSemasa'))
            ->groupBy(DB::raw('substr(Programkod, 1, 9)'))
            ->get();
        $syarat_jantina = DB::connection('emas')
            ->table('syarat_lain')
            ->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->where('Syarat_Jantina', '1')
            ->where('sesi', session()->get('sesiSemasa'))
            ->groupBy(DB::raw('substr(Programkod, 1, 9)'))
            ->get();
        $syarat_3M = DB::connection('emas')
            ->table('syarat_lain')
            ->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(Programkod, 1, 9)'), $PROGRAM->kod_Program)
            ->where(DB::raw('substr(Programkod, -2, 1)'), $PROGRAM->kategori_Pengajian)
            ->where('sesi', session()->get('sesiSemasa'))
            ->groupBy(DB::raw('substr(Programkod, 1, 9)'))
            ->get();

    @endphp
@endforeach

<ol style="padding-left: 2em;" style="list-style-type:decimal;">

    @if (count($syaratkhas_nn) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_nn')
    @endif

    @if (count($syaratkhas_valid_g1) > 0 && count($syaratkhas_valid_ga) == 0)
        @include('programPengajian.cetak_syarat.syarat_khas_g1')
    @endif

    @if (count($syaratkhas_valid_g1) == 0 && count($syaratkhas_valid_ga) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_ga')
    @endif

    @if (count($syaratkhas_valid_g1) > 0 && count($syaratkhas_valid_ga) > 0)
        @if (count($syaratkhas_sk1) > 0)
            @include('programPengajian.cetak_syarat.syarat_khas_sk1')
        @endif
    @endif


    @if (count($syaratkhas_valid_g2) > 0 && count($syaratkhas_valid_gb) == 0)
        @include('programPengajian.cetak_syarat.syarat_khas_g2')
    @endif

    @if (count($syaratkhas_valid_g2) == 0 && count($syaratkhas_valid_gb) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_gb')
    @endif

    @if (count($syaratkhas_valid_g2) > 0 && count($syaratkhas_valid_gb) > 0)
        @if (count($syaratkhas_sk2) > 0)
            @include('programPengajian.cetak_syarat.syarat_khas_sk2')
        @endif
    @endif

    @if (count($syaratkhas_valid_g3) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_g3')
    @endif

    @if (count($syaratkhas_f3) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_f3')
    @endif

    @if (count($syaratkhas_f1) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_f1')
    @endif

    @if (count($syaratkhas_f2) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_f2')
    @endif

    @include('programPengajian.cetak_syarat.syarat_lain')
</ol>
