$(function() {
    var inbox = (function() {
        var messages = [];

        return {
            count: function() {
                return
            },
            add: function(message) {
                messages.push(message)
            },
            clear: function() {
                message = [];
            },
            get: function() {
                return messages;
            }
        };

    })();

    $(".kelayakanMinimum_Modal").on("submit", 'form[data-target]', function(e) {
        e.preventDefault();
        var form = $(this);
        inbox.add(form.serialize())
        form.parent().load(form.attr('action'));
    });

    $(".kelayakanMinimum_Modal").on("show.bs.modal", function(e) {

        var link = $(e.relatedTarget);
        var content = $(this).find(".contentLoad");

        if (content.html()) {
            content.load(link.attr("href"));
        }

    });
});