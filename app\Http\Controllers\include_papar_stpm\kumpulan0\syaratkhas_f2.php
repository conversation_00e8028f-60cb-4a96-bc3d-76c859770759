<?php
$syaratkhas_f2  = DB::CONNECTION('emas')->SELECT("SELECT * FROM
(
    SELECT
    a.PROGRAMKOD AS PROGRAMKOD,
    CASE WHEN SUBSTR(a.PROGRAMKOD, -3, 1) IN ('1', '2', '3', '4') THEN SUBSTR(a.PROGRAMKOD, -3, 1) ELSE 'X' END AS ALIRAN,
          COALESCE(
               (CASE WHEN e.kategori ='T' THEN e.tahap_STAM ELSE NULL END),
        'TIADA'
    ) AS codeset_tstam,

    a.KODSUBJEK AS GKUMPULAN,
    b.<PERSON> AS KODSUBJEK_1,
    d.<PERSON><PERSON><PERSON> AS KODSUBJEK_2,
    a.MINGRED AS MINGRED,
    a.KUMPULAN AS KUMPULAN,
    a.SUB_KUMPULAN AS SUB_KUMPULAN,
    CASE
        WHEN a.JUMLAH_MIN_SUBJEK = '1'
        THEN 'SATU'
        WHEN a.JUMLAH_MIN_SUBJEK = '2'
        THEN 'DUA'
        WHEN a.JUMLAH_MIN_SUBJEK = '3'
        THEN 'TIGA'
        WHEN a.JUMLAH_MIN_SUBJEK = '4'
        THEN 'EMPAT'
        WHEN a.JUMLAH_MIN_SUBJEK = '5'
        THEN 'LIMA'
        WHEN a.JUMLAH_MIN_SUBJEK = '6'
        THEN 'ENAM'
        WHEN a.JUMLAH_MIN_SUBJEK = '7'
        THEN 'TUJUH'
        WHEN a.JUMLAH_MIN_SUBJEK = '8'
        THEN 'LAPAN'
        ELSE 'SEMBILAN'
    END AS KET_JUMLAH_MIN_SUBJEK,
    a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
    a.SESI AS SESI,
    a.ORDERID AS ORDERID,
    b.ORDERID AS ORDERID2
    FROM syarat_khas_stpm a
    LEFT JOIN syarat_xsub_kumpulan_subjek_stpm b ON (a.KODSUBJEK = b.KUMPULAN AND a.SESI = b.SESI)
    LEFT JOIN upuplus_all_subjek d ON (b.KODSUBJEK = d.KOD)
    LEFT JOIN syarat_lain e ON (a.programkod = e.programkod)
    WHERE a.KUMPULAN = 'F2' AND a.SESI = '$sessionSesi'
) AS temp
    WHERE temp.PROGRAMKOD LIKE '$PROGRAM->kod_Program%'
    AND temp.PROGRAMKOD LIKE '%$PROGRAM->kategori_Pengajian)'
    AND SUBSTR(temp.PROGRAMKOD, -3, 1) NOT IN ('1','2','3','4')
    GROUP BY PROGRAMKOD,ALIRAN,codeset_tstam,
    GKUMPULAN,
    KODSUBJEK_1,
    KODSUBJEK_2,
    MINGRED,
    KUMPULAN,
    SUB_KUMPULAN

    ORDER BY ORDERID2 ASC");
