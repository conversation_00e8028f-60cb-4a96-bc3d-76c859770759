<?php

$syaratkhas_sk1  = DB::connection('emas')->select("SELECT * FROM 
(   
    SELECT
        a.PROGRAMKOD AS PROGRAMKOD,
        CASE WHEN SUBSTR(a.PROGRAMKOD, -3, 1) IN ('1', '2', '3', '4') THEN SUBSTR(a.PROGRAMKOD, -3, 1) ELSE 'X' END AS ALIRAN,
        a.GKUMPULAN AS GKUMPULAN,
        a.KODSUBJEK_1 AS KODSUBJEK_1,
        a.KODSUBJEK_2 AS KODSUBJEK_2,
        a.K<PERSON>PULAN AS KUMPULAN,
        a.SUB_KUMPULAN AS SUB_KUMPULAN,
        a.MINGRED AS MINGRED_1,
        a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK_1,
        a.KET_JUMLAH_MIN_SUBJEK AS KET_JUMLAH_MIN_SUBJEK_1,
        b.<PERSON>NGRE<PERSON> AS MINGRED_2,
        b.JUM<PERSON><PERSON>_MIN_SUB<PERSON> AS JUMLAH_MIN_SUBJEK_2,
        b.<PERSON><PERSON>_<PERSON>LAH_MIN_SUBJEK AS KET_JUMLAH_MIN_SUBJEK_2,
        a.SESI AS SESI,
        a.ORDERID AS ORDERID,
        a.ORDERID2 AS ORDERID2
        FROM (
            SELECT
            a.PROGRAMKOD AS PROGRAMKOD,
            CASE
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '1'
                THEN '1'
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '2'
                THEN '2'
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '3'
                THEN '3'
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '4'
                THEN '4'
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '4'
                THEN '4'
                ELSE 'X'
            END AS ALIRAN,
            a.KODSUBJEK AS GKUMPULAN,
            b.KODSUBJEK AS KODSUBJEK_1,
            GROUP_CONCAT(
                d.MATAPELAJARAN
                ORDER BY c.ORDERID ASC SEPARATOR ' / '
            ) AS KODSUBJEK_2,
            a.MINGRED AS MINGRED,
            a.KUMPULAN AS KUMPULAN,
            a.SUB_KUMPULAN AS SUB_KUMPULAN,
            CASE
                WHEN a.JUMLAH_MIN_SUBJEK = '1'
                THEN 'SATU'
                WHEN a.JUMLAH_MIN_SUBJEK = '2'
                THEN 'DUA'
                WHEN a.JUMLAH_MIN_SUBJEK = '3'
                THEN 'TIGA'
                WHEN a.JUMLAH_MIN_SUBJEK = '4'
                THEN 'EMPAT'
                WHEN a.JUMLAH_MIN_SUBJEK = '5'
                THEN 'LIMA'
                WHEN a.JUMLAH_MIN_SUBJEK = '6'
                THEN 'ENAM'
                WHEN a.JUMLAH_MIN_SUBJEK = '7'
                THEN 'TUJUH'
                WHEN a.JUMLAH_MIN_SUBJEK = '8'
                THEN 'LAPAN'
                ELSE 'SEMBILAN'
            END AS KET_JUMLAH_MIN_SUBJEK,
            a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
            a.SESI AS SESI,
            a.ORDERID AS ORDERID,
            b.ORDERID AS ORDERID2
            FROM emas.syarat_khas a
            JOIN emas.syarat_sub_kumpulan_subjek b ON (a.KODSUBJEK = b.KUMPULAN AND a.SESI = b.SESI)
            JOIN emas.syarat_kesetaraan c ON (c.PARENT_KUMPULAN_KOD = b.KODSUBJEK AND a.SESI = c.SESI)
            JOIN (
                -- FIRST JOIN
                SELECT
                upucodeset.refspm_subjek.kodsubjekspm AS KOD,
                upucodeset.refspm_subjek.ketsubjekspm AS MATAPELAJARAN
                FROM
                upucodeset.refspm_subjek
                WHERE upucodeset.refspm_subjek.kodsubjekspm <> '0000'

                UNION ALL

                SELECT
                a.KOD AS KOD,
                a.MATAPELAJARAN AS MATAPELAJARAN
                FROM emas.subjek_kesetaraan a
                JOIN emas.syarat_kesetaraan b ON (a.KOD = b.PARENT_KUMPULAN_KOD)
                JOIN upucodeset.refspm_subjek c ON (b.KOD = c.kodsubjekspm)
                GROUP BY a.KOD

            ) AS d ON (c.KOD = d.KOD)
            WHERE a.KUMPULAN = 'Y'
            AND a.SUB_KUMPULAN = 'N'
            AND SUBSTR(a.KODSUBJEK, 1, 2) = 'G1'
            AND SUBSTR(b.KODSUBJEK, 1, 2) = 'K-'
            AND a.SESI = '$sessionSesi'
            GROUP BY a.PROGRAMKOD,
            b.KODSUBJEK
            
            
            UNION ALL

            SELECT
            a.PROGRAMKOD AS PROGRAMKOD,
            CASE
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '1'
                THEN '1'
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '2'
                THEN '2'
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '3'
                THEN '3'
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '4'
                THEN '4'
                WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '4'
                THEN '4'
                ELSE 'X'
            END AS ALIRAN,
            a.KODSUBJEK AS GKUMPULAN,
            b.KODSUBJEK AS KODSUBJEK_1,
            d.MATAPELAJARAN AS KODSUBJEK_2,
            a.MINGRED AS MINGRED,
            a.KUMPULAN AS KUMPULAN,
            a.SUB_KUMPULAN AS SUB_KUMPULAN,
            CASE
                WHEN a.JUMLAH_MIN_SUBJEK = '1'
                THEN 'SATU'
                WHEN a.JUMLAH_MIN_SUBJEK = '2'
                THEN 'DUA'
                WHEN a.JUMLAH_MIN_SUBJEK = '3'
                THEN 'TIGA'
                WHEN a.JUMLAH_MIN_SUBJEK = '4'
                THEN 'EMPAT'
                WHEN a.JUMLAH_MIN_SUBJEK = '5'
                THEN 'LIMA'
                WHEN a.JUMLAH_MIN_SUBJEK = '6'
                THEN 'ENAM'
                WHEN a.JUMLAH_MIN_SUBJEK = '7'
                THEN 'TUJUH'
                WHEN a.JUMLAH_MIN_SUBJEK = '8'
                THEN 'LAPAN'
                ELSE 'SEMBILAN'
            END AS KET_JUMLAH_MIN_SUBJEK,
            a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
            a.SESI AS SESI,
            a.ORDERID AS ORDERID,
            b.ORDERID AS ORDERID2
            FROM emas.syarat_khas a
            JOIN emas.syarat_sub_kumpulan_subjek b ON (a.KODSUBJEK = b.KUMPULAN AND a.SESI = b.SESI)
            JOIN (
                SELECT
                upucodeset.refspm_subjek.kodsubjekspm AS KOD,
                upucodeset.refspm_subjek.ketsubjekspm AS MATAPELAJARAN
                FROM
                upucodeset.refspm_subjek
                WHERE upucodeset.refspm_subjek.kodsubjekspm <> '0000'

                UNION ALL

                SELECT
                a.KOD AS KOD,
                a.MATAPELAJARAN AS MATAPELAJARAN
                FROM emas.subjek_kesetaraan a
                JOIN emas.syarat_kesetaraan b ON (a.KOD = b.PARENT_KUMPULAN_KOD)
                JOIN upucodeset.refspm_subjek c ON (b.KOD = c.kodsubjekspm)
                GROUP BY a.KOD

            ) AS d ON (b.KODSUBJEK = d.KOD)
            WHERE a.KUMPULAN = 'Y'
            AND a.SUB_KUMPULAN = 'N'
            AND SUBSTR(a.KODSUBJEK, 1, 2) = 'G1'
            AND SUBSTR(b.KODSUBJEK, 1, 2) <> 'K-'
            AND a.SESI = '$sessionSesi'
    ) AS a
    -- SECOND JOIN
    JOIN (
        SELECT
        a.PROGRAMKOD AS PROGRAMKOD,
        CASE
            WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '1'
            THEN '1'
            WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '2'
            THEN '2'
            WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '3'
            THEN '3'
            ELSE 'X'
        END AS ALIRAN,
        a.KODSUBJEK AS GKUMPULAN,
        b.KODSUBJEK AS KODSUBJEK_1,
        GROUP_CONCAT(
            d.MATAPELAJARAN
            ORDER BY c.ORDERID ASC SEPARATOR ' / '
        ) AS KODSUBJEK_2,
        a.MINGRED AS MINGRED,
        a.KUMPULAN AS KUMPULAN,
        a.SUB_KUMPULAN AS SUB_KUMPULAN,
        CASE
            WHEN a.JUMLAH_MIN_SUBJEK = '1'
            THEN 'SATU'
            WHEN a.JUMLAH_MIN_SUBJEK = '2'
            THEN 'DUA'
            WHEN a.JUMLAH_MIN_SUBJEK = '3'
            THEN 'TIGA'
            WHEN a.JUMLAH_MIN_SUBJEK = '4'
            THEN 'EMPAT'
            WHEN a.JUMLAH_MIN_SUBJEK = '5'
            THEN 'LIMA'
            WHEN a.JUMLAH_MIN_SUBJEK = '6'
            THEN 'ENAM'
            WHEN a.JUMLAH_MIN_SUBJEK = '7'
            THEN 'TUJUH'
            WHEN a.JUMLAH_MIN_SUBJEK = '8'
            THEN 'LAPAN'
            ELSE 'SEMBILAN'
        END AS KET_JUMLAH_MIN_SUBJEK,
        a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
        a.SESI AS SESI,
        a.ORDERID AS ORDERID,
        b.ORDERID AS ORDERID2
        FROM emas.syarat_khas a
        JOIN emas.syarat_sub_kumpulan_subjek b ON (a.KODSUBJEK = b.KUMPULAN AND a.SESI = b.SESI)
        JOIN emas.syarat_kesetaraan c ON (c.PARENT_KUMPULAN_KOD = b.KODSUBJEK AND a.SESI = c.SESI)
        JOIN (
            SELECT
            upucodeset.refspm_subjek.kodsubjekspm AS KOD,
            upucodeset.refspm_subjek.ketsubjekspm AS MATAPELAJARAN
            FROM
            upucodeset.refspm_subjek
            WHERE upucodeset.refspm_subjek.kodsubjekspm <> '0000'

            UNION ALL

            SELECT
            a.KOD AS KOD,
            a.MATAPELAJARAN AS MATAPELAJARAN
            FROM emas.subjek_kesetaraan a
            JOIN emas.syarat_kesetaraan b ON (a.KOD = b.PARENT_KUMPULAN_KOD)
            JOIN upucodeset.refspm_subjek c ON (b.KOD = c.kodsubjekspm)
            GROUP BY a.KOD

        ) AS d ON (c.KOD = d.KOD)
        WHERE a.KUMPULAN = 'Y'
        AND a.SUB_KUMPULAN = 'N'
        AND SUBSTR(a.KODSUBJEK, 1, 2) = 'GA'
        AND SUBSTR(b.KODSUBJEK, 1, 1) = 'K'
        AND a.SESI = '$sessionSesi'
        GROUP BY a.PROGRAMKOD,
        b.KODSUBJEK
        
        UNION ALL

        SELECT
        a.PROGRAMKOD AS PROGRAMKOD,
        CASE
            WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '1'
            THEN '1'
            WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '2'
            THEN '2'
            WHEN SUBSTR(a.PROGRAMKOD, - 3, 1) = '3'
            THEN '3'
            ELSE 'X'
        END AS ALIRAN,
        a.KODSUBJEK AS GKUMPULAN,
        b.KODSUBJEK AS KODSUBJEK_1,
        d.MATAPELAJARAN AS KODSUBJEK_2,
        a.MINGRED AS MINGRED,
        a.KUMPULAN AS KUMPULAN,
        a.SUB_KUMPULAN AS SUB_KUMPULAN,
        CASE
            WHEN a.JUMLAH_MIN_SUBJEK = '1'
            THEN 'SATU'
            WHEN a.JUMLAH_MIN_SUBJEK = '2'
            THEN 'DUA'
            WHEN a.JUMLAH_MIN_SUBJEK = '3'
            THEN 'TIGA'
            WHEN a.JUMLAH_MIN_SUBJEK = '4'
            THEN 'EMPAT'
            WHEN a.JUMLAH_MIN_SUBJEK = '5'
            THEN 'LIMA'
            WHEN a.JUMLAH_MIN_SUBJEK = '6'
            THEN 'ENAM'
            WHEN a.JUMLAH_MIN_SUBJEK = '7'
            THEN 'TUJUH'
            WHEN a.JUMLAH_MIN_SUBJEK = '8'
            THEN 'LAPAN'
            ELSE 'SEMBILAN'
        END AS KET_JUMLAH_MIN_SUBJEK,
        a.JUMLAH_MIN_SUBJEK AS JUMLAH_MIN_SUBJEK,
        a.SESI AS SESI,
        a.ORDERID AS ORDERID,
        b.ORDERID AS ORDERID2
        FROM emas.syarat_khas a 
        JOIN emas.syarat_sub_kumpulan_subjek b ON (a.KODSUBJEK = b.KUMPULAN AND a.SESI = b.SESI)
        JOIN (
            SELECT
            upucodeset.refspm_subjek.kodsubjekspm AS KOD,
            upucodeset.refspm_subjek.ketsubjekspm AS MATAPELAJARAN
            FROM
            upucodeset.refspm_subjek
            WHERE upucodeset.refspm_subjek.kodsubjekspm <> '0000'

            UNION ALL

            SELECT
            a.KOD AS KOD,
            a.MATAPELAJARAN AS MATAPELAJARAN
            FROM emas.subjek_kesetaraan a
            JOIN emas.syarat_kesetaraan b ON (a.KOD = b.PARENT_KUMPULAN_KOD)
            JOIN upucodeset.refspm_subjek c ON (b.KOD = c.kodsubjekspm)
            GROUP BY a.KOD

        ) AS d ON (b.KODSUBJEK = d.KOD)  
        WHERE a.KUMPULAN = 'Y'
        AND a.SUB_KUMPULAN = 'N'
        AND SUBSTR(a.KODSUBJEK, 1, 2) = 'GA'
        AND SUBSTR(b.KODSUBJEK, 1, 1) <> 'K'
        AND a.SESI = '$sessionSesi'	   
    ) AS b ON (a.PROGRAMKOD = b.PROGRAMKOD AND a.KODSUBJEK_1 = b.KODSUBJEK_1 AND a.SESI = b.SESI) WHERE a.SESI = '$sessionSesi'
) AS temp 
WHERE PROGRAMKOD LIKE '$PROGRAM->kod_Program%' 
AND SUBSTR(PROGRAMKOD, -2, 1)='$PROGRAM->kategori_Pengajian' 
ORDER BY ORDERID2 ASC");


