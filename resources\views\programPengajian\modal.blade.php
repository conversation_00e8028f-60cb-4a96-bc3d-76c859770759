<!-- Modal Maklumat Program Pengajian -->
<div class="modal fade" id="maklumatProgram__{{ $PROGRAM->kod_Program }}" tabindex="-1" role="dialog"
    aria-labelledby="login-title" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
            <div class="modal-body p-0">
                <div class="container-fluid py-5">
                    <h4 class="text-center">Maklumat Program</h4>
                    <div class="row align-items-center no-gutters">
                        @foreach ($MAKLUMAT_PENGAJIAN as $maklumat_Pengajian)
                            @if ($maklumat_Pengajian->kod_Program == $PROGRAM->kod_Program)
                                <div class="col-lg-6 col-md-5">
                                    <form class="login-form p-4">
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-graduation-cap"
                                                style="color: #4579E7"></span>
                                            Peringkat Pengajian :
                                            <b>{{ Str::title($maklumat_Pengajian->keterangan_peringkat) }}</b>
                                        </p>
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-tags"
                                                style="color: #4579E7"></span> Mod
                                            Pengajian :
                                            @if ($maklumat_Pengajian->mod2u2i == 'Y')
                                                <b>2U2I/3U1I</b>
                                            @else
                                                <b>Konvensional</b>
                                            @endif
                                        </p>
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-hourglass-end"
                                                style="color: #4579E7"></span>
                                            Tempoh Pengajian :
											<b>
											{{ Str::title($maklumat_Pengajian->tempoh_Pengajian) }} 
											@if($maklumat_Pengajian->tempoh=='01') Semester @endif
											@if($maklumat_Pengajian->tempoh=='02') Tahun @endif
											@if($maklumat_Pengajian->tempoh=='03') Bulan @endif
											</b>
                                        </p>
                                        {{-- @if (session()->get('jenprog') == 'stpm')
                                            <p class="para-desc text-muted mb-1">Minimum PNGK : </p>
                                            <p class="para-desc text-muted mb-1">Minimum MUET : </p>
                                        @endif --}}
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-sitemap"
                                                style="color: #4579E7"></span> Bidang
                                            NEC :
                                            <b>{{ Str::title($maklumat_Pengajian->bidang_NEC) }}
                                                {{ Str::title($maklumat_Pengajian->keterangan_bidangNEC) }}</b>
                                        </p>
                                    </form>
                                </div>
                                <!--end col-->

                                <div class="col-lg-6 col-md-7">
                                    <form class="login-form p-4">
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-hashtag"
                                                style="color: #4579E7"></span> Bertemu
                                            duga / Ujian:
                                            @if ($maklumat_Pengajian->program_Temuduga == 'Y')
                                                <b>Ya</b>
                                            @else
                                                <b>Tidak</b>
                                            @endif
                                        </p>
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-certificate"
                                                style="color: #4579E7"></span>
                                            Joint / Dual / Double Degree :
                                            @if ($maklumat_Pengajian->double_Degree == 'Y')
                                                <b>Ya</b>
                                            @else
                                                <b>Tidak</b>
                                            @endif
                                        </p>
										
										@if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F')
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-bar-chart"
                                                style="color: #4579E7"></span>
                                            Purata Markah Merit:

                                            @if($maklumat_Pengajian->kategori_Pengajian == 'T')
                                            <b>@if($maklumat_Pengajian->merit_Program=='') Tiada (Program Baru) @else {!! $maklumat_Pengajian->merit_Program !!}% @endif</b>
                                            @endif

                                            @if($maklumat_Pengajian->kategori_Pengajian != 'T')
                                            <b>@if($maklumat_Pengajian->merit_Program=='') Tiada (Program Baru) @else {!! $maklumat_Pengajian->merit_Program !!}% @endif</b>
                                            @endif

                                        </p>
										@endif
										
                                        <p class="para-desc text-muted mb-1"><span class="fa fa-sticky-note"
                                                style="color: #4579E7"></span>
                                            Catatan:
                                            @if (is_null($maklumat_Pengajian->catatan_Program) || empty($maklumat_Pengajian->catatan_Program))
                                                <b>Tidak Berkenaan</b>
                                            @else
                                                <b>{!! $maklumat_Pengajian->catatan_Program !!}</b>
                                            @endif
                                        </p>
                                    </form>
                                </div>
                                <!--end col-->
                            @endif
                        @endforeach
                    </div>
					
					@if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F')
					<div style="color:red;">Penafian : Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata. Kejayaan mendapat tawaran bergantung kepada kedudukan merit, memenuhi syarat am dan syarat khas, bilangan tempat yang disediakan dan lulus temu duga dan/atau ujian bagi program pengajian yang menetapkan keperluan berkenaan.</div>
                    @endif
					
					<!--end row-->
                </div>
                <!--end container-->
            </div>
        </div>
    </div>
</div>

<!-- Modal Kampus @ Pusat Latihan-->
@if (substr($PROGRAM->kod_Program, 0, 1) === 'U')
    <div class="modal fade" id="kampus__{{ $PROGRAM->kod_Program }}" tabindex="-1" role="dialog"
        aria-labelledby="kampus-title" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
                <div class="modal-body py-5">
                    <div class="text-left">
                        <h4 class="text-center">Kampus</h4>
                        @foreach ($SENARAI_KAMPUS as $Kampus)
                            @if ($Kampus->KOD == $PROGRAM->kod_Program)
                                <b class="text-muted"> <br>- {{ Str::title($Kampus->lokasiKampus) }},
                                    {!! Str::title($Kampus->negeri) !!} </b>
                            @endif
                        @endforeach
                    </div>

                    @if (session()->get('jenprog') == 'spm' && substr($PROGRAM->kod_Program, 0, 1) != 'U')
                        <div class="text-left" style="padding-top: 3rem; margin-bottom: -2rem">
                            <p class="para-desc text-muted mb-1"><b>Rujukan Fasiliti di Politeknik, Kolej Komuniti &
                                    ILKA
                                    Sahaja</b></p>
                            <p class="para-desc text-muted mb-1" style="text-indent: 0.3rem"><span
                                    class="mdi mdi-bed-empty" style="color: black"></span> Asrama disediakan</p>

                            <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                    class="mdi mdi-gender-male" style="color: #4579E7"></span> Hanya Asrama lelaki
                                disediakan</p>

                            <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                    class="mdi mdi-gender-female" style="color: #E775BF"></span> Hanya Asrama perempuan
                                disediakan
                            </p>

                            <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                    class="mdi mdi-wheelchair-accessibility" style="color: #4579E7"></span> Kampus mesra
                                OKU
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@else
    <div class="modal fade" id="kampus__{{ $PROGRAM->kod_Program }}" tabindex="-1" role="dialog"
        aria-labelledby="kampus-title" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
                <div class="modal-body py-5">
                    <div class="text-left">
                        <h4 class="text-center">Kampus</h4>
                        @foreach ($PUSAT_LATIHAN as $pusatLatihan)
                            @if ($pusatLatihan->kodkursus == $PROGRAM->kod_Program)
                                <b class="text-muted"> <br>- {{ Str::title($pusatLatihan->ketpusat) }} </b>
                                @if ($pusatLatihan->asrama == 'Y')
                                    <span class="mdi mdi-bed-empty"></span>
                                @endif

                                @if ($pusatLatihan->jantinakhas == 'L')
                                    <span class="mdi mdi-gender-male" style="color: #4579E7"></span>
                                @elseif($pusatLatihan->jantinakhas == 'P')
                                    <span class="mdi mdi-gender-female" style="color: #E775BF"></span>
                                @endif

                                @if ($pusatLatihan->mesraoku == 'Y')
                                    <span class="mdi mdi-wheelchair-accessibility" style="color: #4579E7"></span>
                                @endif
                            @endif
                        @endforeach
                    </div>
                    <div class="text-left" style="padding-top: 3rem; margin-bottom: -2rem">
                        <p class="para-desc text-muted mb-1"><b>Rujukan Fasiliti di Politeknik, Kolej Komuniti & ILKA
                                Sahaja</b></p>
                        <p class="para-desc text-muted mb-1" style="text-indent: 0.3rem"><span class="mdi mdi-bed-empty"
                                style="color: black"></span> Asrama disediakan</p>

                        <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                class="mdi mdi-gender-male" style="color: #4579E7"></span> Hanya Asrama lelaki
                            disediakan
                        </p>

                        <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                class="mdi mdi-gender-female" style="color: #E775BF"></span> Hanya Asrama perempuan
                            disediakan</p>

                        <p class="para-desc text-muted mb-1" style="margin-top: -0.6rem; text-indent: 0.3rem"><span
                                class="mdi mdi-wheelchair-accessibility" style="color: #4579E7"></span> Kampus mesra
                            OKU</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif

<!-- Modal Yuran Pengajian-->
<div class="modal fade" id="yuran-pengajian" tabindex="-1" role="dialog" aria-labelledby="yuran-pengajian-title"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
            <div class="modal-body py-5">
                <div class="text-left">
                    <h4 class="text-center">Yuran Pengajian</h4>
                    <p class="text-muted" style="text-align: left">
					<b class="text-muted">	
					Yuran Pengajian adalah ditetapkan oleh pihak UA/IPTA. Maklumat yuran akan dipaparkan di dalam Surat Tawaran Rasmi yang akan dikeluarkan oleh UA/IPTA masing-masing.
					{{-- {!! $PROGRAM->yuran_Pengajian !!} --}}							
					</b>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Syarat Program Pengajian -->
{{-- @if (session()->get('jenprog') == 'stpm')
    <div class="modal fade" id="kelayakanMinimum_Modal{{ $PROGRAM->kod_Program }}" tabindex="-1" role="dialog"
        aria-labelledby="syarat-program-title" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document" style="max-width: 630px">
            <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
                <button type="button" class="close float-right mr-2" data-dismiss="modal" aria-label="Close"
                    style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-body">
                    <div class="text-left">
                        <h4 class="text-center"><b>Syarat Program</b></h4>
                        <div class="container mt-100 mt-60">
                            <div class="row">
                                <div class="col-12">
                                    <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                        id="pills-tab" role="tablist">
                                        @if (substr($PROGRAM->bidang_NEC, 0, 2) == '01')
                                            <!--Syarat Am Tab-->
                                            <li class="nav-item m-1">
                                                <a class="nav-link py-2 px-5 active rounded"
                                                    id="syarat-am-tab-{{ $PROGRAM->kod_Program }}" data-toggle="pill"
                                                    href="#syarat-am-{{ $PROGRAM->kod_Program }}" role="tab"
                                                    aria-controls="syarat-am" aria-selected="false">
                                                    <div class="text-center">
                                                        <h6 class="mb-0">Syarat Am</h6>
                                                    </div>
                                                </a>
                                            </li>

                                            <!--Syarat Am Pendidikan Tab-->
                                            @if (substr($PROGRAM->bidang_NEC, 0, 2) == '01')
                                                <li class="nav-item m-1">
                                                    <a class="nav-link py-2 px-5 rounded"
                                                        id="syarat-am-pendidikan-tab-{{ $PROGRAM->kod_Program }}"
                                                        data-toggle="pill"
                                                        href="#syarat-am-pendidikan-{{ $PROGRAM->kod_Program }}"
                                                        role="tab" aria-controls="syarat-am-pendidikan"
                                                        aria-selected="false">
                                                        <div class="text-center">
                                                            <h6 class="mb-0">Syarat Am Pendidikan</h6>
                                                        </div>
                                                    </a>
                                                    <!--end nav link-->
                                                </li>
                                            @endif

                                            <!--Syarat Khas Tab-->
                                            <li class="nav-item m-1">
                                                <a class="nav-link py-2 px-5 rounded"
                                                    id="syarat-Khas-tab-{{ $PROGRAM->kod_Program }}"
                                                    data-toggle="pill"
                                                    href="#syarat-Khas-{{ $PROGRAM->kod_Program }}" role="tab"
                                                    aria-controls="syarat-Khas" aria-selected="false">
                                                    <div class="text-center">
                                                        <h6 class="mb-0">Syarat Khas</h6>
                                                    </div>
                                                </a>
                                            </li>
                                        @else
                                            <!--Syarat Am Tab-->
                                            <li class="nav-item m-1">
                                                <a class="nav-link py-2 active rounded"
                                                    id="syarat-am-tab-{{ $PROGRAM->kod_Program }}" data-toggle="pill"
                                                    href="#syarat-am-{{ $PROGRAM->kod_Program }}" role="tab"
                                                    aria-controls="syarat-am" aria-selected="false"
                                                    style="padding-left: 5.9rem; padding-right: 5.9rem">
                                                    <div class="text-center">
                                                        <h6 class="mb-0">Syarat Am</h6>
                                                    </div>
                                                </a>
                                            </li>

                                            <!--Syarat Khas Tab-->
                                            <li class="nav-item m-1">
                                                <a class="nav-link py-2 rounded"
                                                    id="syarat-Khas-tab-{{ $PROGRAM->kod_Program }}"
                                                    data-toggle="pill"
                                                    href="#syarat-Khas-{{ $PROGRAM->kod_Program }}" role="tab"
                                                    aria-controls="syarat-Khas" aria-selected="false"
                                                    style="padding-left: 5.9rem; padding-right: 5.9rem">
                                                    <div class="text-center">
                                                        <h6 class="mb-0">Syarat Khas</h6>
                                                    </div>
                                                </a>
                                            </li>
                                        @endif
                                    </ul>

                                    <div class="tab-content" id="pills-tabContent"
                                        style="padding-top: 2rem!important">
                                        <!--Paparan Syarat Am Tab-->
                                        <div class="card border-0 tab-pane fade show active"
                                            id="syarat-am-{{ $PROGRAM->kod_Program }}" role="tabpanel"
                                            aria-labelledby="syarat-am-tab-{{ $PROGRAM->kod_Program }}">
                                            @foreach ($SYARAT_AM as $syaratAM_STPM)
                                                @if (Request::route('kodkatag') &&
                                                        in_array(Request::route('kodkatag'), ['S', 'A']) &&
                                                        $syaratAM_STPM->USA_LEPASAN == 'stpm')
                                                    <b class="text-muted mb-0">{!! $syaratAM_STPM->USA_SYARAT !!}</b>
                                                @elseif(Request::route('kodkatag') &&
                                                        in_array(Request::route('kodkatag'), ['N', 'P', 'L', 'U', 'K', 'J']) &&
                                                        $syaratAM_STPM->USA_LEPASAN == 'matrik')
                                                    <b class="text-muted mb-0">{!! $syaratAM_STPM->USA_SYARAT !!}</b>
                                                @elseif(Request::route('kodkatag') && in_array(Request::route('kodkatag'), ['T']) && $syaratAM_STPM->USA_LEPASAN == 'stam')
                                                    <b class="text-muted mb-0">{!! $syaratAM_STPM->USA_SYARAT !!}</b>
                                                @elseif(Request::route('kodkatag') &&
                                                        in_array(Request::route('kodkatag'), ['G1', 'G2']) &&
                                                        $syaratAM_STPM->USA_LEPASAN == 'dipuapoli')
                                                    <b class="text-muted mb-0">{!! $syaratAM_STPM->USA_SYARAT !!}</b>
                                                @elseif(Request::route('kodkatag') &&
                                                        in_array(Request::route('kodkatag'), ['E1', 'E2', 'E3']) &&
                                                        $syaratAM_STPM->USA_LEPASAN == 'diplain')
                                                    <b class="text-muted mb-0">{!! $syaratAM_STPM->USA_SYARAT !!}</b>
                                                @elseif(Request::route('kodkatag') &&
                                                        in_array(Request::route('kodkatag'), ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8']) &&
                                                        $syaratAM_STPM->USA_LEPASAN == 'dipsetaraf')
                                                    <b class="text-muted mb-0">{!! $syaratAM_STPM->USA_SYARAT !!}</b>
                                                @endif
                                            @endforeach
                                        </div>

                                        <!--Paparan Syarat Am Pendidikan Tab-->
                                        @foreach ($SYARAT_AM as $syaratAM_STPM)
                                            @if (substr($PROGRAM->bidang_NEC, 0, 2) == '01' && $syaratAM_STPM->USA_LEPASAN == 'pendidikan')
                                                <div class="card border-0 tab-pane fade"
                                                    id="syarat-am-pendidikan-{{ $PROGRAM->kod_Program }}"
                                                    role="tabpanel"
                                                    aria-labelledby="syarat-am-pendidikan-tab-{{ $PROGRAM->kod_Program }}">
                                                    <b class="text-muted mb-0">{!! $syaratAM_STPM->USA_SYARAT !!}</b>
                                                </div>
                                            @endif
                                        @endforeach

                                        <!--Paparan Syarat Khas Tab-->
                                        <div class="card border-0 tab-pane fade"
                                            id="syarat-Khas-{{ $PROGRAM->kod_Program }}" role="tabpanel"
                                            aria-labelledby="syarat-Khas-tab-{{ $PROGRAM->kod_Program }}">
                                            <b class="text-muted text-left">
                                                @foreach ($SYARAT_PENGAJIAN as $syarat)
                                                    @if ($syarat->kod_Program == $PROGRAM->kod_Program)
                                                        <b class="text-muted mb-0">{!! $syarat->syarat_KhasProgram !!}</b>
                                                    @endif
                                                @endforeach
                                            </b>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif --}}

<!-- Modal Laluan Kerjaya-->
<div class="modal fade" id="laluan-kerjaya_{{ $PROGRAM->kod_Program }}" tabindex="-1" role="dialog"
    aria-labelledby="yuran-pengajian-title" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
            <div class="modal-body py-5">
                <div class="text-left">
                    <h4 class="text-center">Laluan Kerjaya</h4>
                    <p class="text-muted" style="text-align: left"><br>
                        @foreach ($LALUAN_KERJAYA as $laluanKerjaya)
                            @if ($laluanKerjaya->kod_Kerjaya == $PROGRAM->kod_Program)
                                <b class="text-muted mb-0">{!! $laluanKerjaya->program_Kerjaya !!}</b>
                            @endif
                        @endforeach
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@if (session()->get('jenprog') == 'stpm')
<!-- Modal Bidang NEC-->
<div class="modal fade" id="bidangNEC_{{ $PROGRAM->kod_Program }}" tabindex="-1" role="dialog"
    aria-labelledby="yuran-pengajian-title" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content rounded shadow-lg border-0 overflow-hidden">
            <div class="modal-body py-5">
                <div class="text-left">
                    <h5 class="text-center">Bidang NEC yang layak dipertimbangkan</h5>
                    <p class="text-muted" style="text-align: left"><br>

                        <?php $showErrorMessage = 0; ?>
                        @foreach ($BIDANG_NEC as $bidang_nec)
                            @if ($bidang_nec->kod_program == $PROGRAM->kod_Program && $bidang_nec->jensetaraf == Request::route('kodkatag'))
                                @foreach ($CODESET_NEC as $nec)
                                    @if($bidang_nec->bidangnec == $nec->kod)
                                        <?php $showErrorMessage = 1;?>
                                        <b class="text-muted mb-0">{{ $nec->kod }}</b>  - {{$nec->ket_bidang_bm}} <br>
                                    @endif
                                @endforeach
                            @endif
                        @endforeach

                        @if($showErrorMessage == 0)
                         {{'TAWAR SEMUA BIDANG'}}
                        @endif

                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endif