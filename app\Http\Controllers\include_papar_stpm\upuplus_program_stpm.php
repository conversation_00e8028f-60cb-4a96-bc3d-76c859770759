<?php
$cetak = Cache::remember('stpm_cetak_program_stpm_'. Auth::user()->user_id, now()->addMinutes(10), function () use ($kat,$sessionSesi) {
    return DB::connection('emas')->table('program_stpm')
    ->select(
        'KODPROGRAM_PAPAR AS kodprogram',
        'KATEGORI AS kategori',
        'JENSETARAF AS jensetaraf',
        DB::raw("CONCAT(KODPROGRAM_PAPAR, '(', KATEGORI, ')') AS kodprogram2"),
        'LEPASAN AS lepasan',
        'NAMAPROGRAM AS program',
        'KODUNIVERSITI AS kodipta',
        'BILSEMESTER AS semester',
        'JENISTEMPOH AS tempoh',
        'YURAN AS yuran',
        'OKU AS status_oku',
        'BUMI AS bumiputera',
        'TEMUDUGA AS tduga',
        'STATUS_TAWAR AS status_tawar',
        'STATUS_RAYU AS status_rayu',
        'JENIS_PENGAJIAN AS jenis_pengajian',
        'REMARKS AS catatan'
    )
    ->where('sesi', '=', $sessionSesi)
    ->where('STATUS_TAWAR', '=', 'Y')
    ->whereIn('kategori', $kat)
    ->where('lepasan', session()->get('login_jenprog'))
    ->when((in_array(Auth::user()->ipta, ['11', '22'])) && session()->get('ipta') != '', function ($q) {
        return $q->where('KODPROGRAM_PAPAR', 'like', session()->get('ipta').'%')
                ->where('KODPROGRAM_PAPAR', 'like', session()->get('program').'%');
    })
    ->when((in_array(Auth::user()->ipta, ['11', '22'])) && session()->get('ipta') == '', function ($q) {
        return $q->where('KODPROGRAM_PAPAR', 'like' , 'UA%')
                ->where('KODPROGRAM_PAPAR', 'like', session()->get('program').'%');
    }) 
    ->when(!in_array(Auth::user()->ipta, ['11','22']), function ($q) {
        return $q->where('KODPROGRAM_PAPAR', Auth::user()->ipta.'%')
                ->where('KODPROGRAM_PAPAR', 'like', session()->get('program').'%');
    })
    ->orderby('KODPROGRAM_PAPAR', 'ASC')
    ->orderby('KATEGORI', 'ASC')
    ->groupby('KODPROGRAM_PAPAR', 'KATEGORI', 'JENSETARAF') // Grouping by both kodprogram and kategori
    ->get();
});

